<?php
/**
 * Emergency Database Fix
 * SC_IMS Sports Competition and Event Management System
 * 
 * Comprehensive fix for both tournament_structure_id column error and format inconsistency
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Emergency Database Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Database Fix</h1>
        <p><strong>Fix Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="error">
            <h3>🎯 Critical Issues Being Fixed</h3>
            <p><strong>1. Persistent Database Error:</strong> tournament_structure_id column not found</p>
            <p><strong>2. Tournament Format Inconsistency:</strong> Round Robin vs Single Elimination mismatch</p>
        </div>
        
        <?php
        $overall_success = true;
        $changes_made = [];
        $errors = [];
        
        try {
            // Disable foreign key checks temporarily
            $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Step 1: Fix matches table schema
            echo '<div class="step">';
            echo '<h2>Step 1: Fix Matches Table Schema</h2>';
            
            // Check current matches table structure
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $required_columns = [
                'tournament_structure_id' => 'INT NULL COMMENT "Tournament structure reference"',
                'tournament_round_id' => 'INT NULL COMMENT "Tournament round reference"',
                'bracket_position' => 'VARCHAR(50) NULL COMMENT "Position in tournament bracket"',
                'is_bye_match' => 'BOOLEAN DEFAULT FALSE COMMENT "Whether this is a bye match"'
            ];
            
            foreach ($required_columns as $column => $definition) {
                if (!in_array($column, $columns)) {
                    try {
                        $sql = "ALTER TABLE matches ADD COLUMN $column $definition";
                        $conn->exec($sql);
                        echo '<p>✅ Added column: ' . $column . '</p>';
                        $changes_made[] = "Added $column column to matches table";
                    } catch (Exception $e) {
                        echo '<p>❌ Failed to add column ' . $column . ': ' . htmlspecialchars($e->getMessage()) . '</p>';
                        $errors[] = "Failed to add column $column: " . $e->getMessage();
                        $overall_success = false;
                    }
                } else {
                    echo '<p>✓ Column ' . $column . ' already exists</p>';
                }
            }
            
            echo '</div>';
            
            // Step 2: Ensure tournament tables exist
            echo '<div class="step">';
            echo '<h2>Step 2: Ensure Tournament Tables Exist</h2>';
            
            // Check and create tournament_formats table
            $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
            $stmt->execute();
            if (!$stmt->fetch()) {
                $sql = "CREATE TABLE tournament_formats (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    code VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    sport_types VARCHAR(255) DEFAULT 'team,individual',
                    min_participants INT DEFAULT 2,
                    max_participants INT NULL,
                    algorithm_class VARCHAR(100) DEFAULT 'SingleEliminationAlgorithm',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_sport_types (sport_types),
                    INDEX idx_code (code)
                )";
                $conn->exec($sql);
                echo '<p>✅ Created tournament_formats table</p>';
                $changes_made[] = "Created tournament_formats table";
            } else {
                echo '<p>✓ tournament_formats table already exists</p>';
            }
            
            // Check and create tournament_structures table
            $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_structures'");
            $stmt->execute();
            if (!$stmt->fetch()) {
                $sql = "CREATE TABLE tournament_structures (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    event_sport_id INT NOT NULL,
                    tournament_format_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                    participant_count INT DEFAULT 0,
                    total_rounds INT DEFAULT 0,
                    current_round INT DEFAULT 0,
                    seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
                    bracket_data JSON,
                    advancement_rules JSON,
                    scoring_config JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                    FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE CASCADE
                )";
                $conn->exec($sql);
                echo '<p>✅ Created tournament_structures table</p>';
                $changes_made[] = "Created tournament_structures table";
            } else {
                echo '<p>✓ tournament_structures table already exists</p>';
            }
            
            echo '</div>';
            
            // Step 3: Insert default tournament formats
            echo '<div class="step">';
            echo '<h2>Step 3: Ensure Default Tournament Formats</h2>';
            
            $default_formats = [
                ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null, 'SingleEliminationAlgorithm'],
                ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null, 'DoubleEliminationAlgorithm'],
                ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16, 'RoundRobinAlgorithm'],
                ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null, 'SwissSystemAlgorithm'],
                ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null, 'JudgedRoundsAlgorithm'],
                ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null, 'MultiStageAlgorithm']
            ];
            
            foreach ($default_formats as $format) {
                try {
                    $stmt = $conn->prepare("
                        INSERT IGNORE INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants, algorithm_class)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute($format);
                    if ($stmt->rowCount() > 0) {
                        echo '<p>✅ Inserted format: ' . $format[0] . '</p>';
                        $changes_made[] = "Inserted tournament format: " . $format[0];
                    } else {
                        echo '<p>✓ Format already exists: ' . $format[0] . '</p>';
                    }
                } catch (Exception $e) {
                    echo '<p>❌ Failed to insert format ' . $format[0] . ': ' . htmlspecialchars($e->getMessage()) . '</p>';
                    $errors[] = "Failed to insert format " . $format[0] . ": " . $e->getMessage();
                }
            }
            
            echo '</div>';
            
            // Step 4: Fix tournament format inconsistency
            echo '<div class="step">';
            echo '<h2>Step 4: Fix Tournament Format Inconsistency</h2>';
            
            // Check sport categories for format inconsistencies
            $stmt = $conn->prepare("SELECT * FROM sport_categories WHERE tournament_format IS NOT NULL");
            $stmt->execute();
            $categories = $stmt->fetchAll();
            
            if ($categories) {
                echo '<h3>Current Sport Categories with Tournament Formats:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Current Format</th><th>Action</th></tr>';
                
                foreach ($categories as $category) {
                    echo '<tr>';
                    echo '<td>' . $category['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($category['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($category['tournament_format']) . '</td>';
                    
                    // Standardize tournament format based on sport type
                    $new_format = 'single_elimination'; // Default
                    if (stripos($category['name'], 'round') !== false || stripos($category['name'], 'robin') !== false) {
                        $new_format = 'round_robin';
                    }
                    
                    if ($category['tournament_format'] !== $new_format) {
                        try {
                            $stmt_update = $conn->prepare("UPDATE sport_categories SET tournament_format = ? WHERE id = ?");
                            $stmt_update->execute([$new_format, $category['id']]);
                            echo '<td style="color: green;">Updated to ' . $new_format . '</td>';
                            $changes_made[] = "Updated category " . $category['name'] . " format to " . $new_format;
                        } catch (Exception $e) {
                            echo '<td style="color: red;">Failed to update</td>';
                            $errors[] = "Failed to update category " . $category['name'] . ": " . $e->getMessage();
                        }
                    } else {
                        echo '<td style="color: blue;">No change needed</td>';
                    }
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<p>No sport categories with tournament formats found.</p>';
            }
            
            // Also check sports table
            $stmt = $conn->prepare("SELECT * FROM sports WHERE bracket_format IS NOT NULL");
            $stmt->execute();
            $sports = $stmt->fetchAll();
            
            if ($sports) {
                echo '<h3>Sports with Bracket Formats:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Current Format</th><th>Action</th></tr>';
                
                foreach ($sports as $sport) {
                    echo '<tr>';
                    echo '<td>' . $sport['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($sport['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($sport['bracket_format']) . '</td>';
                    
                    // Standardize format
                    $new_format = 'single_elimination';
                    if ($sport['bracket_format'] === 'round_robin' || stripos($sport['name'], 'round') !== false) {
                        $new_format = 'round_robin';
                    }
                    
                    if ($sport['bracket_format'] !== $new_format) {
                        try {
                            $stmt_update = $conn->prepare("UPDATE sports SET bracket_format = ? WHERE id = ?");
                            $stmt_update->execute([$new_format, $sport['id']]);
                            echo '<td style="color: green;">Updated to ' . $new_format . '</td>';
                            $changes_made[] = "Updated sport " . $sport['name'] . " format to " . $new_format;
                        } catch (Exception $e) {
                            echo '<td style="color: red;">Failed to update</td>';
                            $errors[] = "Failed to update sport " . $sport['name'] . ": " . $e->getMessage();
                        }
                    } else {
                        echo '<td style="color: blue;">No change needed</td>';
                    }
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            echo '</div>';
            
            // Step 5: Test the fix
            echo '<div class="step">';
            echo '<h2>Step 5: Test Database Fix</h2>';
            
            // Test INSERT with tournament columns
            try {
                $test_sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
                $stmt = $conn->prepare($test_sql);
                $stmt->execute();
                $test_id = $conn->lastInsertId();
                
                echo '<div class="success">✅ INSERT test successful (Test ID: ' . $test_id . ')</div>';
                
                // Clean up test record
                $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
                echo '<p>✓ Test record cleaned up</p>';
                
            } catch (Exception $e) {
                echo '<div class="error">';
                echo '<h3>❌ INSERT Test Failed</h3>';
                echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                echo '</div>';
                $errors[] = "INSERT test failed: " . $e->getMessage();
                $overall_success = false;
            }
            
            echo '</div>';
            
            // Re-enable foreign key checks
            $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Emergency fix error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
            $overall_success = false;
        } finally {
            // Ensure foreign key checks are re-enabled
            try {
                $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
            } catch (Exception $e) {
                // Ignore errors here
            }
        }
        ?>
        
        <!-- Results Summary -->
        <div class="step">
            <h2>📊 Emergency Fix Results</h2>
            
            <?php if ($overall_success): ?>
                <div class="success">
                    <h3>🎉 EMERGENCY FIX SUCCESSFUL!</h3>
                    <p><strong>✅ Database schema has been fixed</strong></p>
                    <p><strong>✅ Tournament format inconsistencies have been resolved</strong></p>
                    <p><strong>✅ Tournament creation should now work correctly</strong></p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h3>❌ Some Issues Occurred</h3>
                    <p>The emergency fix encountered some problems:</p>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($changes_made)): ?>
                <div class="info">
                    <h3>✅ Changes Applied:</h3>
                    <ul>
                        <?php foreach ($changes_made as $change): ?>
                            <li><?php echo htmlspecialchars($change); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Next Steps -->
        <div class="step">
            <h2>🚀 Next Steps</h2>
            <p>
                <?php if ($overall_success): ?>
                    <a href="comprehensive-tournament-test.php" class="btn btn-success">🧪 Test Tournament Creation</a>
                    <a href="manage-event.php?id=1" class="btn">📋 Manage Events</a>
                    <a href="index.php" class="btn">🏠 Admin Dashboard</a>
                <?php else: ?>
                    <a href="critical-database-investigation.php" class="btn btn-danger">🔍 Investigate Further</a>
                    <a href="manual-schema-fix.sql" class="btn">📄 Manual SQL Fix</a>
                <?php endif; ?>
            </p>
        </div>
        
        <div class="highlight">
            <h3>🔒 What This Fix Does</h3>
            <p>This emergency fix:</p>
            <ul>
                <li>✅ Adds missing tournament columns to the matches table</li>
                <li>✅ Creates all necessary tournament tables</li>
                <li>✅ Inserts default tournament formats</li>
                <li>✅ Resolves tournament format inconsistencies</li>
                <li>✅ Tests the fix to ensure it works</li>
            </ul>
        </div>
    </div>
</body>
</html>
