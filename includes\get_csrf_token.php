<?php
/**
 * CSRF Token Generator Endpoint
 * Returns a JSON response with a fresh CSRF token
 */

// Start output buffering to prevent any unwanted output
ob_start();

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/../config/config.php';
require_once 'functions.php';

// Clean any output that might have been generated
ob_clean();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Generate CSRF token
    $token = generateCSRFToken();
    
    echo json_encode([
        'success' => true,
        'token' => $token
    ]);
} catch (Exception $e) {
    // Clean any output that might have been generated
    ob_clean();
    
    echo json_encode([
        'success' => false,
        'message' => 'Failed to generate CSRF token: ' . $e->getMessage()
    ]);
}

// Ensure clean output
ob_end_flush();
?>
