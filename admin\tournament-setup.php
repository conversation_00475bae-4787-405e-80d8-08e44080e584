<?php
/**
 * Tournament Setup Page
 * SC_IMS Sports Competition and Event Management System
 * 
 * One-time setup page to initialize tournament management system
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// Ensure admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$setupComplete = false;
$errors = [];
$messages = [];

// Check if tournament tables already exist
function checkTournamentTables($conn) {
    $tables = ['sport_types', 'tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
    $existingTables = [];
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                $existingTables[] = $table;
            }
        } catch (PDOException $e) {
            // Table doesn't exist
        }
    }
    
    return $existingTables;
}

// Run tournament setup
if (isset($_POST['run_setup'])) {
    try {
        // Read the SQL file
        $sqlFile = __DIR__ . '/../database/tournament_schema_update.sql';
        $sql = file_get_contents($sqlFile);
        
        if ($sql === false) {
            throw new Exception('Could not read tournament schema file');
        }
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                try {
                    $conn->exec($statement);
                    $successCount++;
                    $messages[] = "✓ Executed: " . substr(str_replace(["\n", "\r"], ' ', $statement), 0, 80) . "...";
                } catch (PDOException $e) {
                    $errorCount++;
                    $errors[] = "✗ Error: " . $e->getMessage();
                    $errors[] = "  Statement: " . substr(str_replace(["\n", "\r"], ' ', $statement), 0, 80) . "...";
                }
            }
        }
        
        if ($errorCount == 0) {
            $messages[] = "✓ Tournament schema setup completed successfully!";
            $messages[] = "Successful statements: {$successCount}";
            $setupComplete = true;
        } else {
            $errors[] = "⚠ Tournament schema setup completed with {$errorCount} errors.";
            $messages[] = "Successful statements: {$successCount}";
        }
        
    } catch (Exception $e) {
        $errors[] = "Fatal error: " . $e->getMessage();
    }
}

$existingTables = checkTournamentTables($conn);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Setup - SC_IMS Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-styles.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .status-box {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .status-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        
        .status-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        
        .status-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        
        .table-list {
            list-style: none;
            padding: 0;
        }
        
        .table-list li {
            padding: 0.5rem;
            margin: 0.25rem 0;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #28a745;
        }
        
        .setup-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .setup-button:hover {
            background: #0056b3;
        }
        
        .setup-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .message-log {
            max-height: 400px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .message-log div {
            margin: 0.25rem 0;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="setup-container">
            <h1>Tournament Management System Setup</h1>
            
            <?php if (!empty($existingTables)): ?>
                <div class="status-box status-warning">
                    <h3>⚠ Tournament Tables Already Exist</h3>
                    <p>The following tournament tables are already present in the database:</p>
                    <ul class="table-list">
                        <?php foreach ($existingTables as $table): ?>
                            <li><?php echo htmlspecialchars($table); ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <p><strong>Warning:</strong> Running the setup again may overwrite existing data.</p>
                </div>
            <?php else: ?>
                <div class="status-box status-info">
                    <h3>ℹ Tournament System Not Initialized</h3>
                    <p>The tournament management system has not been set up yet. Click the button below to initialize the required database tables and data.</p>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errors)): ?>
                <div class="status-box status-error">
                    <h3>Setup Errors</h3>
                    <div class="message-log">
                        <?php foreach ($errors as $error): ?>
                            <div><?php echo htmlspecialchars($error); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($messages)): ?>
                <div class="status-box status-success">
                    <h3>Setup Messages</h3>
                    <div class="message-log">
                        <?php foreach ($messages as $message): ?>
                            <div><?php echo htmlspecialchars($message); ?></div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <?php if ($setupComplete): ?>
                <div class="status-box status-success">
                    <h3>✓ Setup Complete!</h3>
                    <p>The tournament management system has been successfully initialized. You can now:</p>
                    <ul>
                        <li>Create events with dynamic tournament formats</li>
                        <li>Use sport-type-specific bracket selection</li>
                        <li>Manage comprehensive tournament algorithms</li>
                        <li>Track tournament progression and standings</li>
                    </ul>
                    <p><a href="manage-event.php" class="btn btn-primary">Go to Event Management</a></p>
                </div>
            <?php else: ?>
                <form method="POST" style="margin-top: 2rem;">
                    <button type="submit" name="run_setup" class="setup-button" 
                            onclick="return confirm('Are you sure you want to run the tournament setup? This will create new database tables.')">
                        🚀 Initialize Tournament System
                    </button>
                </form>
            <?php endif; ?>
            
            <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #dee2e6;">
                <h3>What This Setup Does:</h3>
                <ul>
                    <li><strong>Sport Types:</strong> Creates categorization for Individual, Team, Academic, and Judged sports</li>
                    <li><strong>Tournament Formats:</strong> Adds 11 comprehensive tournament formats with mathematical algorithms</li>
                    <li><strong>Tournament Structures:</strong> Creates tables for managing active tournaments</li>
                    <li><strong>Tournament Rounds:</strong> Enables round-by-round tournament progression</li>
                    <li><strong>Tournament Participants:</strong> Manages participant seeding and advancement</li>
                    <li><strong>Enhanced Matches:</strong> Adds tournament-specific match management</li>
                    <li><strong>Algorithm Integration:</strong> Links tournament formats to PHP algorithm classes</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
