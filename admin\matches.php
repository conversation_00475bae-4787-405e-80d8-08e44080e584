<?php
/**
 * Match Management for SC_IMS Admin Panel - Modal Interface
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get all matches with details
$matches = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name, s.type as sport_type,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.abbreviation as team1_abbr,
                   d2.name as team2_dept_name, d2.abbreviation as team2_abbr
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            ORDER BY 
                CASE m.status 
                    WHEN 'ongoing' THEN 1 
                    WHEN 'scheduled' THEN 2 
                    WHEN 'completed' THEN 3 
                    WHEN 'cancelled' THEN 4 
                END, 
                m.scheduled_time ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    $error = "Error fetching matches: " . $e->getMessage();
}

// Get event sports for dropdown
$event_sports = [];
try {
    $sql = "SELECT es.*, e.name as event_name, s.name as sport_name 
            FROM event_sports es 
            JOIN events e ON es.event_id = e.id 
            JOIN sports s ON es.sport_id = s.id 
            ORDER BY e.name, s.name";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
} catch (Exception $e) {
    // Handle error silently
}

// Get registrations for team selection
$registrations = [];
try {
    $sql = "SELECT r.*, d.name as department_name, d.abbreviation, e.name as event_name, s.name as sport_name
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            JOIN event_sports es ON r.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            ORDER BY e.name, s.name, d.name";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $registrations = $stmt->fetchAll();
} catch (Exception $e) {
    // Handle error silently
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Management - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-calendar-check"></i>
                        <span>Match Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Match Management</h1>
                <p class="page-description">Schedule, manage, and monitor sports matches and competitions</p>
            </div>

            <!-- Matches Header with Add Button -->
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Matches</h3>
                    <button class="btn-modal-trigger" onclick="openModal('matchModal')">
                        <i class="fas fa-plus"></i>
                        Schedule New Match
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($matches)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Event/Sport</th>
                                    <th>Teams</th>
                                    <th>Round</th>
                                    <th>Scheduled Time</th>
                                    <th>Venue</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($matches as $match): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($match['event_name'] ?? ''); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($match['sport_name'] ?? ''); ?></small>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 8px;">
                                            <span class="team-badge"><?php echo htmlspecialchars($match['team1_abbr'] ?? ''); ?></span>
                                            <span>vs</span>
                                            <span class="team-badge"><?php echo htmlspecialchars($match['team2_abbr'] ?? 'TBD'); ?></span>
                                        </div>
                                    </td>
                                    <td>Round <?php echo $match['round_number'] ?? 1; ?></td>
                                    <td>
                                        <?php if ($match['scheduled_time']): ?>
                                            <?php echo date('M j, Y g:i A', strtotime($match['scheduled_time'])); ?>
                                        <?php else: ?>
                                            <span class="text-muted">Not scheduled</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($match['venue'] ?? 'TBD'); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $match['status'] ?? 'scheduled'; ?>">
                                            <?php echo ucfirst($match['status'] ?? 'scheduled'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editMatch(<?php echo $match['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <?php if ($match['status'] === 'scheduled'): ?>
                                                <a href="../referee/scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-sm btn-success" target="_blank">
                                                    <i class="fas fa-play"></i> Start
                                                </a>
                                            <?php endif; ?>
                                            <button class="btn-delete" onclick="deleteMatch(<?php echo $match['id']; ?>, '<?php echo htmlspecialchars($match['team1_abbr'] ?? ''); ?> vs <?php echo htmlspecialchars($match['team2_abbr'] ?? 'TBD'); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-calendar-check" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
                            <p style="font-size: 18px; margin-bottom: 8px;">No matches found</p>
                            <p style="margin-bottom: 20px;">Schedule your first match to get started</p>
                            <button class="btn-modal-trigger" onclick="openModal('matchModal')">
                                <i class="fas fa-plus"></i>
                                Schedule First Match
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Match Modal -->
    <div id="matchModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="matchModalTitle">Schedule New Match</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                <input type="hidden" name="entity" value="match">
                <input type="hidden" name="action" value="create" id="matchAction">
                <input type="hidden" name="id" id="matchId">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="modal-body">
                    <div class="form-group">
                        <label for="matchEventSport">Event & Sport *</label>
                        <select id="matchEventSport" name="event_sport_id" class="form-control" required onchange="loadTeams()">
                            <option value="">Select Event & Sport</option>
                            <?php foreach ($event_sports as $es): ?>
                                <option value="<?php echo $es['id']; ?>">
                                    <?php echo htmlspecialchars($es['event_name'] . ' - ' . $es['sport_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="error-message">Event & Sport selection is required</div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="matchTeam1">Team 1 *</label>
                            <select id="matchTeam1" name="team1_id" class="form-control" required>
                                <option value="">Select Team 1</option>
                            </select>
                            <div class="error-message">Team 1 is required</div>
                        </div>
                        <div class="form-group">
                            <label for="matchTeam2">Team 2</label>
                            <select id="matchTeam2" name="team2_id" class="form-control">
                                <option value="">Select Team 2 (Optional)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="matchRound">Round Number</label>
                            <input type="number" id="matchRound" name="round_number" class="form-control" min="1" value="1">
                        </div>
                        <div class="form-group">
                            <label for="matchNumber">Match Number</label>
                            <input type="number" id="matchNumber" name="match_number" class="form-control" min="1" value="1">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="matchScheduledTime">Scheduled Time</label>
                            <input type="datetime-local" id="matchScheduledTime" name="scheduled_time" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="matchVenue">Venue</label>
                            <input type="text" id="matchVenue" name="venue" class="form-control" placeholder="Enter venue...">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="matchStatus">Status</label>
                        <select id="matchStatus" name="status" class="form-control">
                            <option value="scheduled">Scheduled</option>
                            <option value="ongoing">Ongoing</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="matchSubmitBtn">Schedule Match</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    
    <script>
        // Store registrations data for team loading
        const registrationsData = <?php echo json_encode($registrations); ?>;
        
        function loadTeams() {
            const eventSportId = document.getElementById('matchEventSport').value;
            const team1Select = document.getElementById('matchTeam1');
            const team2Select = document.getElementById('matchTeam2');
            
            // Clear existing options
            team1Select.innerHTML = '<option value="">Select Team 1</option>';
            team2Select.innerHTML = '<option value="">Select Team 2 (Optional)</option>';
            
            if (eventSportId) {
                // Filter registrations for selected event sport
                const filteredRegistrations = registrationsData.filter(reg => reg.event_sport_id == eventSportId);
                
                filteredRegistrations.forEach(reg => {
                    const option1 = new Option(`${reg.department_name} (${reg.abbreviation})`, reg.id);
                    const option2 = new Option(`${reg.department_name} (${reg.abbreviation})`, reg.id);
                    team1Select.add(option1);
                    team2Select.add(option2);
                });
            }
        }
        
        function editMatch(matchId) {
            // Fetch match data and populate modal
            fetch(`ajax/get-match.php?id=${matchId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const match = data.match;
                        
                        // Update modal title and form
                        document.getElementById('matchModalTitle').textContent = 'Edit Match';
                        document.getElementById('matchAction').value = 'update';
                        document.getElementById('matchId').value = match.id;
                        document.getElementById('matchSubmitBtn').textContent = 'Update Match';
                        
                        // Populate form fields
                        editRecord('matchModal', match);
                        
                        // Load teams for the selected event sport
                        loadTeams();
                    } else {
                        alert('Error loading match data: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading match data');
                });
        }
        
        function deleteMatch(matchId, matchDescription) {
            if (confirm(`Are you sure you want to delete the match "${matchDescription}"? This action cannot be undone.`)) {
                const formData = new FormData();
                formData.append('entity', 'match');
                formData.append('action', 'delete');
                formData.append('id', matchId);
                formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

                fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.modalManager.showSuccessMessage(data.message);
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting match');
                });
            }
        }
        
        // Reset modal when opening for new match
        document.addEventListener('DOMContentLoaded', function() {
            const matchModal = document.getElementById('matchModal');
            matchModal.addEventListener('show', function() {
                if (document.getElementById('matchAction').value === 'create') {
                    document.getElementById('matchModalTitle').textContent = 'Schedule New Match';
                    document.getElementById('matchSubmitBtn').textContent = 'Schedule Match';
                    matchModal.querySelector('form').reset();
                    loadTeams();
                }
            });
        });
    </script>
    
    <style>
        .team-badge {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }
    </style>
</body>
</html>
