<?php
/**
 * Find Correct Badminton Category Parameters
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>Find Correct Badminton Category</h1>";
echo "<p>Let me find the correct parameters for your Badminton category...</p>";

try {
    // Find all Badminton-related categories
    echo "<h2>Step 1: Find Badminton Event Sports</h2>";
    $stmt = $conn->prepare("
        SELECT 
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE s.name LIKE '%badminton%' OR s.name LIKE '%Badminton%'
        ORDER BY es.id
    ");
    $stmt->execute();
    $badminton_event_sports = $stmt->fetchAll();
    
    if (empty($badminton_event_sports)) {
        echo "<p style='color: red;'>❌ No Badminton event sports found!</p>";
        
        // Show all sports to help identify
        echo "<h3>All Available Sports:</h3>";
        $stmt = $conn->prepare("SELECT id, name FROM sports ORDER BY name");
        $stmt->execute();
        $all_sports = $stmt->fetchAll();
        
        echo "<ul>";
        foreach ($all_sports as $sport) {
            echo "<li>ID {$sport['id']}: {$sport['name']}</li>";
        }
        echo "</ul>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Event Sport ID</th><th>Event ID</th><th>Sport ID</th><th>Event Name</th><th>Sport Name</th></tr>";
    foreach ($badminton_event_sports as $es) {
        echo "<tr>";
        echo "<td>{$es['event_sport_id']}</td>";
        echo "<td>{$es['event_id']}</td>";
        echo "<td>{$es['sport_id']}</td>";
        echo "<td>{$es['event_name']}</td>";
        echo "<td>{$es['sport_name']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Find categories for each Badminton event sport
    echo "<h2>Step 2: Find Badminton Categories</h2>";
    foreach ($badminton_event_sports as $es) {
        echo "<h3>{$es['event_name']} - {$es['sport_name']} (Event Sport ID: {$es['event_sport_id']})</h3>";
        
        $stmt = $conn->prepare("
            SELECT * FROM sport_categories 
            WHERE event_sport_id = ?
            ORDER BY category_name
        ");
        $stmt->execute([$es['event_sport_id']]);
        $categories = $stmt->fetchAll();
        
        if (empty($categories)) {
            echo "<p style='color: orange;'>⚠ No categories found for this event sport</p>";
            continue;
        }
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Category ID</th><th>Category Name</th><th>Type</th><th>Status</th><th>Correct URL</th></tr>";
        foreach ($categories as $cat) {
            $url = "manage-category.php?event_id={$es['event_id']}&sport_id={$es['sport_id']}&category_id={$cat['id']}";
            echo "<tr>";
            echo "<td>{$cat['id']}</td>";
            echo "<td>{$cat['category_name']}</td>";
            echo "<td>{$cat['category_type']}</td>";
            echo "<td>{$cat['status']}</td>";
            echo "<td><a href='{$url}' target='_blank'>Open Category</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check registrations for this event sport
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM registrations
            WHERE event_sport_id = ? AND status IN ('confirmed', 'approved')
        ");
        $stmt->execute([$es['event_sport_id']]);
        $reg_count = $stmt->fetch()['count'];
        
        echo "<p><strong>Confirmed/Approved Registrations:</strong> {$reg_count}</p>";
        
        if ($reg_count < 2) {
            echo "<p style='color: red;'>❌ Need more registrations for tournament creation</p>";
            echo "<p><a href='quick-registration-fix.php?event_sport_id={$es['event_sport_id']}' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Create Registrations for this Event Sport</a></p>";
        } else {
            echo "<p style='color: green;'>✅ Sufficient registrations for tournament creation!</p>";
        }
        
        echo "<hr>";
    }
    
    // Check if the original URL parameters exist at all
    echo "<h2>Step 3: Check Original URL Parameters</h2>";
    echo "<p>Checking if Event ID=1, Sport ID=4, Category ID=2 exists...</p>";
    
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = 2 AND es.event_id = 1 AND es.sport_id = 4
    ");
    $stmt->execute();
    $original_category = $stmt->fetch();
    
    if ($original_category) {
        echo "<p style='color: green;'>✅ Original category exists!</p>";
        echo "<p>Category: {$original_category['category_name']}</p>";
        echo "<p>Event: {$original_category['event_name']}</p>";
        echo "<p>Sport: {$original_category['sport_name']}</p>";
    } else {
        echo "<p style='color: red;'>❌ Original category (Event ID=1, Sport ID=4, Category ID=2) does not exist!</p>";
        echo "<p><strong>This explains why you're getting 'Category not found!' error.</strong></p>";
    }
    
    // Show what does exist for Event ID=1
    echo "<h3>What exists for Event ID=1:</h3>";
    $stmt = $conn->prepare("
        SELECT 
            es.id as event_sport_id,
            es.sport_id,
            s.name as sport_name,
            COUNT(sc.id) as category_count
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
        WHERE es.event_id = 1
        GROUP BY es.id
        ORDER BY s.name
    ");
    $stmt->execute();
    $event1_sports = $stmt->fetchAll();
    
    if (empty($event1_sports)) {
        echo "<p style='color: red;'>❌ No sports found for Event ID=1</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Event Sport ID</th><th>Sport ID</th><th>Sport Name</th><th>Categories</th></tr>";
        foreach ($event1_sports as $sport) {
            echo "<tr>";
            echo "<td>{$sport['event_sport_id']}</td>";
            echo "<td>{$sport['sport_id']}</td>";
            echo "<td>{$sport['sport_name']}</td>";
            echo "<td>{$sport['category_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>🎯 Recommended Action:</h2>";
    if (!empty($badminton_event_sports)) {
        $first_badminton = $badminton_event_sports[0];
        echo "<p style='color: green; font-size: 16px;'><strong>Use the correct Badminton category from the table above!</strong></p>";
        echo "<p>The working Badminton event sport is:</p>";
        echo "<ul>";
        echo "<li><strong>Event ID:</strong> {$first_badminton['event_id']}</li>";
        echo "<li><strong>Sport ID:</strong> {$first_badminton['sport_id']}</li>";
        echo "<li><strong>Event Sport ID:</strong> {$first_badminton['event_sport_id']}</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>You need to create a Badminton event sport first!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
