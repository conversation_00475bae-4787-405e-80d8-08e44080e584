<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

// Check if tournament tables exist
$tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
foreach ($tables as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$table]);
    $exists = $stmt->fetch() ? 'EXISTS' : 'MISSING';
    echo "$table: $exists\n";
}

// Check if tournament_manager.php exists
if (file_exists('../includes/tournament_manager.php')) {
    echo "tournament_manager.php: EXISTS\n";
} else {
    echo "tournament_manager.php: MISSING\n";
}

// Check if tournament_algorithms.php exists
if (file_exists('../includes/tournament_algorithms.php')) {
    echo "tournament_algorithms.php: EXISTS\n";
} else {
    echo "tournament_algorithms.php: MISSING\n";
}
?>
