<?php
/**
 * Debug Add Sport Issues
 */

// Start output buffering to capture any unwanted output
ob_start();

// Disable error display
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Clear any output that might have been generated
ob_clean();

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Simulate the exact add sport process
    $event_id = 1;
    $sport_id = 1; // Basketball
    $tournament_format_id = 1;
    $seeding_method = 'random';
    
    // Step 1: Check if sport already added to event
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$event_id, $sport_id]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo json_encode([
            'success' => false,
            'message' => 'Sport already added to this event',
            'debug' => [
                'existing_id' => $existing['id'],
                'event_id' => $event_id,
                'sport_id' => $sport_id
            ]
        ]);
        exit;
    }
    
    // Step 2: Get sport details
    $stmt = $conn->prepare("SELECT name, type FROM sports WHERE id = ?");
    $stmt->execute([$sport_id]);
    $sport = $stmt->fetch();
    
    if (!$sport) {
        echo json_encode([
            'success' => false,
            'message' => 'Sport not found',
            'debug' => [
                'sport_id' => $sport_id
            ]
        ]);
        exit;
    }
    
    // Step 3: Get tournament format details
    $stmt = $conn->prepare("SELECT name FROM tournament_formats WHERE id = ?");
    $stmt->execute([$tournament_format_id]);
    $format = $stmt->fetch();
    
    if (!$format) {
        echo json_encode([
            'success' => false,
            'message' => 'Tournament format not found',
            'debug' => [
                'tournament_format_id' => $tournament_format_id
            ]
        ]);
        exit;
    }
    
    // Step 4: Insert into event_sports
    $stmt = $conn->prepare("
        INSERT INTO event_sports (event_id, sport_id, tournament_format_id, seeding_method, max_teams, status, created_at) 
        VALUES (?, ?, ?, ?, 8, 'registration', NOW())
    ");
    
    $inserted = $stmt->execute([$event_id, $sport_id, $tournament_format_id, $seeding_method]);
    
    if (!$inserted) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to insert event sport',
            'debug' => [
                'error_info' => $stmt->errorInfo()
            ]
        ]);
        exit;
    }
    
    $event_sport_id = $conn->lastInsertId();
    
    // Step 5: Test logAdminActivity
    try {
        logAdminActivity('ADD_SPORT_TO_EVENT', 'event_sports', $event_sport_id,
            null, "Added sport to event (Event: {$event_id}, Sport: {$sport_id}, Format: {$tournament_format_id})");
        
        $log_success = true;
    } catch (Exception $e) {
        $log_success = false;
        $log_error = $e->getMessage();
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Sport added successfully (debug mode)',
        'debug' => [
            'event_sport_id' => $event_sport_id,
            'sport_name' => $sport['name'],
            'sport_type' => $sport['type'],
            'format_name' => $format['name'],
            'log_success' => $log_success,
            'log_error' => $log_error ?? null
        ]
    ]);
    
    // Clean up - remove the test record
    $stmt = $conn->prepare("DELETE FROM event_sports WHERE id = ?");
    $stmt->execute([$event_sport_id]);
    
} catch (Exception $e) {
    // Clear any output buffer to ensure clean JSON
    ob_clean();
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}

// Ensure output buffer is flushed
ob_end_flush();
?>
