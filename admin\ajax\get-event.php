<?php
/**
 * Get Event Data for Modal Editing
 * SC_IMS Admin Panel AJAX Endpoint
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get event ID
$event_id = $_GET['id'] ?? 0;

if (!$event_id) {
    echo json_encode([
        'success' => false,
        'message' => 'Event ID is required'
    ]);
    exit;
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Fetch event data
    $stmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    
    if (!$event) {
        echo json_encode([
            'success' => false,
            'message' => 'Event not found'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'event' => $event
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching event data: ' . $e->getMessage()
    ]);
}
?>
