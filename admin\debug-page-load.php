<?php
/**
 * Debug script to test if the manage-event.php page loads correctly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting debug test...\n";

// Test if we can include the required files
try {
    require_once 'auth.php';
    echo "✓ auth.php loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Error loading auth.php: " . $e->getMessage() . "\n";
    exit;
}

try {
    require_once '../config/database.php';
    echo "✓ database.php loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Error loading database.php: " . $e->getMessage() . "\n";
    exit;
}

try {
    require_once '../includes/functions.php';
    echo "✓ functions.php loaded successfully\n";
} catch (Exception $e) {
    echo "✗ Error loading functions.php: " . $e->getMessage() . "\n";
    exit;
}

// Test database connection
try {
    $database = new Database();
    $conn = $database->getConnection();
    echo "✓ Database connection successful\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit;
}

// Test if event exists
$event_id = 1; // Test with event ID 1
try {
    $event = getEventById($conn, $event_id);
    if ($event) {
        echo "✓ Event found: " . $event['name'] . "\n";
    } else {
        echo "✗ Event not found with ID: $event_id\n";
        exit;
    }
} catch (Exception $e) {
    echo "✗ Error getting event: " . $e->getMessage() . "\n";
    exit;
}

// Test other functions
try {
    $event_sports = getEventSports($conn, $event_id);
    echo "✓ Event sports loaded: " . count($event_sports) . " sports\n";
} catch (Exception $e) {
    echo "✗ Error loading event sports: " . $e->getMessage() . "\n";
}

try {
    $available_sports = getAvailableSports($conn, $event_id);
    echo "✓ Available sports loaded: " . count($available_sports) . " sports\n";
} catch (Exception $e) {
    echo "✗ Error loading available sports: " . $e->getMessage() . "\n";
}

try {
    $departments = getAllDepartments($conn);
    echo "✓ Departments loaded: " . count($departments) . " departments\n";
} catch (Exception $e) {
    echo "✗ Error loading departments: " . $e->getMessage() . "\n";
}

try {
    $standings = getEventStandings($conn, $event_id);
    echo "✓ Event standings loaded: " . count($standings) . " entries\n";
} catch (Exception $e) {
    echo "✗ Error loading event standings: " . $e->getMessage() . "\n";
}

try {
    $recent_matches = getEventRecentMatches($conn, $event_id);
    echo "✓ Recent matches loaded: " . count($recent_matches) . " matches\n";
} catch (Exception $e) {
    echo "✗ Error loading recent matches: " . $e->getMessage() . "\n";
}

echo "\nAll tests completed. If all tests passed, the issue is likely in the frontend JavaScript.\n";
?>
