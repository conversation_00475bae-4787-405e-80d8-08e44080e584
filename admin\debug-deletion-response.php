<?php
/**
 * Debug Event Deletion Response
 */

// Start output buffering to prevent any unwanted output
ob_start();

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Clean any output that might have been generated
ob_clean();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'test_modal_handler':
            // Test the modal handler directly
            $eventId = $_POST['event_id'] ?? 1;
            
            // Get CSRF token
            $csrfToken = generateCSRFToken();
            
            // Prepare data for modal handler
            $postData = [
                'entity' => 'event',
                'action' => 'delete',
                'id' => $eventId,
                'csrf_token' => $csrfToken,
                'force_cascade' => 'true'
            ];
            
            // Make internal request to modal handler
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost/SC_IMS/admin/ajax/modal-handler.php');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            // Split headers and body
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $headers = substr($response, 0, $headerSize);
            $body = substr($response, $headerSize);
            
            echo json_encode([
                'success' => true,
                'http_code' => $httpCode,
                'headers' => $headers,
                'body' => $body,
                'body_length' => strlen($body),
                'is_json' => json_decode($body) !== null
            ]);
            break;
            
        case 'test_dependency_check':
            // Test dependency checking endpoint
            $eventId = $_POST['event_id'] ?? 1;
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost/SC_IMS/admin/ajax/check-event-dependencies.php');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(['event_id' => $eventId]));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HEADER, true);
            curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            // Split headers and body
            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $headers = substr($response, 0, $headerSize);
            $body = substr($response, $headerSize);
            
            echo json_encode([
                'success' => true,
                'http_code' => $httpCode,
                'headers' => $headers,
                'body' => $body,
                'body_length' => strlen($body),
                'is_json' => json_decode($body) !== null
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'Invalid action'
            ]);
    }
} else {
    // Clean output buffer for HTML response
    ob_clean();
    header('Content-Type: text/html');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Debug Event Deletion Response</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
            .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
            button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            button:hover { background: #0056b3; }
            pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
            .response-details { margin: 10px 0; }
            .response-details h4 { margin: 5px 0; }
        </style>
    </head>
    <body>
        <h1>Debug Event Deletion Response</h1>
        
        <div class="section">
            <h3>1. Test Modal Handler Response</h3>
            <div id="modal-result"></div>
            <button onclick="testModalHandler()">Test Modal Handler</button>
        </div>
        
        <div class="section">
            <h3>2. Test Dependency Check Response</h3>
            <div id="dependency-result"></div>
            <button onclick="testDependencyCheck()">Test Dependency Check</button>
        </div>

        <script>
            async function testModalHandler() {
                const resultDiv = document.getElementById('modal-result');
                resultDiv.innerHTML = '<p>Testing modal handler...</p>';
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'test_modal_handler');
                    formData.append('event_id', '1');
                    
                    const response = await fetch('', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    console.log('Modal handler test response:', responseText);
                    
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ Modal handler test completed</div>
                            <div class="response-details">
                                <h4>HTTP Code: ${data.http_code}</h4>
                                <h4>Body Length: ${data.body_length}</h4>
                                <h4>Is Valid JSON: ${data.is_json ? 'Yes' : 'No'}</h4>
                                <h4>Headers:</h4>
                                <pre>${data.headers}</pre>
                                <h4>Response Body:</h4>
                                <pre>${data.body}</pre>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    console.error('Modal handler test error:', error);
                }
            }
            
            async function testDependencyCheck() {
                const resultDiv = document.getElementById('dependency-result');
                resultDiv.innerHTML = '<p>Testing dependency check...</p>';
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'test_dependency_check');
                    formData.append('event_id', '1');
                    
                    const response = await fetch('', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    console.log('Dependency check test response:', responseText);
                    
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ Dependency check test completed</div>
                            <div class="response-details">
                                <h4>HTTP Code: ${data.http_code}</h4>
                                <h4>Body Length: ${data.body_length}</h4>
                                <h4>Is Valid JSON: ${data.is_json ? 'Yes' : 'No'}</h4>
                                <h4>Headers:</h4>
                                <pre>${data.headers}</pre>
                                <h4>Response Body:</h4>
                                <pre>${data.body}</pre>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    console.error('Dependency check test error:', error);
                }
            }
        </script>
    </body>
    </html>
    <?php
}

// Ensure clean output
ob_end_flush();
?>
