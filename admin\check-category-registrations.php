<?php
/**
 * Check Category Registrations
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters from URL
$event_id = $_GET['event_id'] ?? 1;
$sport_id = $_GET['sport_id'] ?? 4;
$category_id = $_GET['category_id'] ?? 2;

echo "<h1>Category Registration Check</h1>";
echo "<p>Checking registrations for Event ID: {$event_id}, Sport ID: {$sport_id}, Category ID: {$category_id}</p>";

try {
    // Get category details
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ? AND es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        echo "<p style='color: red;'>❌ Category not found!</p>";
        exit;
    }
    
    echo "<h2>Category Details:</h2>";
    echo "<ul>";
    echo "<li><strong>Category:</strong> {$category['category_name']}</li>";
    echo "<li><strong>Event:</strong> {$category['event_name']}</li>";
    echo "<li><strong>Sport:</strong> {$category['sport_name']}</li>";
    echo "<li><strong>Event Sport ID:</strong> {$category['event_sport_id']}</li>";
    echo "</ul>";
    
    // Check registrations for this event sport
    echo "<h2>Registrations for this Event Sport:</h2>";
    $stmt = $conn->prepare("
        SELECT 
            r.*,
            d.name as department_name
        FROM registrations r
        JOIN departments d ON r.department_id = d.id
        WHERE r.event_sport_id = ?
        ORDER BY r.status, d.name
    ");
    $stmt->execute([$category['event_sport_id']]);
    $registrations = $stmt->fetchAll();
    
    if (empty($registrations)) {
        echo "<p style='color: red;'>❌ No registrations found for this event sport!</p>";
        echo "<p><strong>This is why tournament creation is failing.</strong></p>";
        
        // Show available departments
        echo "<h3>Available Departments:</h3>";
        $stmt = $conn->prepare("SELECT id, name FROM departments ORDER BY name");
        $stmt->execute();
        $departments = $stmt->fetchAll();
        
        echo "<form method='post' action='create-test-registrations.php'>";
        echo "<input type='hidden' name='event_sport_id' value='{$category['event_sport_id']}'>";
        echo "<p>Select departments to register for this sport:</p>";
        
        foreach ($departments as $dept) {
            echo "<label style='display: block; margin: 5px 0;'>";
            echo "<input type='checkbox' name='departments[]' value='{$dept['id']}'> ";
            echo htmlspecialchars($dept['name']);
            echo "</label>";
        }
        
        echo "<br><button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;'>Create Test Registrations</button>";
        echo "</form>";
        
    } else {
        echo "<p style='color: green;'>✓ Found " . count($registrations) . " registrations:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Department</th><th>Team Name</th><th>Status</th><th>Registration Date</th></tr>";
        
        $confirmed_count = 0;
        foreach ($registrations as $reg) {
            $status_color = ($reg['status'] == 'confirmed' || $reg['status'] == 'approved') ? 'green' : 'orange';
            if ($reg['status'] == 'confirmed' || $reg['status'] == 'approved') {
                $confirmed_count++;
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($reg['department_name']) . "</td>";
            echo "<td>" . htmlspecialchars($reg['team_name'] ?? 'N/A') . "</td>";
            echo "<td style='color: {$status_color};'>" . htmlspecialchars($reg['status']) . "</td>";
            echo "<td>" . htmlspecialchars($reg['registration_date']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Confirmed/Approved registrations: {$confirmed_count}</strong></p>";
        
        if ($confirmed_count >= 2) {
            echo "<p style='color: green;'>✓ Sufficient registrations for tournament creation!</p>";
            echo "<p><a href='manage-category.php?event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Try Creating Tournament Again</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Need at least 2 confirmed/approved registrations for tournament creation.</p>";
            
            if (count($registrations) >= 2) {
                echo "<p>You have enough registrations, but they need to be confirmed/approved.</p>";
                echo "<form method='post' action='approve-registrations.php'>";
                echo "<input type='hidden' name='event_sport_id' value='{$category['event_sport_id']}'>";
                echo "<button type='submit' style='background: #ffc107; color: black; padding: 10px 20px; border: none; border-radius: 4px;'>Approve All Registrations</button>";
                echo "</form>";
            } else {
                echo "<p>You need more registrations. Use the form above to create test registrations.</p>";
            }
        }
    }
    
    // Show all event sports for debugging
    echo "<h2>All Event Sports for Event {$event_id}:</h2>";
    $stmt = $conn->prepare("
        SELECT 
            es.id,
            es.sport_id,
            s.name as sport_name,
            COUNT(r.id) as registration_count
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN registrations r ON es.id = r.event_sport_id
        WHERE es.event_id = ?
        GROUP BY es.id
        ORDER BY s.name
    ");
    $stmt->execute([$event_id]);
    $all_event_sports = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Event Sport ID</th><th>Sport Name</th><th>Registrations</th></tr>";
    foreach ($all_event_sports as $es) {
        $highlight = ($es['id'] == $category['event_sport_id']) ? 'background: yellow;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td>{$es['id']}</td>";
        echo "<td>{$es['sport_name']}</td>";
        echo "<td>{$es['registration_count']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
