<?php
/**
 * Get Sport Data for Modal Editing
 * SC_IMS Admin Panel AJAX Endpoint
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get sport ID
$sport_id = $_GET['id'] ?? 0;

if (!$sport_id) {
    echo json_encode([
        'success' => false,
        'message' => 'Sport ID is required'
    ]);
    exit;
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Fetch sport data with enhanced type information
    $stmt = $conn->prepare("
        SELECT
            s.*,
            st.name as sport_type_name,
            st.description as sport_type_description,
            st.category as sport_type_category,
            st.color_code,
            st.icon_class
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        WHERE s.id = ?
    ");
    $stmt->execute([$sport_id]);
    $sport = $stmt->fetch();
    
    if (!$sport) {
        echo json_encode([
            'success' => false,
            'message' => 'Sport not found'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'sport' => $sport
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching sport data: ' . $e->getMessage()
    ]);
}
?>
