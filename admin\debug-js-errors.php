<?php
/**
 * Debug JavaScript errors in manage-event.php
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? 1;

$event = getEventById($conn, $event_id);
if (!$event) {
    echo "Event not found!";
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug JS Errors - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
    
    <style>
        .debug-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error-log {
            background: #fff3cd;
            border-left-color: #ffc107;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success-log {
            background: #d1edff;
            border-left-color: #0dcaf0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>JavaScript Error Debugging</h1>
        
        <div class="debug-section">
            <h3>Event Information</h3>
            <p><strong>Event ID:</strong> <?php echo $event_id; ?></p>
            <p><strong>Event Name:</strong> <?php echo htmlspecialchars($event['name']); ?></p>
            <p><strong>Admin User:</strong> <?php echo htmlspecialchars($current_admin['username']); ?></p>
        </div>

        <div class="debug-section">
            <h3>JavaScript Loading Test</h3>
            <p>Testing if JavaScript loads properly...</p>
            <div id="js-test-result">JavaScript not loaded yet...</div>
        </div>

        <div class="debug-section">
            <h3>jQuery Test</h3>
            <div id="jquery-test-result">jQuery test pending...</div>
        </div>

        <div class="debug-section">
            <h3>Modal System Test</h3>
            <div id="modal-test-result">Modal system test pending...</div>
            <button onclick="testModal()" class="btn btn-primary">Test Modal</button>
        </div>

        <div class="debug-section">
            <h3>Console Error Log</h3>
            <div id="error-log" class="error-log">No errors captured yet...</div>
        </div>

        <div class="debug-section">
            <h3>Function Availability Test</h3>
            <div id="function-test-result">Function test pending...</div>
        </div>
    </div>

    <!-- Test Modal -->
    <div id="testModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>Test Modal</h3>
                <span class="modal-close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>This is a test modal to check if the modal system works.</p>
            </div>
        </div>
    </div>

    <script>
        // Capture JavaScript errors
        window.addEventListener('error', function(e) {
            const errorLog = document.getElementById('error-log');
            const errorMsg = `ERROR: ${e.message}\nFile: ${e.filename}\nLine: ${e.lineno}\nColumn: ${e.colno}\nStack: ${e.error ? e.error.stack : 'No stack trace'}\n\n`;
            errorLog.textContent += errorMsg;
            console.error('Captured error:', e);
        });

        // Test basic JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');
            
            // Test 1: Basic JavaScript
            document.getElementById('js-test-result').innerHTML = '<span style="color: green;">✓ JavaScript is working!</span>';
            
            // Test 2: jQuery
            if (typeof $ !== 'undefined') {
                document.getElementById('jquery-test-result').innerHTML = '<span style="color: green;">✓ jQuery is loaded (version: ' + $.fn.jquery + ')</span>';
                
                // Test jQuery functionality
                try {
                    $('body').addClass('jquery-test');
                    document.getElementById('jquery-test-result').innerHTML += '<br><span style="color: green;">✓ jQuery functionality works</span>';
                } catch (e) {
                    document.getElementById('jquery-test-result').innerHTML += '<br><span style="color: red;">✗ jQuery functionality error: ' + e.message + '</span>';
                }
            } else {
                document.getElementById('jquery-test-result').innerHTML = '<span style="color: red;">✗ jQuery is not loaded</span>';
            }
            
            // Test 3: Modal Manager
            if (typeof window.modalManager !== 'undefined') {
                document.getElementById('modal-test-result').innerHTML = '<span style="color: green;">✓ Modal Manager is available</span>';
            } else {
                document.getElementById('modal-test-result').innerHTML = '<span style="color: red;">✗ Modal Manager is not available</span>';
            }
            
            // Test 4: Function availability
            const functions = ['openModal', 'closeModal', 'showSuccess', 'showError'];
            let functionResults = '';
            
            functions.forEach(func => {
                if (typeof window[func] === 'function') {
                    functionResults += `<span style="color: green;">✓ ${func} is available</span><br>`;
                } else {
                    functionResults += `<span style="color: red;">✗ ${func} is not available</span><br>`;
                }
            });
            
            document.getElementById('function-test-result').innerHTML = functionResults;
            
            console.log('All tests completed');
        });

        // Test modal function
        function testModal() {
            console.log('Testing modal...');
            if (typeof openModal === 'function') {
                openModal('testModal');
            } else {
                alert('openModal function not available');
            }
        }

        // Simple close modal function for testing
        function closeModal() {
            const modal = document.getElementById('testModal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        console.log('Debug script loaded successfully');
    </script>

    <!-- Include admin scripts -->
    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
