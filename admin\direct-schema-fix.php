<?php
/**
 * Direct Schema Fix - Immediate Database Resolution
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

// Start output buffering to capture all output
ob_start();

echo "<h1>Direct Schema Fix - Immediate Resolution</h1>";
echo "<p>Timestamp: " . date('Y-m-d H:i:s') . "</p>";

$success = false;
$error_message = "";

try {
    // Step 1: Check current matches table structure
    echo "<h2>Step 1: Current Matches Table Structure</h2>";
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $current_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    $has_tournament_columns = false;
    $existing_tournament_columns = [];
    
    foreach ($current_columns as $column) {
        $is_tournament = in_array($column['Field'], ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match']);
        $row_style = $is_tournament ? 'background: #e8f5e8;' : '';
        
        echo "<tr style='$row_style'>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
        
        if ($is_tournament) {
            $has_tournament_columns = true;
            $existing_tournament_columns[] = $column['Field'];
        }
    }
    echo "</table>";
    
    echo "<p><strong>Tournament columns found:</strong> " . ($has_tournament_columns ? implode(', ', $existing_tournament_columns) : "NONE") . "</p>";
    
    // Step 2: Check foreign key constraints that might be causing issues
    echo "<h2>Step 2: Checking Foreign Key Constraints</h2>";
    $stmt = $conn->prepare("
        SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'matches'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $stmt->execute();
    $foreign_keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $problematic_fks = [];
    if (empty($foreign_keys)) {
        echo "<p>✓ No foreign key constraints found on matches table.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Constraint Name</th><th>Column</th><th>References</th><th>Status</th></tr>";
        foreach ($foreign_keys as $fk) {
            $is_tournament_fk = strpos($fk['COLUMN_NAME'], 'tournament') !== false;
            $row_style = $is_tournament_fk ? 'background: #ffe8e8;' : '';
            $status = $is_tournament_fk ? 'PROBLEMATIC' : 'OK';
            
            echo "<tr style='$row_style'>";
            echo "<td>" . htmlspecialchars($fk['CONSTRAINT_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($fk['COLUMN_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($fk['REFERENCED_TABLE_NAME']) . "." . htmlspecialchars($fk['REFERENCED_COLUMN_NAME']) . "</td>";
            echo "<td><strong>$status</strong></td>";
            echo "</tr>";
            
            if ($is_tournament_fk) {
                $problematic_fks[] = $fk;
            }
        }
        echo "</table>";
    }
    
    // Step 3: Apply the definitive fix
    echo "<h2>Step 3: Applying Definitive Schema Fix</h2>";
    
    // Start transaction for safety
    $conn->beginTransaction();
    
    // Disable foreign key checks temporarily
    $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "<p>✓ Disabled foreign key checks</p>";
    
    // Remove problematic foreign key constraints
    foreach ($problematic_fks as $fk) {
        try {
            $sql = "ALTER TABLE matches DROP FOREIGN KEY " . $fk['CONSTRAINT_NAME'];
            $conn->exec($sql);
            echo "<p>✓ Dropped problematic foreign key: " . htmlspecialchars($fk['CONSTRAINT_NAME']) . "</p>";
        } catch (Exception $e) {
            echo "<p>⚠ Could not drop foreign key " . htmlspecialchars($fk['CONSTRAINT_NAME']) . ": " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // Drop existing tournament columns if they exist (to ensure clean slate)
    $tournament_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
    $existing_columns = array_column($current_columns, 'Field');
    
    foreach ($tournament_columns as $column) {
        if (in_array($column, $existing_columns)) {
            try {
                $sql = "ALTER TABLE matches DROP COLUMN $column";
                $conn->exec($sql);
                echo "<p>✓ Dropped existing column: $column</p>";
            } catch (Exception $e) {
                echo "<p>⚠ Could not drop column $column: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    // Add tournament columns with correct definitions (no foreign keys initially)
    echo "<h3>Adding Tournament Columns (Clean Definitions)</h3>";
    
    $column_definitions = [
        'tournament_structure_id' => 'INT NULL COMMENT "Tournament structure reference"',
        'tournament_round_id' => 'INT NULL COMMENT "Tournament round reference"', 
        'bracket_position' => 'VARCHAR(50) NULL COMMENT "Position in tournament bracket"',
        'is_bye_match' => 'BOOLEAN DEFAULT FALSE COMMENT "Whether this is a bye match"'
    ];
    
    foreach ($column_definitions as $column => $definition) {
        try {
            $sql = "ALTER TABLE matches ADD COLUMN $column $definition";
            $conn->exec($sql);
            echo "<p>✓ Added column: $column</p>";
        } catch (Exception $e) {
            echo "<p>❌ Failed to add column $column: " . htmlspecialchars($e->getMessage()) . "</p>";
            throw $e; // Re-throw to trigger rollback
        }
    }
    
    // Commit the transaction
    $conn->commit();
    echo "<p>✓ Transaction committed successfully</p>";
    
    // Re-enable foreign key checks
    $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "<p>✓ Re-enabled foreign key checks</p>";
    
    $success = true;
    
} catch (Exception $e) {
    // Rollback transaction on error
    try {
        $conn->rollback();
        echo "<p>⚠ Transaction rolled back due to error</p>";
    } catch (Exception $rollback_error) {
        echo "<p>❌ Could not rollback transaction: " . htmlspecialchars($rollback_error->getMessage()) . "</p>";
    }
    
    $error_message = $e->getMessage();
    echo "<h2>❌ Error During Schema Fix</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>File: " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
} finally {
    // Ensure foreign key checks are re-enabled
    try {
        $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
    } catch (Exception $e) {
        // Ignore errors here
    }
}

// Step 4: Test the fix if successful
if ($success) {
    echo "<h2>Step 4: Testing the Fix</h2>";
    
    // Test column access
    $all_columns_work = true;
    foreach ($tournament_columns as $column) {
        try {
            $stmt = $conn->prepare("SELECT $column FROM matches LIMIT 1");
            $stmt->execute();
            echo "<p>✓ Column $column is accessible</p>";
        } catch (Exception $e) {
            echo "<p>❌ Column $column access failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            $all_columns_work = false;
        }
    }
    
    // Test INSERT with tournament columns
    if ($all_columns_work) {
        try {
            $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $test_id = $conn->lastInsertId();
            
            echo "<p>✅ <strong>INSERT with tournament columns SUCCESSFUL!</strong> (Test ID: $test_id)</p>";
            
            // Clean up test record
            $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
            echo "<p>✓ Test record cleaned up</p>";
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h2>🎉 SCHEMA FIX SUCCESSFUL!</h2>";
            echo "<p><strong>The tournament_structure_id column issue has been permanently resolved.</strong></p>";
            echo "<p>You can now create tournaments without database errors.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<p>❌ INSERT test failed: " . htmlspecialchars($e->getMessage()) . "</p>";
            $success = false;
        }
    } else {
        $success = false;
    }
}

// Final status
if ($success) {
    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>✅ Next Steps:</h3>";
    echo "<ul>";
    echo "<li><a href='test-tournament-creation-final.php'>Test Tournament Creation</a></li>";
    echo "<li><a href='manage-event.php?id=1'>Manage Events</a></li>";
    echo "<li><a href='index.php'>Return to Admin Dashboard</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>❌ Fix Failed</h3>";
    echo "<p>The schema fix was not successful. Please check the error messages above.</p>";
    echo "<p><a href='diagnose-database-state.php'>Run Detailed Diagnosis</a></p>";
    echo "</div>";
}

// End output buffering and display
$output = ob_get_clean();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Direct Schema Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <?php echo $output; ?>
</body>
</html>
