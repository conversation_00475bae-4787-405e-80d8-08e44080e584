<?php
/**
 * Comprehensive investigation tool for sport addition/removal issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set content type
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sport Issues Investigation - SC_IMS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .test-success { background-color: #d4edda; color: #155724; }
        .test-error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Sport Issues Investigation & Cleanup Tool</h1>
    
    <div class="section info">
        <h3>🔍 Database State Investigation</h3>
        <p>This tool will investigate the current database state and identify issues with sport addition/removal.</p>
        <button class="btn-primary" onclick="investigateDatabase()">Start Investigation</button>
        <div id="investigation-results"></div>
    </div>
    
    <div class="section warning">
        <h3>🧹 Database Cleanup</h3>
        <p>Clean up orphaned records and fix inconsistencies.</p>
        <button class="btn-warning" onclick="cleanupDatabase()">Clean Database</button>
        <div id="cleanup-results"></div>
    </div>
    
    <div class="section">
        <h3>🧪 Test AJAX Endpoints</h3>
        <p>Test the AJAX endpoints to identify JSON response issues.</p>
        <button class="btn-primary" onclick="testAjaxEndpoints()">Test AJAX</button>
        <div id="ajax-test-results"></div>
    </div>
    
    <div class="section">
        <h3>🔄 Test Add/Remove Workflow</h3>
        <p>Test the complete add/remove sport workflow.</p>
        <button class="btn-success" onclick="testWorkflow()">Test Workflow</button>
        <div id="workflow-test-results"></div>
    </div>

    <script>
        async function investigateDatabase() {
            const resultsDiv = document.getElementById('investigation-results');
            resultsDiv.innerHTML = '<p>🔍 Investigating database state...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=investigate'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                <h4>✅ Investigation Complete</h4>
                                <pre>${JSON.stringify(data.results, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="test-error">
                                <h4>❌ Investigation Failed</h4>
                                <p>${data.message}</p>
                            </div>
                        `;
                    }
                } catch (e) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            <h4>❌ JSON Parse Error</h4>
                            <p>Error: ${e.message}</p>
                            <p>Raw response:</p>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function cleanupDatabase() {
            const resultsDiv = document.getElementById('cleanup-results');
            resultsDiv.innerHTML = '<p>🧹 Cleaning up database...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=cleanup'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                <h4>✅ Cleanup Complete</h4>
                                <pre>${JSON.stringify(data.results, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="test-error">
                                <h4>❌ Cleanup Failed</h4>
                                <p>${data.message}</p>
                            </div>
                        `;
                    }
                } catch (e) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            <h4>❌ JSON Parse Error</h4>
                            <p>Error: ${e.message}</p>
                            <p>Raw response:</p>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testAjaxEndpoints() {
            const resultsDiv = document.getElementById('ajax-test-results');
            resultsDiv.innerHTML = '<p>🧪 Testing AJAX endpoints...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=test_ajax'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                <h4>✅ AJAX Tests Complete</h4>
                                <pre>${JSON.stringify(data.results, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="test-error">
                                <h4>❌ AJAX Tests Failed</h4>
                                <p>${data.message}</p>
                            </div>
                        `;
                    }
                } catch (e) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            <h4>❌ JSON Parse Error</h4>
                            <p>Error: ${e.message}</p>
                            <p>Raw response:</p>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testWorkflow() {
            const resultsDiv = document.getElementById('workflow-test-results');
            resultsDiv.innerHTML = '<p>🔄 Testing add/remove workflow...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=test_workflow'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div class="test-success">
                                <h4>✅ Workflow Tests Complete</h4>
                                <pre>${JSON.stringify(data.results, null, 2)}</pre>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="test-error">
                                <h4>❌ Workflow Tests Failed</h4>
                                <p>${data.message}</p>
                            </div>
                        `;
                    }
                } catch (e) {
                    resultsDiv.innerHTML = `
                        <div class="test-error">
                            <h4>❌ JSON Parse Error</h4>
                            <p>Error: ${e.message}</p>
                            <p>Raw response:</p>
                            <pre>${text}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
