<?php
/**
 * Populate Test Registrations for Unified Department Registration System
 * Creates sample department registrations to test the category management system
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/department_registration.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

$messages = [];

try {
    // Get available events and departments
    $stmt = $conn->prepare("SELECT id, name FROM events ORDER BY id LIMIT 3");
    $stmt->execute();
    $events = $stmt->fetchAll();

    $stmt = $conn->prepare("SELECT id, name FROM departments ORDER BY id LIMIT 5");
    $stmt->execute();
    $departments = $stmt->fetchAll();

    if (empty($events) || empty($departments)) {
        throw new Exception("No events or departments found. Please create some first.");
    }

    // For each event, register some departments
    foreach ($events as $event) {
        $messages[] = "📅 Processing Event: " . $event['name'];
        
        // Register 3-4 departments per event
        $dept_count = 0;
        foreach ($departments as $dept) {
            if ($dept_count >= 4) break; // Limit to 4 departments per event
            
            // Check if department already registered
            $stmt = $conn->prepare("SELECT id FROM event_department_registrations WHERE event_id = ? AND department_id = ?");
            $stmt->execute([$event['id'], $dept['id']]);
            if ($stmt->fetch()) {
                $messages[] = "  ⚠️ {$dept['name']} already registered for {$event['name']}";
                continue;
            }

            // Register department for the entire event
            $registration_data = [
                'status' => 'approved',
                'contact_person' => 'Test Contact ' . $dept['name'],
                'contact_email' => strtolower(str_replace(' ', '', $dept['name'])) . '@test.com',
                'contact_phone' => '555-' . str_pad($dept['id'], 4, '0', STR_PAD_LEFT),
                'notes' => 'Test registration for unified system',
                'total_participants' => rand(15, 30)
            ];

            $result = registerDepartmentForEvent($conn, $event['id'], $dept['id'], $registration_data);
            
            if ($result['success']) {
                $messages[] = "  ✅ Registered {$dept['name']} for {$event['name']}";
                $dept_count++;
            } else {
                $messages[] = "  ❌ Failed to register {$dept['name']}: " . $result['message'];
            }
        }
    }

    // Create some sample matches for testing
    $messages[] = "\n🏆 Creating sample matches...";
    
    // Get some registrations to create matches
    $stmt = $conn->prepare("
        SELECT r.id, r.event_sport_id, r.team_name, d.name as dept_name
        FROM registrations r 
        JOIN departments d ON r.department_id = d.id 
        WHERE r.status = 'approved'
        ORDER BY r.event_sport_id, r.id 
        LIMIT 10
    ");
    $stmt->execute();
    $teams = $stmt->fetchAll();

    // Group teams by event_sport_id
    $teams_by_sport = [];
    foreach ($teams as $team) {
        $teams_by_sport[$team['event_sport_id']][] = $team;
    }

    // Create matches for each sport that has at least 2 teams
    foreach ($teams_by_sport as $event_sport_id => $sport_teams) {
        if (count($sport_teams) < 2) continue;
        
        // Create a few matches
        for ($i = 0; $i < count($sport_teams) - 1; $i += 2) {
            if (!isset($sport_teams[$i + 1])) break;
            
            $team1 = $sport_teams[$i];
            $team2 = $sport_teams[$i + 1];
            
            // Check if match already exists
            $stmt = $conn->prepare("
                SELECT id FROM matches 
                WHERE event_sport_id = ? AND team1_id = ? AND team2_id = ?
            ");
            $stmt->execute([$event_sport_id, $team1['id'], $team2['id']]);
            if ($stmt->fetch()) continue;
            
            // Create match
            $stmt = $conn->prepare("
                INSERT INTO matches 
                (event_sport_id, team1_id, team2_id, round_number, match_number, status, scheduled_time, venue)
                VALUES (?, ?, ?, 1, ?, 'completed', NOW() - INTERVAL 1 DAY, 'Test Venue')
            ");
            $stmt->execute([$event_sport_id, $team1['id'], $team2['id'], ($i / 2) + 1]);
            $match_id = $conn->lastInsertId();
            
            // Add random scores
            $team1_score = rand(0, 5);
            $team2_score = rand(0, 5);
            $winner_id = $team1_score > $team2_score ? $team1['id'] : ($team2_score > $team1_score ? $team2['id'] : null);
            
            $stmt = $conn->prepare("
                INSERT INTO scores (match_id, team1_score, team2_score, is_final)
                VALUES (?, ?, ?, 1)
            ");
            $stmt->execute([$match_id, $team1_score, $team2_score]);
            
            // Update match winner
            $stmt = $conn->prepare("UPDATE matches SET winner_id = ? WHERE id = ?");
            $stmt->execute([$winner_id, $match_id]);
            
            $messages[] = "  ⚽ Created match: {$team1['team_name']} vs {$team2['team_name']} ({$team1_score}-{$team2_score})";
        }
    }

} catch (Exception $e) {
    $messages[] = "❌ Error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Populate Test Registrations | SC_IMS Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .message {
            padding: 0.5rem 1rem;
            margin: 0.25rem 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-line;
        }
        .message:has-text("✅") {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .message:has-text("❌") {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        .message:has-text("⚠️") {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Registration Population</h1>
        <p>This script creates sample department registrations using the unified registration system to test the category management functionality.</p>
        
        <h3>Results:</h3>
        <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 1rem; background: #f8f9fa;">
            <?php foreach ($messages as $message): ?>
                <div class="message"><?php echo htmlspecialchars($message); ?></div>
            <?php endforeach; ?>
        </div>
        
        <div style="margin-top: 2rem;">
            <a href="department-registrations.php" class="btn">📋 View Department Registrations</a>
            <a href="sport-categories.php?event_id=1&sport_id=1" class="btn">🏆 View Sport Categories</a>
            <a href="manage-category.php?event_id=1&sport_id=1&category_id=2" class="btn btn-success">🎯 Test Category Management</a>
        </div>
    </div>
</body>
</html>
