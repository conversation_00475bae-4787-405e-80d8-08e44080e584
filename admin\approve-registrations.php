<?php
/**
 * Approve Registrations
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $event_sport_id = $_POST['event_sport_id'] ?? '';
    
    if (empty($event_sport_id)) {
        echo "<p style='color: red;'>❌ Event Sport ID is required!</p>";
        echo "<p><a href='javascript:history.back()'>Go Back</a></p>";
        exit;
    }
    
    echo "<h1>Approving Registrations</h1>";
    echo "<p>Event Sport ID: {$event_sport_id}</p>";
    
    try {
        // Get all registrations for this event sport
        $stmt = $conn->prepare("
            SELECT r.*, d.name as department_name
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ? AND r.status NOT IN ('confirmed', 'approved')
        ");
        $stmt->execute([$event_sport_id]);
        $registrations = $stmt->fetchAll();
        
        if (empty($registrations)) {
            echo "<p style='color: orange;'>⚠ No registrations found that need approval.</p>";
            echo "<p><a href='javascript:history.back()'>Go Back</a></p>";
            exit;
        }
        
        $conn->beginTransaction();
        
        $approved_count = 0;
        foreach ($registrations as $reg) {
            $stmt = $conn->prepare("UPDATE registrations SET status = 'confirmed' WHERE id = ?");
            $stmt->execute([$reg['id']]);
            
            echo "<p style='color: green;'>✓ Approved registration for {$reg['department_name']}</p>";
            $approved_count++;
        }
        
        $conn->commit();
        
        echo "<h2>Summary:</h2>";
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ Approved {$approved_count} registrations!</p>";
        
        // Get the category details for redirect
        $stmt = $conn->prepare("
            SELECT sc.id, es.event_id, es.sport_id
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            WHERE es.id = ?
            LIMIT 1
        ");
        $stmt->execute([$event_sport_id]);
        $category = $stmt->fetch();
        
        if ($category && $approved_count >= 2) {
            echo "<p><a href='manage-category.php?event_id={$category['event_id']}&sport_id={$category['sport_id']}&category_id={$category['id']}' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px;'>Go Create Tournament Now!</a></p>";
        }
        
        echo "<p><a href='check-category-registrations.php?event_id=" . ($_GET['event_id'] ?? 1) . "&sport_id=" . ($_GET['sport_id'] ?? 4) . "&category_id=" . ($_GET['category_id'] ?? 2) . "'>Check Registrations Again</a></p>";
        
    } catch (Exception $e) {
        $conn->rollBack();
        echo "<p style='color: red;'>❌ Transaction failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><a href='javascript:history.back()'>Go Back</a></p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Invalid request method</p>";
    echo "<p><a href='javascript:history.back()'>Go Back</a></p>";
}
?>
