<?php
/**
 * Debug script to check tournament formats and sport addition issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set content type
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Formats Debug - SC_IMS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Tournament Formats and Sport Addition Debug</h1>
    
    <div class="debug-section">
        <h3>Tournament Formats Table</h3>
        <?php
        try {
            $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
            $stmt->execute();
            $formats = $stmt->fetchAll();
            
            if ($formats) {
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Code</th><th>Description</th><th>Sport Types</th></tr>';
                foreach ($formats as $format) {
                    echo '<tr>';
                    echo '<td>' . $format['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($format['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($format['code']) . '</td>';
                    echo '<td>' . htmlspecialchars($format['description']) . '</td>';
                    echo '<td>' . htmlspecialchars($format['sport_types']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<p class="error">No tournament formats found! This might be the issue.</p>';
                
                // Try to create basic tournament formats
                echo '<h4>Creating Basic Tournament Formats</h4>';
                $basic_formats = [
                    ['Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'team,individual'],
                    ['Double Elimination', 'double_elimination', 'Tournament with winner and loser brackets', 'team,individual'],
                    ['Round Robin', 'round_robin', 'Every participant plays every other participant', 'team,individual'],
                    ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic']
                ];
                
                foreach ($basic_formats as $format) {
                    try {
                        $stmt = $conn->prepare("
                            INSERT INTO tournament_formats (name, code, description, sport_types)
                            VALUES (?, ?, ?, ?)
                        ");
                        $stmt->execute($format);
                        echo "<p>✓ Created format: {$format[0]}</p>";
                    } catch (Exception $e) {
                        echo "<p class=\"error\">Failed to create {$format[0]}: " . $e->getMessage() . "</p>";
                    }
                }
            }
        } catch (Exception $e) {
            echo '<p class="error">Error checking tournament formats: ' . htmlspecialchars($e->getMessage()) . '</p>';
            
            // Check if table exists
            try {
                $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
                $stmt->execute();
                $table_exists = $stmt->fetch();
                
                if (!$table_exists) {
                    echo '<p class="error">tournament_formats table does not exist!</p>';
                    echo '<h4>Creating tournament_formats table</h4>';
                    
                    $sql = "CREATE TABLE tournament_formats (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        code VARCHAR(50) NOT NULL UNIQUE,
                        description TEXT,
                        sport_types TEXT,
                        min_participants INT DEFAULT 2,
                        max_participants INT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )";
                    
                    $conn->exec($sql);
                    echo '<p class="success">✓ tournament_formats table created</p>';
                    
                    // Insert basic formats
                    $basic_formats = [
                        ['Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'team,individual', 2, 0],
                        ['Double Elimination', 'double_elimination', 'Tournament with winner and loser brackets', 'team,individual', 2, 0],
                        ['Round Robin', 'round_robin', 'Every participant plays every other participant', 'team,individual', 2, 16],
                        ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic', 4, 0]
                    ];
                    
                    foreach ($basic_formats as $format) {
                        $stmt = $conn->prepare("
                            INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                            VALUES (?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute($format);
                        echo "<p>✓ Inserted format: {$format[0]}</p>";
                    }
                }
            } catch (Exception $e2) {
                echo '<p class="error">Error creating table: ' . htmlspecialchars($e2->getMessage()) . '</p>';
            }
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>Sports Table</h3>
        <?php
        try {
            $stmt = $conn->prepare("SELECT id, name, type, sport_type_id FROM sports ORDER BY name");
            $stmt->execute();
            $sports = $stmt->fetchAll();
            
            if ($sports) {
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Type</th><th>Sport Type ID</th></tr>';
                foreach ($sports as $sport) {
                    echo '<tr>';
                    echo '<td>' . $sport['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($sport['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($sport['type']) . '</td>';
                    echo '<td>' . ($sport['sport_type_id'] ?: 'NULL') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<p class="error">No sports found!</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error checking sports: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>Events Table</h3>
        <?php
        try {
            $stmt = $conn->prepare("SELECT id, name, status FROM events ORDER BY id");
            $stmt->execute();
            $events = $stmt->fetchAll();
            
            if ($events) {
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Status</th></tr>';
                foreach ($events as $event) {
                    echo '<tr>';
                    echo '<td>' . $event['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($event['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($event['status']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            } else {
                echo '<p class="error">No events found!</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">Error checking events: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h3>Test Sport Addition</h3>
        <p>This will test adding Basketball (ID: 1) to Event 1 using the same logic as the AJAX handler:</p>
        
        <?php
        if ($_POST['test_add'] ?? false) {
            echo '<h4>Test Results:</h4>';
            
            try {
                $event_id = 1;
                $sport_id = 1;
                $tournament_format_id = 1;
                
                // Check if sport already added to event
                $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
                $stmt->execute([$event_id, $sport_id]);
                if ($stmt->fetch()) {
                    echo '<p class="error">Sport already added to this event</p>';
                } else {
                    // Check if tournament format exists
                    $stmt = $conn->prepare("SELECT code FROM tournament_formats WHERE id = ?");
                    $stmt->execute([$tournament_format_id]);
                    $format = $stmt->fetch();
                    
                    if (!$format) {
                        echo '<p class="error">Tournament format not found</p>';
                    } else {
                        $bracket_type = $format['code'];
                        
                        // Insert event sport
                        $stmt = $conn->prepare("
                            INSERT INTO event_sports (event_id, sport_id, bracket_type, max_teams, registration_deadline)
                            VALUES (?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([$event_id, $sport_id, $bracket_type, 8, null]);
                        
                        $event_sport_id = $conn->lastInsertId();
                        echo "<p class=\"success\">✓ Sport added successfully! Event Sport ID: {$event_sport_id}</p>";
                    }
                }
            } catch (Exception $e) {
                echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
            }
        }
        ?>
        
        <form method="POST">
            <input type="hidden" name="test_add" value="1">
            <button type="submit" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 3px;">Test Add Sport</button>
        </form>
    </div>
    
    <p><a href="manage-event.php?event_id=1">← Back to Event Management</a></p>
</body>
</html>
