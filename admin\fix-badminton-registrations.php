<?php
/**
 * Fix Badminton Registrations - Direct Solution
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>Fix Badminton Tournament Registration Issue</h1>";
echo "<p>This will directly fix the registration issue for your Badminton category...</p>";

try {
    // Find the event sport ID for event_id=1, sport_id=4
    $stmt = $conn->prepare("
        SELECT es.id as event_sport_id, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE es.event_id = 1 AND es.sport_id = 4
    ");
    $stmt->execute();
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event Sport not found for Event ID 1, Sport ID 4!</p>";
        echo "<p>Let me check what event sports exist...</p>";
        
        $stmt = $conn->prepare("
            SELECT es.*, e.name as event_name, s.name as sport_name
            FROM event_sports es
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            ORDER BY es.id
        ");
        $stmt->execute();
        $all_event_sports = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Event Sport ID</th><th>Event</th><th>Sport</th><th>Event ID</th><th>Sport ID</th></tr>";
        foreach ($all_event_sports as $es) {
            echo "<tr>";
            echo "<td>{$es['id']}</td>";
            echo "<td>{$es['event_name']}</td>";
            echo "<td>{$es['sport_name']}</td>";
            echo "<td>{$es['event_id']}</td>";
            echo "<td>{$es['sport_id']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        exit;
    }
    
    $event_sport_id = $event_sport['event_sport_id'];
    echo "<p><strong>Found:</strong> {$event_sport['event_name']} - {$event_sport['sport_name']} (Event Sport ID: {$event_sport_id})</p>";
    
    // Check existing registrations
    $stmt = $conn->prepare("
        SELECT r.*, d.name as department_name
        FROM registrations r
        JOIN departments d ON r.department_id = d.id
        WHERE r.event_sport_id = ?
    ");
    $stmt->execute([$event_sport_id]);
    $existing_registrations = $stmt->fetchAll();
    
    echo "<h2>Current Registrations:</h2>";
    if (empty($existing_registrations)) {
        echo "<p style='color: red;'>❌ No registrations found - this is the problem!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Department</th><th>Team Name</th><th>Status</th></tr>";
        $confirmed_count = 0;
        foreach ($existing_registrations as $reg) {
            $status_color = ($reg['status'] == 'confirmed' || $reg['status'] == 'approved') ? 'green' : 'orange';
            if ($reg['status'] == 'confirmed' || $reg['status'] == 'approved') {
                $confirmed_count++;
            }
            echo "<tr>";
            echo "<td>{$reg['department_name']}</td>";
            echo "<td>{$reg['team_name']}</td>";
            echo "<td style='color: {$status_color};'>{$reg['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p><strong>Confirmed/Approved: {$confirmed_count}</strong></p>";
    }
    
    // Create registrations if needed
    $confirmed_count = 0;
    foreach ($existing_registrations as $reg) {
        if ($reg['status'] == 'confirmed' || $reg['status'] == 'approved') {
            $confirmed_count++;
        }
    }
    
    if ($confirmed_count < 2) {
        echo "<h2>Creating Missing Registrations:</h2>";
        
        // Get departments
        $stmt = $conn->prepare("SELECT id, name FROM departments ORDER BY name LIMIT 6");
        $stmt->execute();
        $departments = $stmt->fetchAll();
        
        $conn->beginTransaction();
        
        $created_count = 0;
        $needed = 4 - $confirmed_count;
        
        foreach ($departments as $dept) {
            if ($created_count >= $needed) break;
            
            // Check if already registered
            $already_registered = false;
            foreach ($existing_registrations as $reg) {
                if ($reg['department_id'] == $dept['id']) {
                    $already_registered = true;
                    break;
                }
            }
            
            if ($already_registered) {
                echo "<p>⚠ {$dept['name']} already registered</p>";
                continue;
            }
            
            // Create registration
            $team_name = $dept['name'] . " Badminton Team";
            $participants = [
                "Player 1 from " . $dept['name'],
                "Player 2 from " . $dept['name']
            ];
            
            $stmt = $conn->prepare("
                INSERT INTO registrations (event_sport_id, department_id, team_name, participants, status, registration_date)
                VALUES (?, ?, ?, ?, 'confirmed', NOW())
            ");
            $stmt->execute([
                $event_sport_id,
                $dept['id'],
                $team_name,
                json_encode($participants)
            ]);
            
            echo "<p style='color: green;'>✓ Created registration for {$dept['name']}</p>";
            $created_count++;
        }
        
        $conn->commit();
        
        echo "<h2>🎉 Success!</h2>";
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>Created {$created_count} registrations!</p>";
        echo "<p>Total confirmed registrations: " . ($confirmed_count + $created_count) . "</p>";
        
        if (($confirmed_count + $created_count) >= 2) {
            echo "<p style='color: green;'>✅ You now have enough registrations to create a tournament!</p>";
            echo "<p><a href='manage-category.php?event_id=1&sport_id=4&category_id=2' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px;'>🏆 Go Create Tournament Now!</a></p>";
        }
    } else {
        echo "<p style='color: green;'>✅ You already have enough registrations!</p>";
        echo "<p><a href='manage-category.php?event_id=1&sport_id=4&category_id=2' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px;'>🏆 Go Create Tournament Now!</a></p>";
    }
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
