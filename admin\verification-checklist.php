<?php
/**
 * Tournament System Verification Checklist
 * SC_IMS Sports Competition and Event Management System
 * 
 * Step-by-step verification process to confirm tournament creation is working
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Tournament System Verification Checklist</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 20px; border-left: 4px solid #007bff; margin: 20px 0; }
        .checklist { background: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 15px 0; }
        .checklist h3 { margin-top: 0; color: #495057; }
        .check-item { padding: 8px 0; border-bottom: 1px solid #eee; }
        .check-item:last-child { border-bottom: none; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        .status-indicator { font-weight: bold; margin-right: 10px; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .manual { color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ Tournament System Verification Checklist</h1>
        <p>Use this checklist to verify that the tournament creation system is working correctly.</p>
        
        <?php
        $overall_status = true;
        $checks = [];
        
        // Automated Checks
        echo '<div class="step">';
        echo '<h2>🤖 Automated Verification Checks</h2>';
        
        // Check 1: Database Schema
        echo '<div class="checklist">';
        echo '<h3>1. Database Schema Verification</h3>';
        
        try {
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $matches_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            $schema_ok = true;
            
            foreach ($required_columns as $column) {
                $exists = in_array($column, $matches_columns);
                $status = $exists ? 'PASS' : 'FAIL';
                $class = $exists ? 'pass' : 'fail';
                if (!$exists) $schema_ok = false;
                
                echo '<div class="check-item">';
                echo '<span class="status-indicator ' . $class . '">' . $status . '</span>';
                echo 'Column "' . $column . '" exists in matches table';
                echo '</div>';
            }
            
            $checks['schema'] = $schema_ok;
            if (!$schema_ok) $overall_status = false;
            
        } catch (Exception $e) {
            echo '<div class="check-item">';
            echo '<span class="status-indicator fail">FAIL</span>';
            echo 'Database schema check failed: ' . htmlspecialchars($e->getMessage());
            echo '</div>';
            $checks['schema'] = false;
            $overall_status = false;
        }
        
        echo '</div>';
        
        // Check 2: Tournament Tables
        echo '<div class="checklist">';
        echo '<h3>2. Tournament Tables Verification</h3>';
        
        $tournament_tables = [
            'tournament_formats' => 'Tournament format definitions',
            'tournament_structures' => 'Tournament instances',
            'tournament_rounds' => 'Tournament round management',
            'tournament_participants' => 'Tournament participant tracking'
        ];
        
        $stmt = $conn->prepare("SHOW TABLES");
        $stmt->execute();
        $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $tables_ok = true;
        foreach ($tournament_tables as $table => $description) {
            $exists = in_array($table, $existing_tables);
            $status = $exists ? 'PASS' : 'FAIL';
            $class = $exists ? 'pass' : 'fail';
            if (!$exists) $tables_ok = false;
            
            echo '<div class="check-item">';
            echo '<span class="status-indicator ' . $class . '">' . $status . '</span>';
            echo $table . ' - ' . $description;
            echo '</div>';
        }
        
        $checks['tables'] = $tables_ok;
        if (!$tables_ok) $overall_status = false;
        
        echo '</div>';
        
        // Check 3: INSERT Test
        echo '<div class="checklist">';
        echo '<h3>3. Database INSERT Test</h3>';
        
        try {
            $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
            $stmt = $conn->prepare($sql);
            $stmt->execute();
            $test_id = $conn->lastInsertId();
            
            // Clean up
            $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
            
            echo '<div class="check-item">';
            echo '<span class="status-indicator pass">PASS</span>';
            echo 'INSERT with tournament columns successful';
            echo '</div>';
            
            $checks['insert'] = true;
            
        } catch (Exception $e) {
            echo '<div class="check-item">';
            echo '<span class="status-indicator fail">FAIL</span>';
            echo 'INSERT test failed: ' . htmlspecialchars($e->getMessage());
            echo '</div>';
            
            $checks['insert'] = false;
            $overall_status = false;
        }
        
        echo '</div>';
        
        // Check 4: Tournament Manager
        echo '<div class="checklist">';
        echo '<h3>4. Tournament Manager Class</h3>';
        
        try {
            require_once '../includes/tournament_manager.php';
            $tournamentManager = new TournamentManager($conn);
            
            echo '<div class="check-item">';
            echo '<span class="status-indicator pass">PASS</span>';
            echo 'Tournament manager class loads successfully';
            echo '</div>';
            
            $checks['manager'] = true;
            
        } catch (Exception $e) {
            echo '<div class="check-item">';
            echo '<span class="status-indicator fail">FAIL</span>';
            echo 'Tournament manager failed: ' . htmlspecialchars($e->getMessage());
            echo '</div>';
            
            $checks['manager'] = false;
            $overall_status = false;
        }
        
        echo '</div>';
        
        // Check 5: Algorithm Classes
        echo '<div class="checklist">';
        echo '<h3>5. Tournament Algorithm Classes</h3>';
        
        try {
            require_once '../includes/tournament_algorithms.php';
            
            $algorithm_classes = ['SingleEliminationAlgorithm', 'DoubleEliminationAlgorithm', 'RoundRobinAlgorithm', 'SwissSystemAlgorithm'];
            $algorithms_ok = true;
            
            foreach ($algorithm_classes as $class) {
                $exists = class_exists($class);
                $status = $exists ? 'PASS' : 'FAIL';
                $class_status = $exists ? 'pass' : 'fail';
                if (!$exists) $algorithms_ok = false;
                
                echo '<div class="check-item">';
                echo '<span class="status-indicator ' . $class_status . '">' . $status . '</span>';
                echo $class . ' algorithm class';
                echo '</div>';
            }
            
            $checks['algorithms'] = $algorithms_ok;
            if (!$algorithms_ok) $overall_status = false;
            
        } catch (Exception $e) {
            echo '<div class="check-item">';
            echo '<span class="status-indicator fail">FAIL</span>';
            echo 'Algorithm classes failed: ' . htmlspecialchars($e->getMessage());
            echo '</div>';
            
            $checks['algorithms'] = false;
            $overall_status = false;
        }
        
        echo '</div>';
        
        echo '</div>';
        
        // Manual Verification Steps
        echo '<div class="step">';
        echo '<h2>👤 Manual Verification Steps</h2>';
        echo '<p>Follow these steps to manually verify tournament creation works:</p>';
        
        echo '<div class="checklist">';
        echo '<h3>Manual Testing Checklist</h3>';
        
        $manual_steps = [
            'Navigate to the Admin Dashboard',
            'Go to "Manage Events" and select an event',
            'Add a sport to the event if none exists',
            'Ensure at least 2 departments are registered for the sport',
            'Click "Create Tournament" for the sport',
            'Select a tournament format (e.g., Single Elimination)',
            'Enter a tournament name',
            'Click "Create Tournament" button',
            'Verify tournament is created without errors',
            'Check that matches are generated in the tournament bracket'
        ];
        
        foreach ($manual_steps as $index => $step) {
            echo '<div class="check-item">';
            echo '<span class="status-indicator manual">MANUAL</span>';
            echo ($index + 1) . '. ' . $step;
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
        
        // Overall Status
        echo '<div class="step">';
        echo '<h2>📊 Overall System Status</h2>';
        
        if ($overall_status) {
            echo '<div class="success">';
            echo '<h3>🎉 SYSTEM READY!</h3>';
            echo '<p>All automated checks passed. The tournament creation system should be working correctly.</p>';
            echo '<p><strong>You can now proceed with manual testing or start creating tournaments.</strong></p>';
            echo '</div>';
        } else {
            echo '<div class="error">';
            echo '<h3>❌ SYSTEM NOT READY</h3>';
            echo '<p>Some automated checks failed. Please fix the issues before proceeding.</p>';
            echo '</div>';
            
            echo '<h3>Failed Checks:</h3>';
            echo '<ul>';
            foreach ($checks as $check => $status) {
                if (!$status) {
                    echo '<li>' . ucfirst($check) . ' check failed</li>';
                }
            }
            echo '</ul>';
        }
        
        echo '<h3>Check Summary:</h3>';
        echo '<table style="width: 100%; border-collapse: collapse; margin: 15px 0;">';
        echo '<tr style="background: #f8f9fa;"><th style="border: 1px solid #ddd; padding: 8px;">Check</th><th style="border: 1px solid #ddd; padding: 8px;">Status</th></tr>';
        
        $check_names = [
            'schema' => 'Database Schema',
            'tables' => 'Tournament Tables',
            'insert' => 'INSERT Operations',
            'manager' => 'Tournament Manager',
            'algorithms' => 'Algorithm Classes'
        ];
        
        foreach ($check_names as $key => $name) {
            $status = $checks[$key] ?? false;
            $status_text = $status ? '✅ PASS' : '❌ FAIL';
            $row_class = $status ? 'background: #d4edda;' : 'background: #f8d7da;';
            
            echo '<tr style="' . $row_class . '">';
            echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $name . '</td>';
            echo '<td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">' . $status_text . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        
        echo '</div>';
        ?>
        
        <!-- Action Buttons -->
        <div class="step">
            <h2>🛠️ Available Actions</h2>
            <p>
                <?php if (!$overall_status): ?>
                    <a href="force-schema-rebuild.php" class="btn btn-danger">🔧 Force Schema Rebuild</a>
                    <a href="diagnose-database-state.php" class="btn btn-warning">🔍 Diagnose Issues</a>
                <?php endif; ?>
                <a href="test-tournament-creation-final.php" class="btn btn-success">🧪 Run Full Test</a>
                <a href="manage-event.php?id=1" class="btn">📋 Manage Events</a>
                <a href="index.php" class="btn">🏠 Admin Dashboard</a>
            </p>
        </div>
        
        <!-- Quick Fix Guide -->
        <?php if (!$overall_status): ?>
        <div class="step">
            <h2>🚑 Quick Fix Guide</h2>
            <div class="warning">
                <h3>If checks are failing, try these steps in order:</h3>
                <ol>
                    <li><strong>Run Force Schema Rebuild:</strong> <a href="force-schema-rebuild.php">force-schema-rebuild.php</a></li>
                    <li><strong>Verify the fix:</strong> Refresh this page to re-run checks</li>
                    <li><strong>Test tournament creation:</strong> <a href="test-tournament-creation-final.php">test-tournament-creation-final.php</a></li>
                    <li><strong>If still failing:</strong> Check the diagnose tool for detailed error information</li>
                </ol>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
