<?php
/**
 * Debug the actual Add Sport modal issue
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Simulate admin session
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h2>Debugging Actual Add Sport Modal Issue</h2>";

try {
    // Step 1: Check if we have any events
    echo "<h3>Step 1: Available Events</h3>";
    $stmt = $conn->prepare("SELECT * FROM events ORDER BY id LIMIT 5");
    $stmt->execute();
    $events = $stmt->fetchAll();
    
    if (empty($events)) {
        echo "<p style='color: red;'>❌ No events found! Creating a test event...</p>";
        $stmt = $conn->prepare("INSERT INTO events (name, description, start_date, end_date, status) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['Test Event', 'Test event for debugging', date('Y-m-d'), date('Y-m-d', strtotime('+7 days')), 'upcoming']);
        $test_event_id = $conn->lastInsertId();
        echo "<p style='color: green;'>✓ Created test event with ID: {$test_event_id}</p>";
    } else {
        echo "<p style='color: green;'>✓ Found " . count($events) . " events:</p>";
        echo "<ul>";
        foreach ($events as $event) {
            echo "<li><a href='manage-event.php?id={$event['id']}'>{$event['name']}</a> (ID: {$event['id']}, Status: {$event['status']})</li>";
        }
        echo "</ul>";
        $test_event_id = $events[0]['id'];
    }
    
    // Step 2: Check available sports for this event
    echo "<h3>Step 2: Available Sports for Event {$test_event_id}</h3>";
    $available_sports = getAvailableSports($conn, $test_event_id);
    
    if (empty($available_sports)) {
        echo "<p style='color: red;'>❌ No available sports found!</p>";
    } else {
        echo "<p style='color: green;'>✓ Found " . count($available_sports) . " available sports:</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Sport Type Name</th><th>Category</th><th>Data Attribute</th></tr>";
        foreach ($available_sports as $sport) {
            $data_type = $sport['sport_type_category'] ?? $sport['type'];
            echo "<tr>";
            echo "<td>{$sport['id']}</td>";
            echo "<td>{$sport['name']}</td>";
            echo "<td>" . ($sport['sport_type_name'] ?? 'NULL') . "</td>";
            echo "<td>" . ($sport['sport_type_category'] ?? 'NULL') . "</td>";
            echo "<td style='font-weight: bold; color: blue;'>{$data_type}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Step 3: Test the AJAX endpoint directly with the sport type from "Art Exhibition"
    echo "<h3>Step 3: Testing AJAX Endpoint with 'individual' sport type</h3>";
    
    // Find "Art Exhibition" sport
    $art_exhibition = null;
    foreach ($available_sports as $sport) {
        if (strpos(strtolower($sport['name']), 'art') !== false) {
            $art_exhibition = $sport;
            break;
        }
    }
    
    if ($art_exhibition) {
        echo "<p style='color: green;'>✓ Found Art Exhibition sport:</p>";
        echo "<ul>";
        echo "<li>ID: {$art_exhibition['id']}</li>";
        echo "<li>Name: {$art_exhibition['name']}</li>";
        echo "<li>Sport Type Name: " . ($art_exhibition['sport_type_name'] ?? 'NULL') . "</li>";
        echo "<li>Category: " . ($art_exhibition['sport_type_category'] ?? 'NULL') . "</li>";
        echo "<li>Data Type: " . ($art_exhibition['sport_type_category'] ?? $art_exhibition['type']) . "</li>";
        echo "</ul>";
        
        $sport_type = $art_exhibition['sport_type_category'] ?? $art_exhibition['type'] ?? 'individual';
        echo "<p>Testing AJAX endpoint with sport_type: <strong>{$sport_type}</strong></p>";
    } else {
        echo "<p style='color: orange;'>⚠ Art Exhibition not found, testing with 'individual' type</p>";
        $sport_type = 'individual';
    }
    
    // Simulate the AJAX call
    $_POST['sport_type'] = $sport_type;
    
    echo "<h4>Simulating AJAX call to get-tournament-formats.php:</h4>";
    
    // Capture output
    ob_start();
    
    // Prevent any output before JSON response
    ob_start();
    
    // Clear any output that might have been generated
    ob_clean();
    
    // Set JSON response header
    header('Content-Type: application/json');
    
    try {
        $sport_type_param = $_POST['sport_type'] ?? 'traditional';
        
        // Map sport types to database categories
        $type_mapping = [
            'traditional' => ['team', 'individual'],
            'team' => ['team'],
            'individual' => ['individual'],
            'academic' => ['academic'],
            'judged' => ['judged'],
            'performance' => ['performance']
        ];
        
        $sport_types = $type_mapping[$sport_type_param] ?? ['team', 'individual'];
        
        // Build WHERE clause for multiple sport types
        $where_conditions = [];
        $params = [];
        
        foreach ($sport_types as $type) {
            $where_conditions[] = "sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?";
            $params[] = $type . ',%';  // type at beginning
            $params[] = '%,' . $type . ',%';  // type in middle
            $params[] = '%,' . $type;  // type at end
            $params[] = $type;  // type alone
        }
        
        $where_clause = '(' . implode(') OR (', $where_conditions) . ')';
        
        // Get tournament formats that match the sport type
        $stmt = $conn->prepare("
            SELECT id, name, code, description, sport_types, min_participants, max_participants
            FROM tournament_formats 
            WHERE {$where_clause}
            ORDER BY name
        ");
        
        $stmt->execute($params);
        $formats = $stmt->fetchAll();
        
        // Debug logging
        error_log("Tournament formats query - Sport type: $sport_type_param, Mapped types: " . implode(',', $sport_types) . ", Found formats: " . count($formats));
        
        // Format the response
        $formatted_formats = [];
        foreach ($formats as $format) {
            $formatted_formats[] = [
                'id' => $format['id'],
                'name' => $format['name'],
                'code' => $format['code'],
                'description' => $format['description'],
                'min_participants' => $format['min_participants'],
                'max_participants' => $format['max_participants'],
                'requires_seeding' => in_array($format['code'], ['single_elimination', 'double_elimination', 'multi_stage']),
                'advancement_type' => in_array($format['code'], ['round_robin', 'swiss_system']) ? 'points' : 'elimination'
            ];
        }
        
        $response = [
            'success' => true,
            'formats' => $formatted_formats
        ];
        
        echo json_encode($response);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error loading tournament formats: ' . $e->getMessage()
        ]);
    }
    
    $ajax_output = ob_get_clean();
    
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($ajax_output);
    echo "</pre>";
    
    // Try to decode the JSON
    $decoded = json_decode($ajax_output, true);
    if ($decoded) {
        echo "<h4>Parsed JSON Response:</h4>";
        if ($decoded['success']) {
            echo "<p style='color: green;'>✓ Success! Found " . count($decoded['formats']) . " formats:</p>";
            if (!empty($decoded['formats'])) {
                echo "<ul>";
                foreach ($decoded['formats'] as $format) {
                    echo "<li><strong>{$format['name']}</strong> (ID: {$format['id']}, Code: {$format['code']})</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ Error: " . ($decoded['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Invalid JSON response</p>";
    }
    
    // Step 4: Check the actual JavaScript function
    echo "<h3>Step 4: JavaScript Function Analysis</h3>";
    echo "<p>The loadBracketTypes() function should:</p>";
    echo "<ol>";
    echo "<li>Get the selected sport option</li>";
    echo "<li>Extract the data-type attribute</li>";
    echo "<li>Make an AJAX call to ajax/get-tournament-formats.php</li>";
    echo "<li>Populate the tournament_format_id dropdown</li>";
    echo "</ol>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Check browser console for JavaScript errors</li>";
    echo "<li>Verify the AJAX call is being made</li>";
    echo "<li>Check if the response is being processed correctly</li>";
    echo "</ul>";
    
    // Clean up
    unset($_POST['sport_type']);
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
</style>
