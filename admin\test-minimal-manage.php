<?php
// Minimal version of manage-event.php to test JavaScript loading
require_once 'auth.php';
requireAdmin();

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? 1;
$event = getEventById($conn, $event_id);

if (!$event) {
    header('Location: events.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Event Management Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .tab-button { 
            padding: 10px 20px; 
            margin: 5px; 
            border: 1px solid #ccc; 
            background: #f8f9fa; 
            cursor: pointer; 
        }
        .tab-button.active { background: #007bff; color: white; }
        .tab-content { 
            display: none; 
            padding: 20px; 
            border: 1px solid #ccc; 
            margin: 10px 0; 
        }
        .tab-content.active { display: block; }
        .debug-section {
            background: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <h1>Minimal Event Management Test</h1>
    <h2>Event: <?php echo htmlspecialchars($event['name']); ?></h2>
    
    <!-- Tab Navigation -->
    <div>
        <button class="tab-button active" data-tab="standings" onclick="showTab('standings', this)">
            Standings
        </button>
        <button class="tab-button" data-tab="sports" onclick="showTab('sports', this)">
            Sports Management
        </button>
        <button class="tab-button" data-tab="registrations" onclick="showTab('registrations', this)">
            Registrations
        </button>
        <button class="tab-button" data-tab="matches" onclick="showTab('matches', this)">
            Recent Matches
        </button>
    </div>

    <!-- Tab Contents -->
    <div id="standings-tab" class="tab-content active">
        <h3>Current Standings</h3>
        <p>Standings content for event: <?php echo htmlspecialchars($event['name']); ?></p>
    </div>

    <div id="sports-tab" class="tab-content">
        <h3>Sports Management</h3>
        <p>Sports management content goes here.</p>
    </div>

    <div id="registrations-tab" class="tab-content">
        <h3>Registrations</h3>
        <p>Registration content goes here.</p>
    </div>

    <div id="matches-tab" class="tab-content">
        <h3>Recent Matches</h3>
        <p>Recent matches content goes here.</p>
    </div>

    <!-- Debug Section -->
    <div class="debug-section">
        <h3>Debug Controls</h3>
        <button onclick="window.testTabClick('standings')">Test Standings</button>
        <button onclick="window.testTabClick('sports')">Test Sports</button>
        <button onclick="window.testTabClick('registrations')">Test Registrations</button>
        <button onclick="window.testTabClick('matches')">Test Matches</button>
        <button onclick="window.testAllTabs()">Test All Tabs</button>
        <br><br>
        <button onclick="console.log('showTab type:', typeof window.showTab)">Check showTab Type</button>
        <button onclick="console.log('testTabClick type:', typeof window.testTabClick)">Check testTabClick Type</button>
    </div>

    <script>
        // IMMEDIATE EXECUTION - NO DOM READY WAIT
        console.log('=== SCRIPT LOADING STARTED ===');
        
        // Define showTab function immediately in global scope
        window.showTab = function(tabName, clickedButton) {
            console.log('=== showTab called ===', tabName, clickedButton);

            try {
                // Hide all tab contents
                const allContents = document.querySelectorAll('.tab-content');
                console.log('Found tab contents:', allContents.length);
                allContents.forEach(content => {
                    content.classList.remove('active');
                    console.log('Hiding:', content.id);
                });

                // Remove active class from all tab buttons
                const allButtons = document.querySelectorAll('.tab-button');
                console.log('Found tab buttons:', allButtons.length);
                allButtons.forEach(button => {
                    button.classList.remove('active');
                });

                // Show selected tab content
                const targetTab = document.getElementById(tabName + '-tab');
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log('SUCCESS: Showing tab:', targetTab.id);
                } else {
                    console.error('ERROR: Tab not found:', tabName + '-tab');
                    return false;
                }

                // Add active class to clicked button
                if (clickedButton) {
                    clickedButton.classList.add('active');
                    console.log('SUCCESS: Activated button');
                } else {
                    const button = document.querySelector(`[data-tab="${tabName}"]`);
                    if (button) {
                        button.classList.add('active');
                        console.log('SUCCESS: Found and activated button by data-tab');
                    }
                }
                
                console.log('SUCCESS: Tab switch completed');
                return true;
                
            } catch (error) {
                console.error('ERROR in showTab:', error);
                return false;
            }
        };
        
        // Define test functions immediately
        window.testTabClick = function(tabName) {
            console.log('=== testTabClick called ===', tabName);
            try {
                const button = document.querySelector(`[data-tab="${tabName}"]`);
                console.log('Found button:', button);
                return window.showTab(tabName, button);
            } catch (error) {
                console.error('ERROR in testTabClick:', error);
                return false;
            }
        };
        
        window.testAllTabs = function() {
            console.log('=== testAllTabs called ===');
            try {
                const tabs = ['standings', 'sports', 'registrations', 'matches'];
                let index = 0;
                
                function testNext() {
                    if (index < tabs.length) {
                        console.log(`Testing tab ${index + 1}: ${tabs[index]}`);
                        const button = document.querySelector(`[data-tab="${tabs[index]}"]`);
                        window.showTab(tabs[index], button);
                        index++;
                        setTimeout(testNext, 1000);
                    } else {
                        console.log('All tabs tested successfully');
                    }
                }

                testNext();
            } catch (error) {
                console.error('ERROR in testAllTabs:', error);
            }
        };
        
        console.log('=== FUNCTIONS DEFINED ===');
        console.log('showTab type:', typeof window.showTab);
        console.log('testTabClick type:', typeof window.testTabClick);
        console.log('testAllTabs type:', typeof window.testAllTabs);
        console.log('=== SCRIPT LOADING COMPLETED ===');
    </script>
</body>
</html>
