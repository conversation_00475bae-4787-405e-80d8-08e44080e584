<?php
/**
 * Direct Matches Table Fix
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script directly adds the missing tournament columns to the matches table
 * with proper error handling and verification.
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Direct Matches Table Fix</h1>";
echo "<p>Directly adding missing tournament columns to matches table...</p>";

try {
    echo "<h2>🔍 Step 1: Current Matches Table Structure</h2>";
    
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<h3>Current Columns:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>🔧 Step 2: Add Missing Tournament Columns</h2>";
    
    $changes_made = [];
    $required_columns = [
        'tournament_structure_id' => 'INT NULL',
        'tournament_round_id' => 'INT NULL',
        'bracket_position' => 'VARCHAR(50) NULL',
        'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
    ];
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            echo "<p>Adding column: {$column_name}...</p>";
            
            try {
                $sql = "ALTER TABLE matches ADD COLUMN {$column_name} {$column_definition}";
                echo "<p><strong>SQL:</strong> {$sql}</p>";
                $conn->exec($sql);
                $changes_made[] = $column_name;
                echo "<p style='color: green; font-weight: bold;'>✅ Successfully added {$column_name}</p>";
            } catch (Exception $e) {
                echo "<p style='color: red; font-weight: bold;'>❌ Failed to add {$column_name}: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ Column {$column_name} already exists</p>";
        }
    }
    
    echo "<h2>🔍 Step 3: Verify Changes</h2>";
    
    // Re-check the table structure
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $updated_columns = $stmt->fetchAll();
    
    echo "<h3>Updated Matches Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $final_columns = [];
    foreach ($updated_columns as $column) {
        $final_columns[] = $column['Field'];
        $highlight = in_array($column['Field'], array_keys($required_columns)) ? 'background: #d4edda; font-weight: bold;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><em>Tournament columns are highlighted in green.</em></p>";
    
    // Verify all required columns exist
    $all_columns_exist = true;
    $missing_columns = [];
    
    foreach (array_keys($required_columns) as $required_col) {
        if (!in_array($required_col, $final_columns)) {
            $all_columns_exist = false;
            $missing_columns[] = $required_col;
        }
    }
    
    if ($all_columns_exist) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>✅ <strong>MATCHES TABLE FIX SUCCESSFUL!</strong></h3>";
        echo "<p style='font-size: 18px;'>All required tournament columns have been added to the matches table.</p>";
        
        if (!empty($changes_made)) {
            echo "<h4>Changes Made:</h4>";
            echo "<ul>";
            foreach ($changes_made as $column) {
                echo "<li style='color: #155724; font-weight: bold;'>Added column: {$column}</li>";
            }
            echo "</ul>";
        }
        
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='test-tournament-creation-now.php' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block; margin-right: 10px;'>🧪 TEST TOURNAMENT CREATION</a>";
        echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #007bff; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE TOURNAMENT</a>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>SOME COLUMNS STILL MISSING!</strong></h3>";
        echo "<p>The following columns are still missing:</p>";
        echo "<ul>";
        foreach ($missing_columns as $column) {
            echo "<li style='color: #721c24; font-weight: bold;'>{$column}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h2>🔍 Step 4: Test Column Access</h2>";
    
    // Test if we can actually use the tournament_structure_id column
    try {
        echo "<p>Testing tournament_structure_id column access...</p>";
        $stmt = $conn->prepare("SELECT tournament_structure_id FROM matches LIMIT 1");
        $stmt->execute();
        echo "<p style='color: green; font-weight: bold;'>✅ tournament_structure_id column is accessible</p>";
    } catch (Exception $e) {
        echo "<p style='color: red; font-weight: bold;'>❌ tournament_structure_id column access failed: " . $e->getMessage() . "</p>";
    }
    
    // Test if we can insert with tournament columns
    try {
        echo "<p>Testing insert with tournament columns...</p>";
        $stmt = $conn->prepare("
            INSERT INTO matches 
            (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        // Use test values that won't conflict
        $stmt->execute([999, 999, 999, 1, 1, 'TEST', 0, 'scheduled']);
        
        // Delete the test record
        $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = 999 AND team1_id = 999");
        $stmt->execute();
        
        echo "<p style='color: green; font-weight: bold;'>✅ Insert with tournament columns works</p>";
    } catch (Exception $e) {
        echo "<p style='color: red; font-weight: bold;'>❌ Insert test failed: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>CRITICAL ERROR!</strong></h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>🔍 Step 5: Database Permissions Check</h2>";

try {
    // Check if we have ALTER privileges
    $stmt = $conn->prepare("SHOW GRANTS");
    $stmt->execute();
    $grants = $stmt->fetchAll();
    
    echo "<h3>Current Database Privileges:</h3>";
    echo "<ul>";
    foreach ($grants as $grant) {
        echo "<li>" . $grant[array_keys($grant)[0]] . "</li>";
    }
    echo "</ul>";
} catch (Exception $e) {
    echo "<p style='color: orange;'>⚠ Could not check database privileges: " . $e->getMessage() . "</p>";
}

echo "<h3>🔧 Manual Fix Instructions</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 10px 0;'>";
echo "<h4>If the automatic fix didn't work, run these SQL commands manually:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px;'>";
echo "ALTER TABLE matches ADD COLUMN tournament_structure_id INT NULL;\n";
echo "ALTER TABLE matches ADD COLUMN tournament_round_id INT NULL;\n";
echo "ALTER TABLE matches ADD COLUMN bracket_position VARCHAR(50) NULL;\n";
echo "ALTER TABLE matches ADD COLUMN is_bye_match BOOLEAN DEFAULT FALSE;\n";
echo "</pre>";
echo "<p><strong>Run these in phpMyAdmin or your MySQL client.</strong></p>";
echo "</div>";

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='direct-matches-table-fix.php'>🔄 Run This Fix Again</a></li>";
echo "<li><a href='comprehensive-schema-analysis.php'>🔍 Run Schema Analysis</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Try Tournament Creation</a></li>";
echo "</ul>";
?>
