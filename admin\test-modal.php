<?php
require_once 'auth.php';
requireAdmin();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Modal Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Modal Test</h1>
    
    <button onclick="testDepartmentEdit()">Test Department Edit</button>
    
    <div id="result"></div>
    
    <script>
        function testDepartmentEdit() {
            console.log('Testing department edit...');
            
            // Test 1: Check if get-department.php works
            fetch('ajax/get-department.php?id=6')
                .then(response => {
                    console.log('Get department response status:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('Get department raw response:', text);
                    try {
                        const data = JSON.parse(text);
                        console.log('Get department parsed data:', data);
                        
                        if (data.success) {
                            // Test 2: Try to submit update
                            testDepartmentUpdate(data.department);
                        }
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        document.getElementById('result').innerHTML = 'Get department failed: ' + text;
                    }
                })
                .catch(error => {
                    console.error('Get department error:', error);
                    document.getElementById('result').innerHTML = 'Get department error: ' + error.message;
                });
        }
        
        function testDepartmentUpdate(department) {
            console.log('Testing department update with:', department);
            
            const formData = new FormData();
            formData.append('entity', 'department');
            formData.append('action', 'update');
            formData.append('id', department.id);
            formData.append('name', department.name);
            formData.append('abbreviation', department.abbreviation);
            formData.append('color_code', department.color_code || '#3498db');
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
            
            fetch('ajax/modal-handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Update response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Update raw response:', text);
                try {
                    const data = JSON.parse(text);
                    console.log('Update parsed data:', data);
                    document.getElementById('result').innerHTML = 'Update result: ' + JSON.stringify(data);
                } catch (e) {
                    console.error('JSON parse error:', e);
                    document.getElementById('result').innerHTML = 'Update failed: ' + text;
                }
            })
            .catch(error => {
                console.error('Update error:', error);
                document.getElementById('result').innerHTML = 'Update error: ' + error.message;
            });
        }
    </script>
</body>
</html>
