<?php
/**
 * AJAX Handler for Department-Centric Registration System
 * Handles all department registration and multi-sport participation operations
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/department_registration.php';

// Require admin authentication
requireAdmin();

// Verify CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
    exit;
}

$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'register_department_for_event':
            $event_id = (int)$_POST['event_id'];
            $department_id = (int)$_POST['department_id'];
            
            $data = [
                'status' => $_POST['status'] ?? 'pending',
                'contact_person' => $_POST['contact_person'] ?? null,
                'contact_email' => $_POST['contact_email'] ?? null,
                'contact_phone' => $_POST['contact_phone'] ?? null,
                'notes' => $_POST['notes'] ?? null,
                'total_participants' => (int)($_POST['total_participants'] ?? 0)
            ];
            
            $result = registerDepartmentForEvent($conn, $event_id, $department_id, $data);
            
            if ($result['success']) {
                logAdminActivity('REGISTER_DEPT_EVENT', 'department_registration', $result['registration_id']);
                echo json_encode([
                    'success' => true, 
                    'message' => 'Department registered for event successfully',
                    'registration_id' => $result['registration_id']
                ]);
            } else {
                echo json_encode($result);
            }
            break;
            
        case 'add_sport_participation':
            $registration_id = (int)$_POST['registration_id'];
            $event_sport_id = (int)$_POST['event_sport_id'];
            
            $data = [
                'team_name' => $_POST['team_name'] ?? null,
                'participants' => $_POST['participants'] ?? '',
                'status' => $_POST['status'] ?? 'registered',
                'notes' => $_POST['notes'] ?? null
            ];
            
            $result = addDepartmentSportParticipation($conn, $registration_id, $event_sport_id, $data);
            
            if ($result['success']) {
                logAdminActivity('ADD_SPORT_PARTICIPATION', 'sport_participation', $result['participation_id']);
                echo json_encode([
                    'success' => true, 
                    'message' => 'Sport participation added successfully',
                    'participation_id' => $result['participation_id']
                ]);
            } else {
                echo json_encode($result);
            }
            break;
            
        case 'remove_sport_participation':
            $participation_id = (int)$_POST['participation_id'];
            
            $result = removeDepartmentSportParticipation($conn, $participation_id);
            
            if ($result['success']) {
                logAdminActivity('REMOVE_SPORT_PARTICIPATION', 'sport_participation', $participation_id);
            }
            
            echo json_encode($result);
            break;
            
        case 'update_registration_status':
            $registration_id = (int)$_POST['registration_id'];
            $status = $_POST['status'];
            
            $result = updateDepartmentRegistrationStatus($conn, $registration_id, $status);
            
            if ($result['success']) {
                logAdminActivity('UPDATE_DEPT_REG_STATUS', 'department_registration', $registration_id);
            }
            
            echo json_encode($result);
            break;
            
        case 'record_sport_score':
            $event_id = (int)$_POST['event_id'];
            $department_id = (int)$_POST['department_id'];
            $sport_id = (int)$_POST['sport_id'];
            $position = (int)$_POST['position'];
            
            $additional_data = [
                'penalty_points' => (float)($_POST['penalty_points'] ?? 0),
                'performance_data' => $_POST['performance_data'] ?? []
            ];
            
            $result = recordDepartmentSportScore($conn, $event_id, $department_id, $sport_id, $position, $additional_data);
            
            if ($result['success']) {
                logAdminActivity('RECORD_SPORT_SCORE', 'sport_score', null);
                echo json_encode([
                    'success' => true, 
                    'message' => 'Sport score recorded successfully',
                    'points_earned' => $result['points_earned']
                ]);
            } else {
                echo json_encode($result);
            }
            break;
            
        case 'calculate_overall_standings':
            $event_id = (int)$_POST['event_id'];
            
            $result = calculateOverallStandings($conn, $event_id);
            
            if ($result['success']) {
                logAdminActivity('CALCULATE_STANDINGS', 'event', $event_id);
                echo json_encode([
                    'success' => true, 
                    'message' => 'Overall standings calculated successfully',
                    'standings_count' => $result['standings_count']
                ]);
            } else {
                echo json_encode($result);
            }
            break;
            
        case 'get_department_registrations':
            $event_id = (int)$_POST['event_id'];
            $registrations = getEventDepartmentRegistrations($conn, $event_id);
            
            echo json_encode([
                'success' => true,
                'registrations' => $registrations
            ]);
            break;
            
        case 'get_sport_participations':
            $registration_id = (int)$_POST['registration_id'];
            $participations = getDepartmentSportParticipations($conn, $registration_id);
            
            echo json_encode([
                'success' => true,
                'participations' => $participations
            ]);
            break;
            
        case 'get_available_sports':
            $event_id = (int)$_POST['event_id'];
            $department_id = (int)$_POST['department_id'];
            $sports = getAvailableSportsForDepartment($conn, $event_id, $department_id);
            
            echo json_encode([
                'success' => true,
                'sports' => $sports
            ]);
            break;
            
        case 'get_overall_standings':
            $event_id = (int)$_POST['event_id'];
            $standings = getOverallStandings($conn, $event_id);
            
            echo json_encode([
                'success' => true,
                'standings' => $standings
            ]);
            break;
            
        case 'get_department_scores_breakdown':
            $event_id = (int)$_POST['event_id'];
            $department_id = (int)$_POST['department_id'];
            $scores = getDepartmentScoresBreakdown($conn, $event_id, $department_id);
            
            echo json_encode([
                'success' => true,
                'scores' => $scores
            ]);
            break;
            
        case 'get_registration_stats':
            $event_id = (int)$_POST['event_id'];
            $stats = getDepartmentRegistrationStats($conn, $event_id);
            
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'Invalid action specified'
            ]);
            break;
    }
    
} catch (Exception $e) {
    error_log("Department Registration AJAX Error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred: ' . $e->getMessage()
    ]);
}
?>
