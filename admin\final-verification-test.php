<?php
/**
 * Final Verification Test
 * SC_IMS Sports Competition and Event Management System
 * 
 * Final test to confirm both issues are completely resolved
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Verification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .final-result { padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center; }
        .final-success { background: linear-gradient(135deg, #d4edda, #c3e6cb); border: 2px solid #28a745; }
        .final-error { background: linear-gradient(135deg, #f8d7da, #f5c6cb); border: 2px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏁 Final Verification Test</h1>
        <p><strong>Verification Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="highlight">
            <h3>🎯 Final Verification Objectives</h3>
            <p>This final test verifies that both critical issues have been completely resolved:</p>
            <ul>
                <li>✅ <strong>Database Error:</strong> No more "tournament_structure_id" column errors</li>
                <li>✅ <strong>Format Consistency:</strong> Tournament formats are synchronized across the system</li>
            </ul>
        </div>
        
        <?php
        $all_tests_passed = true;
        $test_results = [];
        
        try {
            // Test 1: Database Schema Verification
            echo '<div class="step">';
            echo '<h2>Test 1: Database Schema Verification</h2>';
            
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            $schema_test_passed = true;
            
            foreach ($required_columns as $column) {
                if (in_array($column, $columns)) {
                    echo '<p>✅ ' . $column . ' column exists</p>';
                } else {
                    echo '<p>❌ ' . $column . ' column missing</p>';
                    $schema_test_passed = false;
                }
            }
            
            $test_results['schema'] = $schema_test_passed;
            if (!$schema_test_passed) $all_tests_passed = false;
            
            echo '</div>';
            
            // Test 2: Tournament Tables Verification
            echo '<div class="step">';
            echo '<h2>Test 2: Tournament Tables Verification</h2>';
            
            $tournament_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
            $tables_test_passed = true;
            
            foreach ($tournament_tables as $table) {
                $stmt = $conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if ($stmt->fetch()) {
                    echo '<p>✅ ' . $table . ' table exists</p>';
                } else {
                    echo '<p>❌ ' . $table . ' table missing</p>';
                    $tables_test_passed = false;
                }
            }
            
            $test_results['tables'] = $tables_test_passed;
            if (!$tables_test_passed) $all_tests_passed = false;
            
            echo '</div>';
            
            // Test 3: Format Consistency Verification
            echo '<div class="step">';
            echo '<h2>Test 3: Format Consistency Verification</h2>';
            
            // Check for format inconsistencies
            $stmt = $conn->prepare("
                SELECT COUNT(*) as inconsistencies
                FROM sport_categories sc
                LEFT JOIN event_sports es ON sc.event_sport_id = es.id
                LEFT JOIN sports s ON es.sport_id = s.id
                WHERE sc.tournament_format IS NOT NULL 
                AND s.bracket_format IS NOT NULL
                AND sc.tournament_format != s.bracket_format
            ");
            $stmt->execute();
            $inconsistency_count = $stmt->fetchColumn();
            
            $format_test_passed = ($inconsistency_count == 0);
            
            if ($format_test_passed) {
                echo '<p>✅ No format inconsistencies found</p>';
            } else {
                echo '<p>❌ ' . $inconsistency_count . ' format inconsistencies still exist</p>';
            }
            
            $test_results['format_consistency'] = $format_test_passed;
            if (!$format_test_passed) $all_tests_passed = false;
            
            echo '</div>';
            
            // Test 4: Tournament Creation Test
            echo '<div class="step">';
            echo '<h2>Test 4: Tournament Creation Test</h2>';
            
            $tournament_test_passed = false;
            
            if ($schema_test_passed && $tables_test_passed) {
                try {
                    // Include tournament manager
                    require_once '../includes/tournament_manager.php';
                    echo '<p>✅ Tournament manager loaded</p>';
                    
                    // Get or create test data
                    $stmt = $conn->prepare("SELECT id FROM events LIMIT 1");
                    $stmt->execute();
                    $event_id = $stmt->fetchColumn();
                    
                    if (!$event_id) {
                        $stmt = $conn->prepare("INSERT INTO events (name, description, start_date, end_date, status) VALUES (?, ?, ?, ?, ?)");
                        $stmt->execute(['Final Test Event', 'Final verification event', date('Y-m-d'), date('Y-m-d', strtotime('+7 days')), 'active']);
                        $event_id = $conn->lastInsertId();
                    }
                    
                    $stmt = $conn->prepare("SELECT id FROM sports LIMIT 1");
                    $stmt->execute();
                    $sport_id = $stmt->fetchColumn();
                    
                    if (!$sport_id) {
                        $stmt = $conn->prepare("INSERT INTO sports (name, type, scoring_method, bracket_format) VALUES (?, ?, ?, ?)");
                        $stmt->execute(['Final Test Sport', 'traditional', 'point_based', 'single_elimination']);
                        $sport_id = $conn->lastInsertId();
                    }
                    
                    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
                    $stmt->execute([$event_id, $sport_id]);
                    $event_sport_id = $stmt->fetchColumn();
                    
                    if (!$event_sport_id) {
                        $stmt = $conn->prepare("INSERT INTO event_sports (event_id, sport_id, status) VALUES (?, ?, ?)");
                        $stmt->execute([$event_id, $sport_id, 'active']);
                        $event_sport_id = $conn->lastInsertId();
                    }
                    
                    // Ensure registrations exist
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM registrations WHERE event_sport_id = ?");
                    $stmt->execute([$event_sport_id]);
                    $reg_count = $stmt->fetchColumn();
                    
                    if ($reg_count < 4) {
                        // Create departments if needed
                        for ($i = 1; $i <= 4; $i++) {
                            $stmt = $conn->prepare("INSERT IGNORE INTO departments (name, abbreviation, contact_info) VALUES (?, ?, ?)");
                            $stmt->execute(["Final Test Dept $i", "FTD$i", "Contact $i"]);
                        }
                        
                        // Create registrations
                        $stmt = $conn->prepare("SELECT id FROM departments LIMIT 4");
                        $stmt->execute();
                        $departments = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        foreach ($departments as $dept_id) {
                            $stmt = $conn->prepare("INSERT IGNORE INTO registrations (event_sport_id, department_id, team_name, status) VALUES (?, ?, ?, ?)");
                            $stmt->execute([$event_sport_id, $dept_id, "Team $dept_id", 'confirmed']);
                        }
                    }
                    
                    // Get tournament format
                    $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = 'single_elimination' LIMIT 1");
                    $stmt->execute();
                    $format_id = $stmt->fetchColumn();
                    
                    if ($format_id) {
                        // Create tournament manager and test tournament creation
                        $tournamentManager = new TournamentManager($conn);
                        
                        $config = [
                            'seeding_method' => 'random',
                            'scoring_config' => [
                                'points_win' => 3,
                                'points_draw' => 1,
                                'points_loss' => 0
                            ]
                        ];
                        
                        $tournament_name = "Final Verification Tournament " . date('H:i:s');
                        $tournament_id = $tournamentManager->createTournament($event_sport_id, $format_id, $tournament_name, $config);
                        
                        if ($tournament_id) {
                            echo '<p>✅ Tournament created successfully (ID: ' . $tournament_id . ')</p>';
                            
                            // Verify matches were created with tournament columns
                            $stmt = $conn->prepare("SELECT COUNT(*) FROM matches WHERE tournament_structure_id = ?");
                            $stmt->execute([$tournament_id]);
                            $match_count = $stmt->fetchColumn();
                            
                            if ($match_count > 0) {
                                echo '<p>✅ Tournament matches created (' . $match_count . ' matches)</p>';
                                $tournament_test_passed = true;
                            } else {
                                echo '<p>❌ No tournament matches were created</p>';
                            }
                        } else {
                            echo '<p>❌ Tournament creation returned null</p>';
                        }
                    } else {
                        echo '<p>❌ No tournament format found</p>';
                    }
                    
                } catch (Exception $e) {
                    echo '<p>❌ Tournament creation failed: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '<p>Error details: ' . htmlspecialchars($e->getFile()) . ':' . $e->getLine() . '</p>';
                }
            } else {
                echo '<p>⚠️ Skipping tournament creation test due to schema/table issues</p>';
            }
            
            $test_results['tournament_creation'] = $tournament_test_passed;
            if (!$tournament_test_passed) $all_tests_passed = false;
            
            echo '</div>';
            
            // Test 5: SQL INSERT Test
            echo '<div class="step">';
            echo '<h2>Test 5: Direct SQL INSERT Test</h2>';
            
            $sql_test_passed = false;
            
            if ($schema_test_passed) {
                try {
                    $test_sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'FINAL_TEST', 0, 'test')";
                    $stmt = $conn->prepare($test_sql);
                    $stmt->execute();
                    $test_id = $conn->lastInsertId();
                    
                    if ($test_id) {
                        echo '<p>✅ Direct SQL INSERT test successful (Test ID: ' . $test_id . ')</p>';
                        
                        // Clean up test record
                        $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
                        echo '<p>✅ Test record cleaned up</p>';
                        
                        $sql_test_passed = true;
                    }
                    
                } catch (Exception $e) {
                    echo '<p>❌ Direct SQL INSERT test failed: ' . htmlspecialchars($e->getMessage()) . '</p>';
                }
            } else {
                echo '<p>⚠️ Skipping SQL test due to schema issues</p>';
            }
            
            $test_results['sql_insert'] = $sql_test_passed;
            if (!$sql_test_passed) $all_tests_passed = false;
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Verification error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $all_tests_passed = false;
        }
        ?>
        
        <!-- Final Results -->
        <div class="<?php echo $all_tests_passed ? 'final-result final-success' : 'final-result final-error'; ?>">
            <?php if ($all_tests_passed): ?>
                <h2>🎉 ALL ISSUES COMPLETELY RESOLVED! 🎉</h2>
                <h3>✅ BOTH CRITICAL ISSUES HAVE BEEN PERMANENTLY FIXED</h3>
                <p><strong>1. Database Error:</strong> ✅ "tournament_structure_id" column error is completely resolved</p>
                <p><strong>2. Format Inconsistency:</strong> ✅ Tournament formats are now synchronized across the system</p>
                <p><strong>Your sports competition management system is now fully functional for tournament creation!</strong></p>
            <?php else: ?>
                <h2>❌ Some Issues Still Remain</h2>
                <p>The verification test found that some issues are not yet fully resolved.</p>
                <p>Please review the test results above and apply additional fixes as needed.</p>
            <?php endif; ?>
        </div>
        
        <!-- Detailed Test Results -->
        <div class="step">
            <h2>📊 Detailed Test Results</h2>
            <table>
                <tr><th>Test</th><th>Result</th><th>Status</th></tr>
                <tr style="background: <?php echo ($test_results['schema'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Database Schema</td>
                    <td><?php echo ($test_results['schema'] ?? false) ? 'PASS' : 'FAIL'; ?></td>
                    <td><?php echo ($test_results['schema'] ?? false) ? '✅' : '❌'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['tables'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Tournament Tables</td>
                    <td><?php echo ($test_results['tables'] ?? false) ? 'PASS' : 'FAIL'; ?></td>
                    <td><?php echo ($test_results['tables'] ?? false) ? '✅' : '❌'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['format_consistency'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Format Consistency</td>
                    <td><?php echo ($test_results['format_consistency'] ?? false) ? 'PASS' : 'FAIL'; ?></td>
                    <td><?php echo ($test_results['format_consistency'] ?? false) ? '✅' : '❌'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['tournament_creation'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Tournament Creation</td>
                    <td><?php echo ($test_results['tournament_creation'] ?? false) ? 'PASS' : 'FAIL'; ?></td>
                    <td><?php echo ($test_results['tournament_creation'] ?? false) ? '✅' : '❌'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['sql_insert'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>SQL INSERT Test</td>
                    <td><?php echo ($test_results['sql_insert'] ?? false) ? 'PASS' : 'FAIL'; ?></td>
                    <td><?php echo ($test_results['sql_insert'] ?? false) ? '✅' : '❌'; ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Next Steps -->
        <div class="step">
            <h2>🚀 Next Steps</h2>
            <?php if ($all_tests_passed): ?>
                <div class="success">
                    <h3>🎯 Your System is Ready!</h3>
                    <p>You can now use your sports competition management system without any issues:</p>
                    <ul>
                        <li>✅ Create tournaments without database errors</li>
                        <li>✅ Tournament formats will display consistently</li>
                        <li>✅ All tournament functionality is working correctly</li>
                    </ul>
                </div>
                <p>
                    <a href="manage-event.php?id=1" class="btn btn-success">📋 Start Managing Events</a>
                    <a href="index.php" class="btn">🏠 Admin Dashboard</a>
                </p>
            <?php else: ?>
                <div class="warning">
                    <h3>🔧 Additional Fixes Needed</h3>
                    <p>Some issues still need to be resolved. Try these actions:</p>
                </div>
                <p>
                    <a href="emergency-database-fix.php" class="btn btn-warning">🚨 Re-run Emergency Fix</a>
                    <a href="format-synchronization-fix.php" class="btn btn-warning">🔄 Re-run Format Fix</a>
                    <a href="critical-database-investigation.php" class="btn">🔍 Investigate Further</a>
                </p>
            <?php endif; ?>
        </div>
        
        <!-- Summary -->
        <div class="info">
            <h3>📋 What Was Fixed</h3>
            <p>This comprehensive solution addressed both critical issues:</p>
            <ul>
                <li><strong>Database Schema Issue:</strong> Added missing tournament columns to matches table and ensured all tournament tables exist</li>
                <li><strong>Format Inconsistency:</strong> Synchronized tournament formats across sports and categories using standardized rules</li>
                <li><strong>Permanent Solution:</strong> Updated database initialization to prevent future occurrences</li>
                <li><strong>Comprehensive Testing:</strong> Verified the fix works end-to-end with actual tournament creation</li>
            </ul>
        </div>
    </div>
</body>
</html>
