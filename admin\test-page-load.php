<?php
/**
 * Simple test page to check if PHP is working correctly
 */

echo "PHP is working!<br>";
echo "Current time: " . date('Y-m-d H:i:s') . "<br>";

// Test database connection
try {
    require_once '../config/database.php';
    $database = new Database();
    $conn = $database->getConnection();
    echo "Database connection: OK<br>";
} catch (Exception $e) {
    echo "Database connection error: " . $e->getMessage() . "<br>";
}

// Test auth functions
try {
    require_once 'auth.php';
    echo "Auth file loaded: OK<br>";
    
    if (function_exists('getCurrentAdmin')) {
        echo "getCurrentAdmin function exists: OK<br>";
    } else {
        echo "getCurrentAdmin function missing: ERROR<br>";
    }
    
    if (function_exists('requireAdmin')) {
        echo "requireAdmin function exists: OK<br>";
    } else {
        echo "requireAdmin function missing: ERROR<br>";
    }
} catch (Exception $e) {
    echo "Auth error: " . $e->getMessage() . "<br>";
}

// Test functions file
try {
    require_once '../includes/functions.php';
    echo "Functions file loaded: OK<br>";
    
    if (function_exists('getEventById')) {
        echo "getEventById function exists: OK<br>";
    } else {
        echo "getEventById function missing: ERROR<br>";
    }
} catch (Exception $e) {
    echo "Functions error: " . $e->getMessage() . "<br>";
}

echo "<br>Test completed successfully!";
?>
