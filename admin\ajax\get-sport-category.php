<?php
/**
 * Get Sport Category Data for Modal Editing
 * SC_IMS Admin Panel AJAX Endpoint
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get category ID
$category_id = $_GET['id'] ?? 0;

if (!$category_id) {
    echo json_encode([
        'success' => false,
        'message' => 'Category ID is required'
    ]);
    exit;
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Fetch category data
    $stmt = $conn->prepare("SELECT * FROM sport_categories WHERE id = ?");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        echo json_encode([
            'success' => false,
            'message' => 'Category not found'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'category' => $category
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching category data: ' . $e->getMessage()
    ]);
}
?>
