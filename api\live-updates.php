<?php
/**
 * Live Updates API (Server-Sent Events) for SC_IMS
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Set headers for Server-Sent Events
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Prevent timeout
set_time_limit(0);
ini_set('max_execution_time', 0);

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Track last update time
$last_update = $_GET['last_update'] ?? 0;

// Function to send SSE data
function sendSSEData($data, $event = 'update') {
    echo "event: $event\n";
    echo "data: " . json_encode($data) . "\n\n";
    ob_flush();
    flush();
}

// Function to send heartbeat
function sendHeartbeat() {
    echo "event: heartbeat\n";
    echo "data: " . json_encode(['timestamp' => time()]) . "\n\n";
    ob_flush();
    flush();
}

try {
    // Initial data send
    $current_time = time();
    
    // Get live matches
    $live_matches = getLiveMatches($conn);
    
    // Get recent results (last 5 minutes)
    $sql = "SELECT m.*, 
            es.event_id, e.name as event_name,
            s.name as sport_name,
            d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color,
            dw.name as winner_name, dw.abbreviation as winner_abbr
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            LEFT JOIN registrations rw ON m.winner_id = rw.id
            LEFT JOIN departments dw ON rw.department_id = dw.id
            WHERE m.status = 'completed' AND m.actual_end_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY m.actual_end_time DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $recent_results = $stmt->fetchAll();
    
    // Get current rankings for ongoing events
    $ongoing_events = getAllEvents($conn, 'ongoing');
    $current_rankings = [];
    foreach ($ongoing_events as $event) {
        $rankings = getEventRankings($conn, $event['id']);
        if (!empty($rankings)) {
            $current_rankings[$event['id']] = [
                'event' => $event,
                'rankings' => array_slice($rankings, 0, 5) // Top 5
            ];
        }
    }
    
    // Send initial data
    sendSSEData([
        'type' => 'initial',
        'live_matches' => $live_matches,
        'recent_results' => $recent_results,
        'rankings' => $current_rankings,
        'timestamp' => $current_time
    ], 'initial');
    
    // Keep connection alive and send updates
    $heartbeat_counter = 0;
    
    while (true) {
        // Check for new updates every 5 seconds
        sleep(5);
        $heartbeat_counter++;
        
        // Send heartbeat every 30 seconds
        if ($heartbeat_counter % 6 == 0) {
            sendHeartbeat();
        }
        
        // Check for updates
        $has_updates = false;
        $update_data = [];
        
        // Check for new live matches
        $new_live_matches = getLiveMatches($conn);
        if (json_encode($new_live_matches) !== json_encode($live_matches)) {
            $live_matches = $new_live_matches;
            $update_data['live_matches'] = $live_matches;
            $has_updates = true;
        }
        
        // Check for new completed matches
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $new_recent_results = $stmt->fetchAll();
        if (json_encode($new_recent_results) !== json_encode($recent_results)) {
            $recent_results = $new_recent_results;
            $update_data['recent_results'] = $recent_results;
            $has_updates = true;
        }
        
        // Check for ranking updates
        $new_rankings = [];
        foreach ($ongoing_events as $event) {
            $rankings = getEventRankings($conn, $event['id']);
            if (!empty($rankings)) {
                $new_rankings[$event['id']] = [
                    'event' => $event,
                    'rankings' => array_slice($rankings, 0, 5)
                ];
            }
        }
        if (json_encode($new_rankings) !== json_encode($current_rankings)) {
            $current_rankings = $new_rankings;
            $update_data['rankings'] = $current_rankings;
            $has_updates = true;
        }
        
        // Send updates if any
        if ($has_updates) {
            $update_data['type'] = 'update';
            $update_data['timestamp'] = time();
            sendSSEData($update_data, 'update');
        }
        
        // Check if client disconnected
        if (connection_aborted()) {
            break;
        }
        
        // Prevent infinite loop in case of errors
        if ($heartbeat_counter > 720) { // 1 hour max
            break;
        }
    }
    
} catch (Exception $e) {
    sendSSEData([
        'type' => 'error',
        'message' => 'Connection error: ' . $e->getMessage(),
        'timestamp' => time()
    ], 'error');
}
?>
