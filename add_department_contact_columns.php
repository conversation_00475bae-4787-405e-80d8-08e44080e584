<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "Adding contact columns to departments table...\n";
    
    // Check if columns already exist
    $result = $conn->query("SHOW COLUMNS FROM departments LIKE 'contact_person'");
    if ($result->rowCount() == 0) {
        // Add contact_person column
        $sql = "ALTER TABLE departments ADD COLUMN contact_person VARCHAR(255) AFTER contact_info";
        $conn->exec($sql);
        echo "✅ Added contact_person column\n";
    } else {
        echo "✅ contact_person column already exists\n";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM departments LIKE 'contact_email'");
    if ($result->rowCount() == 0) {
        // Add contact_email column
        $sql = "ALTER TABLE departments ADD COLUMN contact_email VARCHAR(255) AFTER contact_person";
        $conn->exec($sql);
        echo "✅ Added contact_email column\n";
    } else {
        echo "✅ contact_email column already exists\n";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM departments LIKE 'contact_phone'");
    if ($result->rowCount() == 0) {
        // Add contact_phone column
        $sql = "ALTER TABLE departments ADD COLUMN contact_phone VARCHAR(20) AFTER contact_email";
        $conn->exec($sql);
        echo "✅ Added contact_phone column\n";
    } else {
        echo "✅ contact_phone column already exists\n";
    }
    
    // Show current table structure
    echo "\nCurrent departments table structure:\n";
    $result = $conn->query("DESCRIBE departments");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "- {$row['Field']}: {$row['Type']}\n";
    }
    
    echo "\n✅ Department contact columns setup complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
