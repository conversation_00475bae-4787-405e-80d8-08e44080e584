<?php
/**
 * Sport Category Management for SC_IMS Admin Panel - Modal Interface
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;

if (!$event_id || !$sport_id) {
    header('Location: events.php');
    exit;
}

// Get event and sport information
$stmt = $conn->prepare("
    SELECT e.name as event_name, s.name as sport_name, es.id as event_sport_id
    FROM events e
    JOIN event_sports es ON e.id = es.event_id
    JOIN sports s ON es.sport_id = s.id
    WHERE e.id = ? AND s.id = ?
");
$stmt->execute([$event_id, $sport_id]);
$event_sport = $stmt->fetch();

if (!$event_sport) {
    header('Location: events.php');
    exit;
}

// Get all categories for this sport in this event
$stmt = $conn->prepare("
    SELECT * FROM sport_categories 
    WHERE event_sport_id = ? 
    ORDER BY category_name ASC
");
$stmt->execute([$event_sport['event_sport_id']]);
$categories = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($event_sport['sport_name']); ?> Categories - <?php echo htmlspecialchars($event_sport['event_name']); ?> | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        /* Category Link Styling */
        .category-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .category-link:hover {
            color: #0056b3;
            text-decoration: underline;
            transform: translateX(2px);
        }

        .category-link:before {
            content: '\f0da';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            font-size: 0.8em;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .category-link:hover:before {
            opacity: 1;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <a href="events.php"><i class="fas fa-calendar"></i> Events</a>
                    </div>
                    <div class="breadcrumb-separator">></div>
                    <div class="breadcrumb-item">
                        <a href="manage-event.php?id=<?php echo $event_id; ?>"><?php echo htmlspecialchars($event_sport['event_name']); ?></a>
                    </div>
                    <div class="breadcrumb-separator">></div>
                    <div class="breadcrumb-item active">
                        <i class="fas fa-layer-group"></i>
                        <span><?php echo htmlspecialchars($event_sport['sport_name']); ?> Categories</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="logout-btn" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-layer-group"></i>
                    <?php echo htmlspecialchars($event_sport['sport_name']); ?> Categories
                </h1>
                <p class="page-description">
                    Manage categories for <?php echo htmlspecialchars($event_sport['sport_name']); ?>
                    in <?php echo htmlspecialchars($event_sport['event_name']); ?>
                </p>
            </div>

            <!-- Categories Header with Add Button -->
            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Categories</h3>
                    <button class="btn-modal-trigger" onclick="openModal('categoryModal')">
                        <i class="fas fa-plus"></i>
                        Add New Category
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($categories)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category Name</th>
                                    <th>Type</th>
                                    <th>Referee</th>
                                    <th>Email</th>
                                    <th>Venue</th>
                                    <th>Max Participants</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                <tr>
                                    <td>
                                        <strong>
                                            <a href="manage-category.php?event_id=<?php echo $event_id; ?>&sport_id=<?php echo $sport_id; ?>&category_id=<?php echo $category['id']; ?>"
                                               class="category-link"
                                               title="Manage <?php echo htmlspecialchars($category['category_name'] ?? ''); ?>">
                                                <?php echo htmlspecialchars($category['category_name'] ?? ''); ?>
                                            </a>
                                        </strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo $category['category_type']; ?>">
                                            <?php 
                                            if ($category['category_type'] === 'other' && !empty($category['category_type_custom'])) {
                                                echo htmlspecialchars($category['category_type_custom']);
                                            } else {
                                                echo ucfirst($category['category_type']);
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($category['referee_name'] ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if (!empty($category['referee_email'])): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($category['referee_email']); ?>">
                                                <?php echo htmlspecialchars($category['referee_email']); ?>
                                            </a>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($category['venue'] ?? 'N/A'); ?></td>
                                    <td><?php echo $category['max_participants'] ?: 'Unlimited'; ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $category['status']; ?>">
                                            <?php echo ucfirst($category['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editCategory(<?php echo $category['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn-delete" onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['category_name'] ?? ''); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-layer-group"></i>
                            <h3>No Categories Yet</h3>
                            <p>Start by adding categories for this sport to organize competitions by gender, age group, or skill level.</p>
                            <button class="btn btn-primary" onclick="openModal('categoryModal')">
                                <i class="fas fa-plus"></i> Add First Category
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Category Modal -->
    <div id="categoryModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="categoryModalTitle">Add New Category</h3>
                <button class="modal-close">&times;</button>
            </div>
            <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                <input type="hidden" name="entity" value="sport_category">
                <input type="hidden" name="action" value="create" id="categoryAction">
                <input type="hidden" name="id" id="categoryId">
                <input type="hidden" name="event_sport_id" value="<?php echo $event_sport['event_sport_id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <div class="modal-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="categoryName">Category Name <span class="required">*</span></label>
                            <input type="text" id="categoryName" name="category_name" class="form-control" required 
                                   placeholder="e.g., Men's Singles, Women's 5v5, Youth Division">
                        </div>
                        <div class="form-group">
                            <label for="categoryType">Category Type <span class="required">*</span></label>
                            <select id="categoryType" name="category_type" class="form-control" required>
                                <option value="">Select Type</option>
                                <option value="men">Men</option>
                                <option value="women">Women</option>
                                <option value="mixed">Mixed</option>
                                <option value="open">Open (Any Gender)</option>
                                <option value="youth">Youth/Junior</option>
                                <option value="senior">Senior</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group" id="customTypeGroup" style="display: none;">
                        <label for="categoryTypeCustom">Custom Type Description</label>
                        <input type="text" id="categoryTypeCustom" name="category_type_custom" class="form-control" 
                               placeholder="Enter custom category type...">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="refereeName">Assigned Referee/Umpire</label>
                            <input type="text" id="refereeName" name="referee_name" class="form-control" 
                                   placeholder="Enter referee name...">
                        </div>
                        <div class="form-group">
                            <label for="refereeEmail">Referee Email</label>
                            <input type="email" id="refereeEmail" name="referee_email" class="form-control" 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="categoryVenue">Venue/Location</label>
                            <input type="text" id="categoryVenue" name="venue" class="form-control" 
                                   placeholder="Enter venue or location...">
                        </div>
                        <div class="form-group">
                            <label for="maxParticipants">Max Participants</label>
                            <input type="number" id="maxParticipants" name="max_participants" class="form-control" 
                                   min="0" placeholder="0 = Unlimited">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="registrationDeadline">Registration Deadline</label>
                            <input type="datetime-local" id="registrationDeadline" name="registration_deadline" class="form-control">
                        </div>
                        <div class="form-group">
                            <label for="categoryStatus">Status</label>
                            <select id="categoryStatus" name="status" class="form-control">
                                <option value="registration">Registration Open</option>
                                <option value="ongoing">Ongoing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="categorySubmitBtn">Create Category</button>
                </div>
            </form>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>

    <script>
        // Category management functions
        function editCategory(categoryId) {
            console.log('editCategory called with ID:', categoryId);
            fetch(`ajax/get-sport-category.php?id=${categoryId}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('Raw response:', text);
                    let data = JSON.parse(text);

                    if (data.success) {
                        const category = data.category;

                        // Populate form fields
                        document.getElementById('categoryId').value = category.id;
                        document.getElementById('categoryName').value = category.category_name || '';
                        document.getElementById('categoryType').value = category.category_type || '';
                        document.getElementById('categoryTypeCustom').value = category.category_type_custom || '';
                        document.getElementById('refereeName').value = category.referee_name || '';
                        document.getElementById('refereeEmail').value = category.referee_email || '';
                        document.getElementById('categoryVenue').value = category.venue || '';
                        document.getElementById('maxParticipants').value = category.max_participants || '';
                        document.getElementById('categoryStatus').value = category.status || 'registration';

                        // Handle registration deadline
                        if (category.registration_deadline) {
                            const deadline = new Date(category.registration_deadline);
                            const localDateTime = new Date(deadline.getTime() - deadline.getTimezoneOffset() * 60000);
                            document.getElementById('registrationDeadline').value = localDateTime.toISOString().slice(0, 16);
                        }

                        // Show/hide custom type field
                        toggleCustomTypeField();

                        // Update modal for editing
                        document.getElementById('categoryAction').value = 'update';
                        document.getElementById('categoryModalTitle').textContent = 'Edit Category';
                        document.getElementById('categorySubmitBtn').textContent = 'Update Category';

                        // Open modal
                        openModal('categoryModal');
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading category data: ' + error.message);
                });
        }

        function deleteCategory(categoryId, categoryName) {
            if (confirm(`Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`)) {
                console.log('Deleting category:', categoryId, categoryName);

                const formData = new FormData();
                formData.append('entity', 'sport_category');
                formData.append('action', 'delete');
                formData.append('id', categoryId);
                formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

                fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('Delete response status:', response.status);
                    return response.text();
                })
                .then(text => {
                    console.log('Delete raw response:', text);
                    try {
                        const data = JSON.parse(text);
                        console.log('Delete parsed response:', data);

                        if (data.success) {
                            if (window.modalManager && window.modalManager.showSuccessMessage) {
                                window.modalManager.showSuccessMessage(data.message);
                            } else {
                                alert('Category deleted successfully!');
                            }
                            setTimeout(() => window.location.reload(), 1000);
                        } else {
                            alert('Error: ' + data.message);
                        }
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        alert('Server error: ' + text.substring(0, 100));
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    alert('Error deleting category: ' + error.message);
                });
            }
        }

        function toggleCustomTypeField() {
            const categoryType = document.getElementById('categoryType').value;
            const customTypeGroup = document.getElementById('customTypeGroup');
            const customTypeInput = document.getElementById('categoryTypeCustom');

            if (categoryType === 'other') {
                customTypeGroup.style.display = 'block';
                customTypeInput.required = true;
            } else {
                customTypeGroup.style.display = 'none';
                customTypeInput.required = false;
                customTypeInput.value = '';
            }
        }

        // Initialize page functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded - Initializing sport categories page');

            // Category type change handler
            const categoryTypeSelect = document.getElementById('categoryType');
            if (categoryTypeSelect) {
                categoryTypeSelect.addEventListener('change', toggleCustomTypeField);
            }

            // Reset modal when opening for new category
            const addCategoryBtn = document.querySelector('.btn-modal-trigger');
            if (addCategoryBtn) {
                addCategoryBtn.addEventListener('click', function() {
                    // Reset form for new category
                    document.getElementById('categoryAction').value = 'create';
                    document.getElementById('categoryModalTitle').textContent = 'Add New Category';
                    document.getElementById('categorySubmitBtn').textContent = 'Create Category';
                    document.getElementById('categoryId').value = '';

                    // Clear all form fields
                    const form = document.querySelector('#categoryModal .modal-form');
                    if (form) {
                        form.reset();
                        // Reset hidden fields that shouldn't be cleared
                        document.querySelector('input[name="entity"]').value = 'sport_category';
                        document.querySelector('input[name="event_sport_id"]').value = '<?php echo $event_sport['event_sport_id']; ?>';
                        document.querySelector('input[name="csrf_token"]').value = '<?php echo generateCSRFToken(); ?>';
                    }

                    // Hide custom type field
                    document.getElementById('customTypeGroup').style.display = 'none';
                    document.getElementById('categoryTypeCustom').required = false;
                });
            }
        });
    </script>
</body>
</html>
