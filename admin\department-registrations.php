<?php
/**
 * Department-Centric Registration Management Interface
 * Unified interface for managing department registrations and multi-sport participation
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/department_registration.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

// Get events for dropdown
$stmt = $conn->prepare("SELECT id, name, start_date, end_date, status FROM events ORDER BY start_date DESC");
$stmt->execute();
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get departments for dropdown
$stmt = $conn->prepare("SELECT id, name, abbreviation, color_code FROM departments ORDER BY name");
$stmt->execute();
$departments = $stmt->fetchAll(PDO::FETCH_ASSOC);

$selected_event_id = $_GET['event_id'] ?? ($events[0]['id'] ?? null);
$registrations = [];
$stats = [];

if ($selected_event_id) {
    $registrations = getEventDepartmentRegistrations($conn, $selected_event_id);
    $stats = getDepartmentRegistrationStats($conn, $selected_event_id);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Registrations - SC_IMS Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .registration-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; margin-top: 5px; }
        .registration-card { background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden; }
        .registration-header { padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: between; align-items: center; }
        .department-info { display: flex; align-items: center; gap: 15px; }
        .department-badge { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; }
        .registration-actions { display: flex; gap: 10px; }
        .sports-list { padding: 20px; }
        .sport-item { display: flex; justify-content: between; align-items: center; padding: 10px; border: 1px solid #eee; border-radius: 4px; margin-bottom: 10px; }
        .sport-info { display: flex; align-items: center; gap: 10px; }
        .sport-badge { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .empty-state { text-align: center; padding: 60px 20px; color: #666; }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="content-header">
            <h1><i class="fas fa-users"></i> Department Registrations</h1>
            <div class="header-actions">
                <select id="eventSelect" class="form-control" style="width: 300px;">
                    <option value="">Select Event</option>
                    <?php foreach ($events as $event): ?>
                        <option value="<?php echo $event['id']; ?>" <?php echo $event['id'] == $selected_event_id ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($event['name']); ?> (<?php echo date('M Y', strtotime($event['start_date'])); ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
                <button class="btn btn-primary" onclick="showRegisterDepartmentModal()">
                    <i class="fas fa-plus"></i> Register Department
                </button>
            </div>
        </div>

        <?php if ($selected_event_id && !empty($stats)): ?>
        <div class="registration-stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_departments'] ?? 0; ?></div>
                <div class="stat-label">Registered Departments</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_sport_participations'] ?? 0; ?></div>
                <div class="stat-label">Sport Participations</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_participants'] ?? 0; ?></div>
                <div class="stat-label">Total Participants</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['approved_departments'] ?? 0; ?></div>
                <div class="stat-label">Approved Departments</div>
            </div>
        </div>
        <?php endif; ?>

        <div class="registrations-container">
            <?php if (empty($registrations)): ?>
                <div class="empty-state">
                    <i class="fas fa-users fa-3x" style="color: #ddd; margin-bottom: 20px;"></i>
                    <h3>No Department Registrations</h3>
                    <p>No departments have registered for this event yet.</p>
                    <?php if ($selected_event_id): ?>
                        <button class="btn btn-primary" onclick="showRegisterDepartmentModal()">
                            <i class="fas fa-plus"></i> Register First Department
                        </button>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <?php foreach ($registrations as $registration): ?>
                    <div class="registration-card" data-registration-id="<?php echo $registration['id']; ?>">
                        <div class="registration-header">
                            <div class="department-info">
                                <div class="department-badge" style="background-color: <?php echo $registration['color_code'] ?? '#007bff'; ?>">
                                    <?php echo strtoupper(substr($registration['abbreviation'] ?? $registration['department_name'], 0, 2)); ?>
                                </div>
                                <div>
                                    <h4><?php echo htmlspecialchars($registration['department_name']); ?></h4>
                                    <div style="display: flex; gap: 15px; align-items: center; margin-top: 5px;">
                                        <span class="status-badge status-<?php echo $registration['status']; ?>">
                                            <?php echo ucfirst($registration['status']); ?>
                                        </span>
                                        <span style="color: #666; font-size: 0.9em;">
                                            <i class="fas fa-calendar"></i> <?php echo date('M j, Y', strtotime($registration['registration_date'])); ?>
                                        </span>
                                        <span style="color: #666; font-size: 0.9em;">
                                            <i class="fas fa-trophy"></i> <?php echo $registration['sports_count']; ?> sports
                                        </span>
                                        <span style="color: #666; font-size: 0.9em;">
                                            <i class="fas fa-users"></i> <?php echo $registration['total_participants']; ?> participants
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="registration-actions">
                                <button class="btn btn-sm btn-outline-primary" onclick="showSportParticipations(<?php echo $registration['id']; ?>)">
                                    <i class="fas fa-list"></i> View Sports
                                </button>
                                <span class="text-success" style="font-size: 0.9em; margin-left: 10px;">
                                    <i class="fas fa-check-circle"></i> Auto-participating in all event sports
                                </span>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" onclick="updateRegistrationStatus(<?php echo $registration['id']; ?>, 'approved')">
                                            <i class="fas fa-check"></i> Approve
                                        </a>
                                        <a class="dropdown-item" onclick="updateRegistrationStatus(<?php echo $registration['id']; ?>, 'rejected')">
                                            <i class="fas fa-times"></i> Reject
                                        </a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item text-danger" onclick="removeRegistration(<?php echo $registration['id']; ?>)">
                                            <i class="fas fa-trash"></i> Remove
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="sports-list" id="sports-list-<?php echo $registration['id']; ?>" style="display: none;">
                            <!-- Sports will be loaded dynamically -->
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Register Department Modal -->
    <div class="modal fade" id="registerDepartmentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> Register Department for Event</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form id="registerDepartmentForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="register_department_for_event">
                        <input type="hidden" name="event_id" value="<?php echo $selected_event_id; ?>">
                        
                        <div class="form-group">
                            <label>Department *</label>
                            <select name="department_id" class="form-control" required>
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Contact Person</label>
                            <input type="text" name="contact_person" class="form-control">
                        </div>
                        
                        <div class="form-group">
                            <label>Contact Email</label>
                            <input type="email" name="contact_email" class="form-control">
                        </div>
                        
                        <div class="form-group">
                            <label>Contact Phone</label>
                            <input type="tel" name="contact_phone" class="form-control">
                        </div>
                        
                        <div class="form-group">
                            <label>Status</label>
                            <select name="status" class="form-control">
                                <option value="pending">Pending</option>
                                <option value="approved">Approved</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Notes</label>
                            <textarea name="notes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Register Department</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Add Sport Participation Modal -->
    <div class="modal fade" id="addSportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> Add Sport Participation</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form id="addSportForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="add_sport_participation">
                        <input type="hidden" name="registration_id" id="sport_registration_id">
                        
                        <div class="form-group">
                            <label>Sport *</label>
                            <select name="event_sport_id" id="sport_select" class="form-control" required>
                                <option value="">Select Sport</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Team Name (Optional)</label>
                            <input type="text" name="team_name" class="form-control" placeholder="Custom team name for this sport">
                        </div>
                        
                        <div class="form-group">
                            <label>Participants</label>
                            <textarea name="participants" class="form-control" rows="5" placeholder="Enter participant names (one per line)"></textarea>
                            <small class="form-text text-muted">Enter one participant name per line</small>
                        </div>
                        
                        <div class="form-group">
                            <label>Notes</label>
                            <textarea name="notes" class="form-control" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Sport Participation</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Event selection change
        $('#eventSelect').change(function() {
            const eventId = $(this).val();
            if (eventId) {
                window.location.href = `department-registrations.php?event_id=${eventId}`;
            }
        });

        // Show register department modal
        function showRegisterDepartmentModal() {
            const eventId = $('#eventSelect').val();
            if (!eventId) {
                alert('Please select an event first');
                return;
            }
            $('input[name="event_id"]').val(eventId);
            $('#registerDepartmentModal').modal('show');
        }

        // Register department form submission
        $('#registerDepartmentForm').submit(function(e) {
            e.preventDefault();
            
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#registerDepartmentModal').modal('hide');
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while registering the department');
                }
            });
        });

        // Show sport participations
        function showSportParticipations(registrationId) {
            const container = $(`#sports-list-${registrationId}`);
            
            if (container.is(':visible')) {
                container.slideUp();
                return;
            }
            
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: {
                    action: 'get_sport_participations',
                    registration_id: registrationId,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        let html = '';
                        if (response.participations.length === 0) {
                            html = '<div class="text-center text-muted py-3"><i class="fas fa-info-circle"></i> This department will automatically participate in all sports when they are added to the event.</div>';
                        } else {
                            html += '<div class="alert alert-info mb-3"><i class="fas fa-info-circle"></i> This department automatically participates in all sports in this event.</div>';
                            response.participations.forEach(function(participation) {
                                const participants = JSON.parse(participation.participants || '[]');
                                html += `
                                    <div class="sport-item">
                                        <div class="sport-info">
                                            <i class="fas fa-trophy"></i>
                                            <span><strong>${participation.sport_name}</strong></span>
                                            <span class="sport-badge" style="background: #e9ecef; color: #495057;">${participation.sport_type}</span>
                                            <span style="color: #666; font-size: 0.9em;">${participants.length} participants</span>
                                            <span class="text-success" style="font-size: 0.8em; margin-left: 10px;">
                                                <i class="fas fa-check"></i> Auto-registered
                                            </span>
                                        </div>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editSportParticipation(${participation.id})">
                                                <i class="fas fa-edit"></i> Edit Details
                                            </button>
                                        </div>
                                    </div>
                                `;
                            });
                        }
                        container.html(html).slideDown();
                    }
                }
            });
        }

        // Show add sport modal
        function showAddSportModal(registrationId) {
            $('#sport_registration_id').val(registrationId);
            
            // Load available sports
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: {
                    action: 'get_available_sports',
                    event_id: $('#eventSelect').val(),
                    department_id: $(`[data-registration-id="${registrationId}"]`).data('department-id'),
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select Sport</option>';
                        response.sports.forEach(function(sport) {
                            if (!sport.is_participating) {
                                options += `<option value="${sport.id}">${sport.sport_name} (${sport.sport_type})</option>`;
                            }
                        });
                        $('#sport_select').html(options);
                        $('#addSportModal').modal('show');
                    }
                }
            });
        }

        // Add sport form submission
        $('#addSportForm').submit(function(e) {
            e.preventDefault();
            
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#addSportModal').modal('hide');
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while adding sport participation');
                }
            });
        });

        // Update registration status
        function updateRegistrationStatus(registrationId, status) {
            if (!confirm(`Are you sure you want to ${status} this registration?`)) return;
            
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: {
                    action: 'update_registration_status',
                    registration_id: registrationId,
                    status: status,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                }
            });
        }

        // Remove sport participation
        function removeSportParticipation(participationId) {
            if (!confirm('Are you sure you want to remove this sport participation?')) return;
            
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: {
                    action: 'remove_sport_participation',
                    participation_id: participationId,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                }
            });
        }
    </script>
</body>
</html>
