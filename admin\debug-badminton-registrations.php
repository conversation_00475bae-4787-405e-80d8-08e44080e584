<?php
/**
 * Debug Badminton Registration Data
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Debug Badminton Registration Data</h1>";
echo "<p>Investigating why tournament creation fails despite showing '5 teams registered'...</p>";

$badminton_event_sport_id = 18;

echo "<h2>1. Check Event Sport Details</h2>";
$stmt = $conn->prepare("
    SELECT es.*, e.name as event_name, s.name as sport_name
    FROM event_sports es
    JOIN events e ON es.event_id = e.id
    JOIN sports s ON es.sport_id = s.id
    WHERE es.id = ?
");
$stmt->execute([$badminton_event_sport_id]);
$event_sport = $stmt->fetch();

if ($event_sport) {
    echo "<p><strong>✅ Event Sport Found:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Event Sport ID:</strong> {$event_sport['id']}</li>";
    echo "<li><strong>Event:</strong> {$event_sport['event_name']} (ID: {$event_sport['event_id']})</li>";
    echo "<li><strong>Sport:</strong> {$event_sport['sport_name']} (ID: {$event_sport['sport_id']})</li>";
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ Event Sport not found!</p>";
    exit;
}

echo "<h2>2. Check Unified Registration System Data</h2>";
$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        dsp.status,
        dsp.team_name,
        dsp.participants,
        d.name as department_name,
        edr.status as registration_status,
        edr.id as registration_id
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ?
    ORDER BY d.name
");
$stmt->execute([$badminton_event_sport_id]);
$unified_registrations = $stmt->fetchAll();

echo "<h3>Unified Registration System Results:</h3>";
if (empty($unified_registrations)) {
    echo "<p style='color: red;'>❌ No registrations found in unified system</p>";
} else {
    echo "<p><strong>Found " . count($unified_registrations) . " registrations:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Department</th><th>Team Name</th><th>Sport Status</th><th>Reg Status</th><th>Participants</th>";
    echo "</tr>";
    
    $confirmed_count = 0;
    foreach ($unified_registrations as $reg) {
        $status_color = ($reg['status'] == 'confirmed') ? 'green' : 'orange';
        if ($reg['status'] == 'confirmed') $confirmed_count++;
        
        $participants = json_decode($reg['participants'], true);
        $participant_count = is_array($participants) ? count($participants) : 0;
        
        echo "<tr>";
        echo "<td>{$reg['id']}</td>";
        echo "<td>{$reg['department_name']}</td>";
        echo "<td>" . ($reg['team_name'] ?: 'Default') . "</td>";
        echo "<td style='color: {$status_color}; font-weight: bold;'>{$reg['status']}</td>";
        echo "<td>{$reg['registration_status']}</td>";
        echo "<td>{$participant_count}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><strong>Confirmed registrations:</strong> <span style='color: " . ($confirmed_count >= 2 ? 'green' : 'red') . ";'>{$confirmed_count}</span></p>";
}

echo "<h2>3. Check Old Registration System Data</h2>";
$stmt = $conn->prepare("
    SELECT r.*, d.name as department_name
    FROM registrations r
    JOIN departments d ON r.department_id = d.id
    WHERE r.event_sport_id = ?
");
$stmt->execute([$badminton_event_sport_id]);
$old_registrations = $stmt->fetchAll();

echo "<h3>Old Registration System Results:</h3>";
if (empty($old_registrations)) {
    echo "<p style='color: orange;'>⚠ No registrations found in old system</p>";
} else {
    echo "<p><strong>Found " . count($old_registrations) . " registrations:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Department</th><th>Team Name</th><th>Status</th><th>Participants</th>";
    echo "</tr>";
    
    $confirmed_count_old = 0;
    foreach ($old_registrations as $reg) {
        $status_color = ($reg['status'] == 'confirmed' || $reg['status'] == 'approved') ? 'green' : 'orange';
        if ($reg['status'] == 'confirmed' || $reg['status'] == 'approved') $confirmed_count_old++;
        
        $participants = json_decode($reg['participants'], true);
        $participant_count = is_array($participants) ? count($participants) : 0;
        
        echo "<tr>";
        echo "<td>{$reg['id']}</td>";
        echo "<td>{$reg['department_name']}</td>";
        echo "<td>" . ($reg['team_name'] ?: 'N/A') . "</td>";
        echo "<td style='color: {$status_color}; font-weight: bold;'>{$reg['status']}</td>";
        echo "<td>{$participant_count}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><strong>Confirmed/Approved registrations:</strong> <span style='color: " . ($confirmed_count_old >= 2 ? 'green' : 'red') . ";'>{$confirmed_count_old}</span></p>";
}

echo "<h2>4. Test Tournament Creation Query</h2>";
echo "<p>Testing the exact query used by tournament creation system...</p>";

// Test unified system query (primary)
$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        COALESCE(dsp.team_name, d.name) as team_name,
        edr.department_id,
        d.name as department_name,
        dsp.status
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
");
$stmt->execute([$badminton_event_sport_id]);
$tournament_participants = $stmt->fetchAll();

echo "<h3>Tournament Creation Query Results (Unified System):</h3>";
echo "<p><strong>Query:</strong> Looking for registrations where:</p>";
echo "<ul>";
echo "<li>event_sport_id = {$badminton_event_sport_id}</li>";
echo "<li>edr.status IN ('approved', 'pending')</li>";
echo "<li>dsp.status = 'confirmed'</li>";
echo "</ul>";

if (empty($tournament_participants)) {
    echo "<p style='color: red; font-size: 18px;'>❌ <strong>FOUND THE PROBLEM!</strong> Tournament query returns 0 participants</p>";
} else {
    echo "<p style='color: green; font-size: 18px;'>✅ Tournament query found " . count($tournament_participants) . " participants</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Team Name</th><th>Department</th><th>Status</th></tr>";
    foreach ($tournament_participants as $p) {
        echo "<tr><td>{$p['id']}</td><td>{$p['team_name']}</td><td>{$p['department_name']}</td><td>{$p['status']}</td></tr>";
    }
    echo "</table>";
}

// Test fallback query (old system)
$stmt = $conn->prepare("
    SELECT 
        r.id,
        COALESCE(r.team_name, d.name) as team_name,
        r.department_id,
        d.name as department_name,
        r.status
    FROM registrations r
    JOIN departments d ON r.department_id = d.id
    WHERE r.event_sport_id = ? AND r.status IN ('approved', 'confirmed')
");
$stmt->execute([$badminton_event_sport_id]);
$fallback_participants = $stmt->fetchAll();

echo "<h3>Tournament Creation Query Results (Fallback System):</h3>";
echo "<p><strong>Fallback Query:</strong> Looking for registrations where:</p>";
echo "<ul>";
echo "<li>event_sport_id = {$badminton_event_sport_id}</li>";
echo "<li>status IN ('approved', 'confirmed')</li>";
echo "</ul>";

if (empty($fallback_participants)) {
    echo "<p style='color: red;'>❌ Fallback query also returns 0 participants</p>";
} else {
    echo "<p style='color: green;'>✅ Fallback query found " . count($fallback_participants) . " participants</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Team Name</th><th>Department</th><th>Status</th></tr>";
    foreach ($fallback_participants as $p) {
        echo "<tr><td>{$p['id']}</td><td>{$p['team_name']}</td><td>{$p['department_name']}</td><td>{$p['status']}</td></tr>";
    }
    echo "</table>";
}

echo "<h2>5. Summary & Solution</h2>";
$total_participants = count($tournament_participants) + count($fallback_participants);

if ($total_participants < 2) {
    echo "<p style='color: red; font-size: 18px; font-weight: bold;'>🚨 PROBLEM IDENTIFIED:</p>";
    echo "<ul>";
    echo "<li>Tournament creation requires participants with specific status combinations</li>";
    echo "<li>Current registrations don't match the required criteria</li>";
    echo "<li>Need to update registration status to 'confirmed'</li>";
    echo "</ul>";
    
    if (!empty($unified_registrations)) {
        echo "<p><a href='confirm-badminton-registrations.php' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px;'>🔧 Fix Registration Status Now</a></p>";
    }
} else {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ Registrations look good! Tournament should be creatable.</p>";
}
?>
