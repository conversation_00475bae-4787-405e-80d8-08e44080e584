<?php
/**
 * Database Update Script for SC_IMS
 * Adds missing columns to existing database tables
 */

require_once '../config/database.php';
require_once 'auth.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$updates_applied = [];
$errors = [];

try {
    // Check and add missing columns to events table
    $result = $conn->query("SHOW COLUMNS FROM events LIKE 'location'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE events ADD COLUMN location VARCHAR(255) AFTER end_date");
        $updates_applied[] = "Added 'location' column to events table";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM events LIKE 'max_participants_per_department'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE events ADD COLUMN max_participants_per_department INT DEFAULT 0 AFTER location");
        $updates_applied[] = "Added 'max_participants_per_department' column to events table";
    }
    
    $result = $conn->query("SHOW COLUMNS FROM events LIKE 'winner_id'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE events ADD COLUMN winner_id INT NULL AFTER max_participants_per_department");
        $updates_applied[] = "Added 'winner_id' column to events table";
        
        // Add foreign key constraint
        try {
            $conn->exec("ALTER TABLE events ADD CONSTRAINT fk_events_winner FOREIGN KEY (winner_id) REFERENCES departments(id) ON DELETE SET NULL");
            $updates_applied[] = "Added foreign key constraint for winner_id";
        } catch (Exception $e) {
            // Foreign key might already exist or departments table might not exist yet
            $errors[] = "Could not add foreign key constraint for winner_id: " . $e->getMessage();
        }
    }
    
    // Check if all required tables exist
    $required_tables = ['events', 'sports', 'departments', 'event_sports', 'registrations', 'matches', 'scores', 'rankings', 'admin_users', 'admin_sessions', 'audit_logs'];
    
    foreach ($required_tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() == 0) {
            $errors[] = "Missing table: $table";
        }
    }
    
    if (empty($errors) && empty($updates_applied)) {
        $message = "Database is up to date. No updates needed.";
        $status = "success";
    } elseif (!empty($updates_applied) && empty($errors)) {
        $message = "Database updated successfully!";
        $status = "success";
    } else {
        $message = "Database update completed with some issues.";
        $status = "warning";
    }
    
} catch (Exception $e) {
    $errors[] = "Database update failed: " . $e->getMessage();
    $message = "Database update failed.";
    $status = "error";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Update - SC_IMS Admin</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .updates-list, .errors-list {
            margin: 20px 0;
        }
        .updates-list h3, .errors-list h3 {
            margin-bottom: 10px;
        }
        .updates-list ul, .errors-list ul {
            list-style-type: none;
            padding: 0;
        }
        .updates-list li, .errors-list li {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .updates-list li {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .errors-list li {
            background-color: #f8d7da;
            color: #721c24;
        }
        .back-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .back-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SC_IMS Database Update</h1>
            <p>Database schema update results</p>
        </div>
        
        <div class="status <?php echo $status; ?>">
            <strong><?php echo $message; ?></strong>
        </div>
        
        <?php if (!empty($updates_applied)): ?>
        <div class="updates-list">
            <h3>✅ Updates Applied:</h3>
            <ul>
                <?php foreach ($updates_applied as $update): ?>
                <li><?php echo htmlspecialchars($update); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
        <div class="errors-list">
            <h3>⚠️ Issues Found:</h3>
            <ul>
                <?php foreach ($errors as $error): ?>
                <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <a href="index.php" class="back-button">← Back to Admin Dashboard</a>
    </div>
</body>
</html>
