<?php
/**
 * <PERSON><PERSON><PERSON> to run the unified registration schema
 */

require_once 'config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "Running Unified Registration Schema...\n";

// Read the schema file
$schema_file = 'database/department_registration_schema.sql';
if (!file_exists($schema_file)) {
    die("Schema file not found: $schema_file\n");
}

$sql = file_get_contents($schema_file);

// Split into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

$success_count = 0;
$error_count = 0;

foreach ($statements as $statement) {
    if (empty($statement) || strpos($statement, '--') === 0) {
        continue; // Skip empty statements and comments
    }
    
    try {
        $conn->exec($statement);
        $success_count++;
        echo "✅ Executed statement successfully\n";
    } catch (PDOException $e) {
        $error_count++;
        echo "❌ Error executing statement: " . $e->getMessage() . "\n";
        echo "Statement: " . substr($statement, 0, 100) . "...\n";
    }
}

echo "\nSchema execution complete:\n";
echo "✅ Successful statements: $success_count\n";
echo "❌ Failed statements: $error_count\n";

// Verify tables were created
echo "\nVerifying tables...\n";
$tables_to_check = [
    'event_department_registrations',
    'department_sport_participations', 
    'department_overall_scores'
];

foreach ($tables_to_check as $table) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "❌ Table '$table' missing\n";
        }
    } catch (PDOException $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "\n";
    }
}

echo "\nDone!\n";
?>
