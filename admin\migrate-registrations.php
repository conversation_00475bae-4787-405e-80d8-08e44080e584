<?php
/**
 * Registration Migration Tool
 * Migrates existing sport-specific registrations to new department-centric system
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/department_registration.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$migration_log = [];
$errors = [];

function logMigrationStep($message, $success = true) {
    global $migration_log;
    $migration_log[] = [
        'message' => $message,
        'success' => $success,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['migrate'])) {
    try {
        $conn->beginTransaction();
        
        logMigrationStep("🚀 Starting registration migration from sport-specific to department-centric system");
        
        // Get all existing registrations grouped by event and department
        $stmt = $conn->prepare("
            SELECT r.*, es.event_id, es.sport_id, d.name as department_name, e.name as event_name, s.name as sport_name
            FROM registrations r
            JOIN event_sports es ON r.event_sport_id = es.id
            JOIN departments d ON r.department_id = d.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            WHERE r.department_sport_participation_id IS NULL
            ORDER BY es.event_id, r.department_id, r.registration_date
        ");
        $stmt->execute();
        $old_registrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($old_registrations)) {
            logMigrationStep("✅ No old registrations found to migrate");
            $conn->commit();
        } else {
            logMigrationStep("📊 Found " . count($old_registrations) . " old registrations to migrate");
            
            $migrated_count = 0;
            $event_dept_registrations = [];
            
            foreach ($old_registrations as $old_reg) {
                $event_id = $old_reg['event_id'];
                $department_id = $old_reg['department_id'];
                $key = $event_id . '_' . $department_id;
                
                // Create unified department registration if not exists
                if (!isset($event_dept_registrations[$key])) {
                    $existing = getDepartmentEventRegistration($conn, $event_id, $department_id);
                    
                    if (!$existing) {
                        $reg_data = [
                            'status' => $old_reg['status'],
                            'contact_person' => null,
                            'contact_email' => null,
                            'contact_phone' => null,
                            'notes' => 'Migrated from sport-specific registration',
                            'total_participants' => 0
                        ];
                        
                        $result = registerDepartmentForEvent($conn, $event_id, $department_id, $reg_data);
                        
                        if ($result['success']) {
                            $event_dept_registrations[$key] = $result['registration_id'];
                            logMigrationStep("✅ Created unified registration for {$old_reg['department_name']} in {$old_reg['event_name']}");
                        } else {
                            throw new Exception("Failed to create unified registration: " . $result['message']);
                        }
                    } else {
                        $event_dept_registrations[$key] = $existing['id'];
                        logMigrationStep("✅ Using existing unified registration for {$old_reg['department_name']} in {$old_reg['event_name']}");
                    }
                }
                
                $registration_id = $event_dept_registrations[$key];
                
                // Create sport participation
                $participants = json_decode($old_reg['participants'], true) ?: [];
                $participation_data = [
                    'team_name' => $old_reg['team_name'],
                    'participants' => $participants,
                    'status' => $old_reg['status'],
                    'notes' => 'Migrated from old registration system'
                ];
                
                $result = addDepartmentSportParticipation($conn, $registration_id, $old_reg['event_sport_id'], $participation_data);
                
                if ($result['success']) {
                    // Update old registration to link with new system
                    $stmt = $conn->prepare("UPDATE registrations SET department_sport_participation_id = ? WHERE id = ?");
                    $stmt->execute([$result['participation_id'], $old_reg['id']]);
                    
                    $migrated_count++;
                    logMigrationStep("✅ Migrated {$old_reg['department_name']} registration for {$old_reg['sport_name']}");
                } else {
                    logMigrationStep("❌ Failed to migrate {$old_reg['department_name']} registration for {$old_reg['sport_name']}: " . $result['message'], false);
                }
            }
            
            logMigrationStep("🎉 Migration completed! Migrated {$migrated_count} registrations");
            $conn->commit();
            
            // Log admin activity
            logAdminActivity('MIGRATE_REGISTRATIONS', 'system', null);
        }
        
    } catch (Exception $e) {
        $conn->rollBack();
        $error_msg = "💥 Migration failed: " . $e->getMessage();
        logMigrationStep($error_msg, false);
        $errors[] = $error_msg;
    }
}

// Check migration status
$stmt = $conn->prepare("
    SELECT 
        COUNT(*) as total_old_registrations,
        COUNT(CASE WHEN department_sport_participation_id IS NOT NULL THEN 1 END) as migrated_registrations
    FROM registrations
");
$stmt->execute();
$migration_status = $stmt->fetch(PDO::FETCH_ASSOC);

$stmt = $conn->prepare("SELECT COUNT(*) as total_dept_registrations FROM event_department_registrations");
$stmt->execute();
$dept_reg_count = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migrate Registrations - SC_IMS Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .migration-container { max-width: 1000px; margin: 20px auto; padding: 20px; }
        .migration-header { text-align: center; margin-bottom: 30px; }
        .migration-status { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px; }
        .status-card { text-align: center; padding: 20px; border-radius: 8px; }
        .status-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .status-label { color: #666; }
        .migration-warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .migration-log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; max-height: 500px; overflow-y: auto; }
        .log-entry { margin: 5px 0; padding: 8px; border-radius: 4px; font-family: monospace; }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }
        .migrate-button { background: #17a2b8; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; }
        .migrate-button:hover { background: #138496; }
        .migrate-button:disabled { background: #6c757d; cursor: not-allowed; }
        .progress-bar { background: #e9ecef; border-radius: 4px; overflow: hidden; margin-top: 10px; }
        .progress-fill { background: #28a745; height: 20px; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="migration-container">
            <div class="migration-header">
                <h1><i class="fas fa-exchange-alt"></i> Registration Migration Tool</h1>
                <p>Migrate existing sport-specific registrations to the new department-centric system</p>
            </div>
            
            <div class="migration-status">
                <h3><i class="fas fa-chart-pie"></i> Migration Status</h3>
                <div class="status-grid">
                    <div class="status-card" style="background: #e3f2fd;">
                        <div class="status-number" style="color: #1976d2;"><?php echo $migration_status['total_old_registrations']; ?></div>
                        <div class="status-label">Total Old Registrations</div>
                    </div>
                    <div class="status-card" style="background: #e8f5e8;">
                        <div class="status-number" style="color: #388e3c;"><?php echo $migration_status['migrated_registrations']; ?></div>
                        <div class="status-label">Migrated Registrations</div>
                    </div>
                    <div class="status-card" style="background: #fff3e0;">
                        <div class="status-number" style="color: #f57c00;"><?php echo $dept_reg_count['total_dept_registrations']; ?></div>
                        <div class="status-label">Department Registrations</div>
                    </div>
                    <div class="status-card" style="background: #fce4ec;">
                        <div class="status-number" style="color: #c2185b;">
                            <?php echo $migration_status['total_old_registrations'] - $migration_status['migrated_registrations']; ?>
                        </div>
                        <div class="status-label">Pending Migration</div>
                    </div>
                </div>
                
                <?php if ($migration_status['total_old_registrations'] > 0): ?>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: <?php echo ($migration_status['migrated_registrations'] / $migration_status['total_old_registrations']) * 100; ?>%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 10px; color: #666;">
                        <?php echo round(($migration_status['migrated_registrations'] / $migration_status['total_old_registrations']) * 100, 1); ?>% Complete
                    </div>
                <?php endif; ?>
            </div>
            
            <?php if (empty($migration_log) && $migration_status['total_old_registrations'] > $migration_status['migrated_registrations']): ?>
            <div class="migration-warning">
                <h3><i class="fas fa-exclamation-triangle"></i> Migration Required</h3>
                <ul>
                    <li><strong>Purpose:</strong> Convert sport-specific registrations to unified department registrations</li>
                    <li><strong>Process:</strong> Creates department event registrations and links existing sport participations</li>
                    <li><strong>Safety:</strong> Original data is preserved and linked to new system</li>
                    <li><strong>Reversible:</strong> Migration can be undone if needed</li>
                </ul>
            </div>
            
            <form method="POST" style="text-align: center;">
                <button type="submit" name="migrate" class="migrate-button">
                    <i class="fas fa-exchange-alt"></i> Start Migration
                </button>
            </form>
            <?php elseif ($migration_status['total_old_registrations'] == $migration_status['migrated_registrations'] && $migration_status['total_old_registrations'] > 0): ?>
            <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="color: #155724;"><i class="fas fa-check-circle"></i> Migration Complete!</h3>
                <p style="color: #155724;">All registrations have been successfully migrated to the new department-centric system.</p>
                <a href="department-registrations.php" class="btn btn-success">
                    <i class="fas fa-arrow-right"></i> View Department Registrations
                </a>
            </div>
            <?php elseif ($migration_status['total_old_registrations'] == 0): ?>
            <div style="background: #e2e3e5; border: 1px solid #d6d8db; padding: 20px; border-radius: 8px; text-align: center;">
                <h3 style="color: #383d41;"><i class="fas fa-info-circle"></i> No Migration Needed</h3>
                <p style="color: #383d41;">No old registrations found. Your system is already using the new department-centric registration system.</p>
                <a href="department-registrations.php" class="btn btn-primary">
                    <i class="fas fa-arrow-right"></i> Manage Department Registrations
                </a>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($migration_log)): ?>
            <div class="migration-log">
                <h3><i class="fas fa-list"></i> Migration Log</h3>
                <?php foreach ($migration_log as $entry): ?>
                    <div class="log-entry <?php echo $entry['success'] ? 'log-success' : 'log-error'; ?>">
                        [<?php echo $entry['timestamp']; ?>] <?php echo htmlspecialchars($entry['message']); ?>
                    </div>
                <?php endforeach; ?>
                
                <?php if (empty($errors)): ?>
                    <div style="text-align: center; margin-top: 20px;">
                        <a href="department-registrations.php" class="btn btn-primary">
                            <i class="fas fa-arrow-right"></i> Continue to Department Registrations
                        </a>
                    </div>
                <?php else: ?>
                    <div style="text-align: center; margin-top: 20px;">
                        <button onclick="location.reload()" class="btn btn-warning">
                            <i class="fas fa-redo"></i> Retry Migration
                        </button>
                    </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
