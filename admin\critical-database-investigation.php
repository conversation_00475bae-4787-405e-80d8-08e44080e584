<?php
/**
 * Critical Database Investigation
 * SC_IMS Sports Competition and Event Management System
 * 
 * Investigate why tournament_structure_id column error persists
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Critical Database Investigation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-danger { background: #dc3545; }
        .btn-success { background: #28a745; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Critical Database Investigation</h1>
        <p><strong>Investigation Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="error">
            <h3>🚨 Critical Issues Identified</h3>
            <p><strong>1. Tournament Format Inconsistency:</strong> Round Robin vs Single Elimination</p>
            <p><strong>2. Persistent Database Error:</strong> tournament_structure_id column not found</p>
        </div>
        
        <?php
        try {
            // Step 1: Check current matches table structure
            echo '<div class="step">';
            echo '<h2>Step 1: Current Matches Table Structure</h2>';
            
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo '<table>';
            echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
            
            $tournament_columns_found = [];
            $required_tournament_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            
            foreach ($columns as $column) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Default']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Extra']) . '</td>';
                echo '</tr>';
                
                if (in_array($column['Field'], $required_tournament_columns)) {
                    $tournament_columns_found[] = $column['Field'];
                }
            }
            echo '</table>';
            
            $missing_columns = array_diff($required_tournament_columns, $tournament_columns_found);
            
            if (empty($missing_columns)) {
                echo '<div class="success">✅ All tournament columns exist in matches table</div>';
            } else {
                echo '<div class="error">❌ Missing tournament columns: ' . implode(', ', $missing_columns) . '</div>';
            }
            
            echo '<p><strong>Tournament columns found:</strong> ' . implode(', ', $tournament_columns_found) . '</p>';
            echo '<p><strong>Missing columns:</strong> ' . implode(', ', $missing_columns) . '</p>';
            
            echo '</div>';
            
            // Step 2: Check tournament tables existence
            echo '<div class="step">';
            echo '<h2>Step 2: Tournament Tables Status</h2>';
            
            $tournament_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
            
            echo '<table>';
            echo '<tr><th>Table</th><th>Exists</th><th>Row Count</th></tr>';
            
            foreach ($tournament_tables as $table) {
                $stmt = $conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                $exists = $stmt->fetch() ? 'Yes' : 'No';
                
                $row_count = 'N/A';
                if ($exists === 'Yes') {
                    try {
                        $stmt = $conn->prepare("SELECT COUNT(*) FROM $table");
                        $stmt->execute();
                        $row_count = $stmt->fetchColumn();
                    } catch (Exception $e) {
                        $row_count = 'Error: ' . $e->getMessage();
                    }
                }
                
                echo '<tr>';
                echo '<td>' . $table . '</td>';
                echo '<td style="color: ' . ($exists === 'Yes' ? 'green' : 'red') . '">' . $exists . '</td>';
                echo '<td>' . $row_count . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            echo '</div>';
            
            // Step 3: Test INSERT operation
            echo '<div class="step">';
            echo '<h2>Step 3: Test INSERT Operation</h2>';
            
            if (empty($missing_columns)) {
                try {
                    // Test INSERT with tournament columns
                    $test_sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
                    
                    echo '<p><strong>Test SQL:</strong></p>';
                    echo '<pre>' . htmlspecialchars($test_sql) . '</pre>';
                    
                    $stmt = $conn->prepare($test_sql);
                    $stmt->execute();
                    $test_id = $conn->lastInsertId();
                    
                    echo '<div class="success">✅ INSERT test successful (Test ID: ' . $test_id . ')</div>';
                    
                    // Clean up test record
                    $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
                    echo '<p>✓ Test record cleaned up</p>';
                    
                } catch (Exception $e) {
                    echo '<div class="error">';
                    echo '<h3>❌ INSERT Test Failed</h3>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '<p><strong>Error Code:</strong> ' . $e->getCode() . '</p>';
                    echo '</div>';
                }
            } else {
                echo '<div class="warning">⚠️ Skipping INSERT test due to missing columns</div>';
            }
            
            echo '</div>';
            
            // Step 4: Check tournament format inconsistency
            echo '<div class="step">';
            echo '<h2>Step 4: Tournament Format Inconsistency Investigation</h2>';
            
            // Check sport categories
            $stmt = $conn->prepare("SELECT * FROM sport_categories WHERE name LIKE '%Badminton%' OR name LIKE '%Doubles%'");
            $stmt->execute();
            $categories = $stmt->fetchAll();
            
            if ($categories) {
                echo '<h3>Sport Categories:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Tournament Format</th><th>Event Sport ID</th></tr>';
                foreach ($categories as $category) {
                    echo '<tr>';
                    echo '<td>' . $category['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($category['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($category['tournament_format'] ?? 'N/A') . '</td>';
                    echo '<td>' . htmlspecialchars($category['event_sport_id']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            // Check sports table
            $stmt = $conn->prepare("SELECT * FROM sports WHERE name LIKE '%Badminton%'");
            $stmt->execute();
            $sports = $stmt->fetchAll();
            
            if ($sports) {
                echo '<h3>Sports:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Type</th><th>Bracket Format</th></tr>';
                foreach ($sports as $sport) {
                    echo '<tr>';
                    echo '<td>' . $sport['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($sport['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($sport['type']) . '</td>';
                    echo '<td>' . htmlspecialchars($sport['bracket_format'] ?? 'N/A') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            // Check event_sports
            $stmt = $conn->prepare("
                SELECT es.*, s.name as sport_name, s.bracket_format 
                FROM event_sports es 
                JOIN sports s ON es.sport_id = s.id 
                WHERE s.name LIKE '%Badminton%'
            ");
            $stmt->execute();
            $event_sports = $stmt->fetchAll();
            
            if ($event_sports) {
                echo '<h3>Event Sports:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Event ID</th><th>Sport Name</th><th>Bracket Format</th><th>Status</th></tr>';
                foreach ($event_sports as $es) {
                    echo '<tr>';
                    echo '<td>' . $es['id'] . '</td>';
                    echo '<td>' . $es['event_id'] . '</td>';
                    echo '<td>' . htmlspecialchars($es['sport_name']) . '</td>';
                    echo '<td>' . htmlspecialchars($es['bracket_format'] ?? 'N/A') . '</td>';
                    echo '<td>' . htmlspecialchars($es['status']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            echo '</div>';
            
            // Step 5: Check database initialization file
            echo '<div class="step">';
            echo '<h2>Step 5: Database Initialization Analysis</h2>';
            
            $database_file = '../config/database.php';
            if (file_exists($database_file)) {
                $content = file_get_contents($database_file);
                
                // Check if tournament columns are in the CREATE TABLE statement
                if (strpos($content, 'tournament_structure_id') !== false) {
                    echo '<div class="success">✅ tournament_structure_id found in database.php</div>';
                } else {
                    echo '<div class="error">❌ tournament_structure_id NOT found in database.php</div>';
                }
                
                // Check for matches table creation
                if (preg_match('/CREATE TABLE.*matches.*\((.*?)\)/s', $content, $matches)) {
                    echo '<h3>Matches Table Definition in database.php:</h3>';
                    echo '<pre>' . htmlspecialchars($matches[0]) . '</pre>';
                } else {
                    echo '<div class="warning">⚠️ Could not find matches table definition in database.php</div>';
                }
            } else {
                echo '<div class="error">❌ database.php file not found</div>';
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Investigation error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
        
        <!-- Analysis Results -->
        <div class="step">
            <h2>📊 Analysis Results</h2>
            
            <div class="highlight">
                <h3>🔍 Key Findings</h3>
                <p>Based on the investigation above, we can identify:</p>
                <ul>
                    <li><strong>Database Schema State:</strong> Whether tournament columns exist or are missing</li>
                    <li><strong>Tournament Tables:</strong> Status of all tournament-related tables</li>
                    <li><strong>Format Inconsistency:</strong> Differences between sport and category tournament formats</li>
                    <li><strong>Initialization File:</strong> Whether the database.php file includes tournament columns</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🎯 Next Steps</h3>
                <p>Based on the findings, we will:</p>
                <ol>
                    <li><strong>Fix Missing Columns:</strong> Add any missing tournament columns to matches table</li>
                    <li><strong>Resolve Format Inconsistency:</strong> Synchronize tournament formats across system</li>
                    <li><strong>Apply Permanent Fix:</strong> Ensure database initialization includes tournament support</li>
                    <li><strong>Test Complete Workflow:</strong> Verify tournament creation works end-to-end</li>
                </ol>
            </div>
        </div>
        
        <!-- Actions -->
        <div class="step">
            <h2>🛠️ Available Actions</h2>
            <p>
                <a href="emergency-database-fix.php" class="btn btn-danger">🚨 Emergency Database Fix</a>
                <a href="format-synchronization-fix.php" class="btn btn-success">🔄 Fix Format Inconsistency</a>
                <a href="comprehensive-tournament-test.php" class="btn">🧪 Test Tournament Creation</a>
                <a href="index.php" class="btn">🏠 Admin Dashboard</a>
            </p>
        </div>
    </div>
</body>
</html>
