<?php
// Disable all error reporting to prevent any output
error_reporting(0);
ini_set('display_errors', 0);

// Clean any existing output
if (ob_get_level()) {
    ob_end_clean();
}

// Start fresh output buffer
ob_start();

try {
    require_once '../../config/database.php';
    require_once '../../includes/auth.php';
    
    // Ensure admin authentication
    requireAdmin();
    
    // Set JSON header
    header('Content-Type: application/json');
    
    $database = new Database();
    $conn = $database->getConnection();
    
    $sportId = $_GET['sport_id'] ?? 4;
    
    // Simple test - just return some basic tournament formats
    $formats = [
        [
            'value' => 1,
            'text' => 'Single Elimination',
            'description' => 'Traditional knockout tournament',
            'min_participants' => 2,
            'max_participants' => null
        ],
        [
            'value' => 2,
            'text' => 'Swiss System',
            'description' => 'Each player plays a fixed number of rounds',
            'min_participants' => 4,
            'max_participants' => null
        ],
        [
            'value' => 3,
            'text' => 'Round Robin',
            'description' => 'Every participant plays every other participant',
            'min_participants' => 3,
            'max_participants' => 16
        ]
    ];
    
    // Clear output buffer and send clean JSON
    ob_clean();
    
    echo json_encode([
        'success' => true,
        'sport_type_category' => 'academic',
        'formats' => $formats,
        'debug' => [
            'sport_id' => $sportId,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    // Clear output buffer
    if (ob_get_level()) {
        ob_clean();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// End output buffer and send
ob_end_flush();
?>
