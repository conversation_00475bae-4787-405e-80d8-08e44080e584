<?php
/**
 * Comprehensive Database State Diagnosis
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Database State Diagnosis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1400px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .btn { display: inline-block; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 3px; font-size: 12px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 11px; }
        .sql-query { background: #e9ecef; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 11px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Comprehensive Database State Diagnosis</h1>
        <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>Database:</strong> <?php echo $conn->query("SELECT DATABASE()")->fetchColumn(); ?></p>
        
        <?php
        try {
            // Step 1: Check exact matches table structure
            echo '<div class="step">';
            echo '<h2>📋 Step 1: Current Matches Table Structure</h2>';
            
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $matches_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo '<table>';
            echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
            
            $tournament_columns_found = [];
            foreach ($matches_columns as $column) {
                $is_tournament_col = in_array($column['Field'], ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match']);
                $row_class = $is_tournament_col ? 'style="background-color: #d4edda;"' : '';
                
                echo "<tr $row_class>";
                echo '<td><strong>' . htmlspecialchars($column['Field']) . '</strong></td>';
                echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
                echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
                echo '<td>' . htmlspecialchars($column['Extra'] ?? '') . '</td>';
                echo '</tr>';
                
                if ($is_tournament_col) {
                    $tournament_columns_found[] = $column['Field'];
                }
            }
            echo '</table>';
            
            echo '<p><strong>Tournament columns found:</strong> ' . (empty($tournament_columns_found) ? 'NONE' : implode(', ', $tournament_columns_found)) . '</p>';
            
            echo '</div>';
            
            // Step 2: Test column access directly
            echo '<div class="step">';
            echo '<h2>🧪 Step 2: Direct Column Access Tests</h2>';
            
            $test_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            
            foreach ($test_columns as $column) {
                try {
                    $stmt = $conn->prepare("SELECT $column FROM matches LIMIT 1");
                    $stmt->execute();
                    echo '<div class="success">✅ ' . $column . ' - Accessible</div>';
                } catch (Exception $e) {
                    echo '<div class="error">❌ ' . $column . ' - Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
            
            echo '</div>';
            
            // Step 3: Check all tournament tables
            echo '<div class="step">';
            echo '<h2>🏗️ Step 3: Tournament Tables Status</h2>';
            
            $tournament_tables = [
                'tournament_formats',
                'tournament_structures', 
                'tournament_rounds',
                'tournament_participants'
            ];
            
            foreach ($tournament_tables as $table) {
                try {
                    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
                    $stmt->execute([$table]);
                    $exists = $stmt->fetch();
                    
                    if ($exists) {
                        echo '<div class="success">✅ ' . $table . ' - EXISTS</div>';
                        
                        // Get row count
                        $stmt = $conn->prepare("SELECT COUNT(*) FROM $table");
                        $stmt->execute();
                        $count = $stmt->fetchColumn();
                        echo '<div class="info">ℹ️ ' . $table . ' - ' . $count . ' records</div>';
                        
                        // Show structure
                        $stmt = $conn->prepare("DESCRIBE $table");
                        $stmt->execute();
                        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo '<details style="margin: 10px 0;">';
                        echo '<summary>Show ' . $table . ' structure (' . count($columns) . ' columns)</summary>';
                        echo '<table style="margin: 10px 0; font-size: 11px;">';
                        echo '<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
                        foreach ($columns as $col) {
                            echo '<tr>';
                            echo '<td>' . htmlspecialchars($col['Field']) . '</td>';
                            echo '<td>' . htmlspecialchars($col['Type']) . '</td>';
                            echo '<td>' . htmlspecialchars($col['Null']) . '</td>';
                            echo '<td>' . htmlspecialchars($col['Key']) . '</td>';
                            echo '<td>' . htmlspecialchars($col['Default'] ?? 'NULL') . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                        echo '</details>';
                    } else {
                        echo '<div class="error">❌ ' . $table . ' - MISSING</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="error">❌ ' . $table . ' - Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
            
            echo '</div>';
            
            // Step 4: Test INSERT operations
            echo '<div class="step">';
            echo '<h2>💾 Step 4: Test INSERT Operations</h2>';
            
            // Test 1: Basic matches insert without tournament columns
            try {
                echo '<h3>Test 1: Basic matches insert (without tournament columns)</h3>';
                $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, status) VALUES (999, 999, 999, 'test')";
                echo '<div class="sql-query">' . htmlspecialchars($sql) . '</div>';
                
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $basic_insert_id = $conn->lastInsertId();
                echo '<div class="success">✅ Basic insert successful - ID: ' . $basic_insert_id . '</div>';
                
                // Clean up
                $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$basic_insert_id]);
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Basic insert failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            
            // Test 2: Insert with tournament columns
            try {
                echo '<h3>Test 2: Insert with tournament columns</h3>';
                $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
                echo '<div class="sql-query">' . htmlspecialchars($sql) . '</div>';
                
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $tournament_insert_id = $conn->lastInsertId();
                echo '<div class="success">✅ Tournament columns insert successful - ID: ' . $tournament_insert_id . '</div>';
                
                // Clean up
                $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$tournament_insert_id]);
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Tournament columns insert failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                echo '<div class="warning">This is likely the source of your error!</div>';
            }
            
            echo '</div>';
            
            // Step 5: Check foreign key constraints
            echo '<div class="step">';
            echo '<h2>🔗 Step 5: Foreign Key Constraints Analysis</h2>';
            
            $stmt = $conn->prepare("
                SELECT 
                    CONSTRAINT_NAME,
                    TABLE_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'matches'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            $stmt->execute();
            $foreign_keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($foreign_keys)) {
                echo '<div class="warning">⚠️ No foreign key constraints found on matches table</div>';
            } else {
                echo '<table>';
                echo '<tr><th>Constraint</th><th>Column</th><th>References</th></tr>';
                foreach ($foreign_keys as $fk) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($fk['CONSTRAINT_NAME']) . '</td>';
                    echo '<td>' . htmlspecialchars($fk['COLUMN_NAME']) . '</td>';
                    echo '<td>' . htmlspecialchars($fk['REFERENCED_TABLE_NAME']) . '.' . htmlspecialchars($fk['REFERENCED_COLUMN_NAME']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            echo '</div>';
            
            // Step 6: Check for any existing tournament data
            echo '<div class="step">';
            echo '<h2>📊 Step 6: Existing Tournament Data</h2>';
            
            // Check for matches with tournament data
            try {
                $stmt = $conn->prepare("SELECT COUNT(*) FROM matches WHERE tournament_structure_id IS NOT NULL");
                $stmt->execute();
                $tournament_matches = $stmt->fetchColumn();
                echo '<div class="info">ℹ️ Matches with tournament_structure_id: ' . $tournament_matches . '</div>';
                
                if ($tournament_matches > 0) {
                    $stmt = $conn->prepare("SELECT id, tournament_structure_id, tournament_round_id, bracket_position FROM matches WHERE tournament_structure_id IS NOT NULL LIMIT 5");
                    $stmt->execute();
                    $sample_matches = $stmt->fetchAll();
                    
                    echo '<h3>Sample Tournament Matches:</h3>';
                    echo '<table>';
                    echo '<tr><th>Match ID</th><th>Tournament Structure ID</th><th>Round ID</th><th>Bracket Position</th></tr>';
                    foreach ($sample_matches as $match) {
                        echo '<tr>';
                        echo '<td>' . $match['id'] . '</td>';
                        echo '<td>' . $match['tournament_structure_id'] . '</td>';
                        echo '<td>' . $match['tournament_round_id'] . '</td>';
                        echo '<td>' . htmlspecialchars($match['bracket_position']) . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                }
            } catch (Exception $e) {
                echo '<div class="error">❌ Error checking tournament matches: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            
            echo '</div>';
            
            // Step 7: Database connection info
            echo '<div class="step">';
            echo '<h2>🔌 Step 7: Database Connection Information</h2>';
            
            try {
                $stmt = $conn->query("SELECT CONNECTION_ID(), USER(), DATABASE(), VERSION()");
                $conn_info = $stmt->fetch(PDO::FETCH_NUM);
                
                echo '<table>';
                echo '<tr><th>Property</th><th>Value</th></tr>';
                echo '<tr><td>Connection ID</td><td>' . $conn_info[0] . '</td></tr>';
                echo '<tr><td>User</td><td>' . htmlspecialchars($conn_info[1]) . '</td></tr>';
                echo '<tr><td>Database</td><td>' . htmlspecialchars($conn_info[2]) . '</td></tr>';
                echo '<tr><td>MySQL Version</td><td>' . htmlspecialchars($conn_info[3]) . '</td></tr>';
                echo '</table>';
                
                // Check if we're in a transaction
                echo '<p><strong>Auto-commit status:</strong> ' . ($conn->query("SELECT @@autocommit")->fetchColumn() ? 'ON' : 'OFF') . '</p>';
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Error getting connection info: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Diagnosis error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
        
        <!-- Actions -->
        <div class="step">
            <h2>🛠️ Recommended Actions</h2>
            <p>
                <a href="force-schema-rebuild.php" class="btn btn-danger">🔧 Force Schema Rebuild</a>
                <a href="comprehensive-tournament-schema-fix.php" class="btn btn-warning">🔄 Re-run Schema Fix</a>
                <a href="test-tournament-creation-fixed.php" class="btn btn-success">🧪 Test Tournament Creation</a>
                <a href="check-matches-table.php" class="btn">📋 Check Matches Table</a>
            </p>
        </div>
    </div>
</body>
</html>
