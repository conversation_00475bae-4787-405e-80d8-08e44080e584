<?php
/**
 * Initialize Missing Database Tables for SC_IMS
 * Creates tournament_structures and scoring_systems tables if they don't exist
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

$messages = [];

try {
    // Create scoring_systems table
    $sql = "CREATE TABLE IF NOT EXISTS scoring_systems (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        name VARCHAR(255) NOT NULL DEFAULT 'Default Scoring',
        description TEXT,
        position_points JSON,
        participation_bonus DECIMAL(5,2) DEFAULT 0,
        winner_bonus DECIMAL(5,2) DEFAULT 0,
        min_sports_required INT DEFAULT 1,
        tie_breaker_method ENUM('total_wins', 'average_position', 'head_to_head', 'manual') DEFAULT 'total_wins',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
        INDEX idx_scoring_event (event_id),
        INDEX idx_scoring_active (is_active)
    )";
    $conn->exec($sql);
    $messages[] = "✅ scoring_systems table created/verified";

    // Create tournament_formats table (enhanced version)
    $sql = "CREATE TABLE IF NOT EXISTS tournament_formats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        sport_type_category ENUM('individual', 'team', 'academic', 'judged', 'all') NOT NULL DEFAULT 'all',
        min_participants INT DEFAULT 2,
        max_participants INT DEFAULT NULL,
        requires_seeding BOOLEAN DEFAULT FALSE,
        supports_byes BOOLEAN DEFAULT TRUE,
        advancement_type ENUM('elimination', 'points', 'ranking', 'hybrid') NOT NULL DEFAULT 'elimination',
        rounds_formula VARCHAR(255),
        matches_formula VARCHAR(255),
        algorithm_class VARCHAR(100),
        configuration JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->exec($sql);
    $messages[] = "✅ tournament_formats table created/verified";

    // Create tournament_structures table
    $sql = "CREATE TABLE IF NOT EXISTS tournament_structures (
        id INT AUTO_INCREMENT PRIMARY KEY,
        event_sport_id INT NOT NULL,
        tournament_format_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
        participant_count INT DEFAULT 0,
        total_rounds INT DEFAULT 0,
        current_round INT DEFAULT 0,
        seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
        bracket_data JSON,
        advancement_rules JSON,
        scoring_config JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
        FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE CASCADE,
        INDEX idx_tournament_structures_event_sport (event_sport_id),
        INDEX idx_tournament_structures_status (status)
    )";
    $conn->exec($sql);
    $messages[] = "✅ tournament_structures table created/verified";

    // Insert default tournament formats if they don't exist
    $default_formats = [
        ['Single Elimination', 'single_elimination', 'Traditional single elimination bracket', 'all', 2, null, true, true, 'elimination', 'ceil(log2(n))', 'n-1', 'SingleEliminationAlgorithm', '{"consolation_rounds": false}'],
        ['Double Elimination', 'double_elimination', 'Double elimination with winners and losers brackets', 'all', 2, null, true, true, 'elimination', 'ceil(log2(n)) + ceil(log2(n)) - 1', '2*n-2', 'DoubleEliminationAlgorithm', '{"consolation_rounds": true}'],
        ['Round Robin', 'round_robin', 'All participants play against each other', 'all', 2, 16, false, false, 'points', '1', 'n*(n-1)/2', 'RoundRobinAlgorithm', '{"points_win": 3, "points_draw": 1, "points_loss": 0}'],
        ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic', 4, null, false, false, 'points', 'ceil(log2(n))', 'varies', 'SwissSystemAlgorithm', '{"rounds": "auto"}']
    ];

    foreach ($default_formats as $format) {
        $stmt = $conn->prepare("
            INSERT IGNORE INTO tournament_formats 
            (name, code, description, sport_type_category, min_participants, max_participants, requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula, algorithm_class, configuration)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($format);
    }
    $messages[] = "✅ Default tournament formats inserted";

    // Insert default scoring systems for existing events
    $stmt = $conn->prepare("SELECT id FROM events");
    $stmt->execute();
    $events = $stmt->fetchAll();

    foreach ($events as $event) {
        $stmt = $conn->prepare("
            INSERT IGNORE INTO scoring_systems 
            (event_id, name, description, position_points, participation_bonus, winner_bonus)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event['id'],
            'Default Scoring System',
            'Standard point-based scoring: 1st=10pts, 2nd=8pts, 3rd=6pts, 4th=4pts, 5th=2pts, participation=1pt',
            '{"1": 10, "2": 8, "3": 6, "4": 4, "5": 2, "participation": 1}',
            1.0,
            2.0
        ]);
    }
    $messages[] = "✅ Default scoring systems created for existing events";

} catch (Exception $e) {
    $messages[] = "❌ Error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Initialize Missing Tables | SC_IMS Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .message {
            padding: 0.75rem 1rem;
            margin: 0.5rem 0;
            border-radius: 4px;
            border-left: 4px solid #28a745;
            background: #d4edda;
            color: #155724;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 1rem;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Tables Initialization</h1>
        <p>This script creates missing database tables required for the category management system.</p>
        
        <h3>Results:</h3>
        <?php foreach ($messages as $message): ?>
            <div class="message <?php echo strpos($message, '❌') !== false ? 'error' : ''; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endforeach; ?>
        
        <a href="sport-categories.php" class="btn">← Back to Categories</a>
        <a href="manage-category.php?event_id=1&sport_id=1&category_id=2" class="btn">Test Category Management</a>
    </div>
</body>
</html>
