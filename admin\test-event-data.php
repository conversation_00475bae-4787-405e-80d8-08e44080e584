<?php
/**
 * Test event data loading
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

echo "Testing event data loading...<br><br>";

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    echo "Database connection: OK<br>";

    $event_id = 1; // Test with event ID 1
    echo "Testing with event_id: $event_id<br>";

    // Test getEventById
    $event = getEventById($conn, $event_id);
    if ($event) {
        echo "Event found: " . htmlspecialchars($event['name']) . "<br>";
        echo "Event status: " . $event['status'] . "<br>";
    } else {
        echo "Event not found!<br>";
    }

    // Test getEventSports
    $event_sports = getEventSports($conn, $event_id);
    echo "Event sports count: " . count($event_sports) . "<br>";

    // Test getDepartments
    $departments = getDepartments($conn);
    echo "Departments count: " . count($departments) . "<br>";

    // Test getAvailableSports
    $available_sports = getAvailableSports($conn, $event_id);
    echo "Available sports count: " . count($available_sports) . "<br>";

    // Test getEventStandings
    $standings = getEventStandings($conn, $event_id);
    echo "Standings count: " . count($standings) . "<br>";

    // Test getEventRecentMatches
    $recent_matches = getEventRecentMatches($conn, $event_id);
    echo "Recent matches count: " . count($recent_matches) . "<br>";

    echo "<br>All data loading tests completed successfully!";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "<br>";
    echo "Stack trace: " . $e->getTraceAsString() . "<br>";
}
?>
