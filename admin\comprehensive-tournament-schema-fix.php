<?php
/**
 * Comprehensive Tournament Schema Fix
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script fixes the tournament database schema issues by:
 * 1. Creating missing tournament-related tables
 * 2. Adding missing columns to existing tables
 * 3. Setting up proper foreign key relationships
 * 4. Ensuring data integrity
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

$errors = [];
$messages = [];
$changes_made = [];

?>
<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive Tournament Schema Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Comprehensive Tournament Schema Fix</h1>
        <p>This tool will fix all tournament-related database schema issues.</p>
        
        <?php
        try {
            echo '<div class="step">';
            echo '<h2>📋 Step 1: Analyze Current Database Structure</h2>';
            
            // Check existing tables
            $stmt = $conn->prepare("SHOW TABLES");
            $stmt->execute();
            $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo '<p><strong>Existing tables:</strong> ' . count($existing_tables) . '</p>';
            
            // Check matches table structure
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $matches_columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $existing_matches_columns = array_column($matches_columns, 'Field');
            
            echo '<p><strong>Current matches table columns:</strong></p>';
            echo '<ul>';
            foreach ($existing_matches_columns as $column) {
                echo '<li>' . htmlspecialchars($column) . '</li>';
            }
            echo '</ul>';
            
            // Check for tournament columns
            $tournament_columns = [
                'tournament_structure_id' => 'INT NULL',
                'tournament_round_id' => 'INT NULL',
                'bracket_position' => 'VARCHAR(50) NULL',
                'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
            ];
            
            $missing_columns = [];
            foreach ($tournament_columns as $column => $definition) {
                if (!in_array($column, $existing_matches_columns)) {
                    $missing_columns[$column] = $definition;
                }
            }
            
            if (empty($missing_columns)) {
                echo '<div class="success">✅ All tournament columns already exist in matches table</div>';
            } else {
                echo '<div class="warning">⚠️ Missing tournament columns: ' . implode(', ', array_keys($missing_columns)) . '</div>';
            }
            
            echo '</div>';
            
            // Step 2: Create missing tournament tables
            echo '<div class="step">';
            echo '<h2>🏗️ Step 2: Create Missing Tournament Tables</h2>';
            
            // Check and create tournament_structures table
            if (!in_array('tournament_structures', $existing_tables)) {
                echo '<p>Creating tournament_structures table...</p>';
                $sql = "CREATE TABLE tournament_structures (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    event_sport_id INT NOT NULL,
                    tournament_format_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                    participant_count INT DEFAULT 0,
                    total_rounds INT DEFAULT 0,
                    current_round INT DEFAULT 0,
                    seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
                    bracket_data JSON,
                    advancement_rules JSON,
                    scoring_config JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                    FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE RESTRICT,
                    INDEX idx_event_sport (event_sport_id),
                    INDEX idx_status (status)
                )";
                $conn->exec($sql);
                $changes_made[] = "Created tournament_structures table";
                echo '<div class="success">✅ Created tournament_structures table</div>';
            } else {
                echo '<div class="info">ℹ️ tournament_structures table already exists</div>';
            }
            
            // Check and create tournament_rounds table
            if (!in_array('tournament_rounds', $existing_tables)) {
                echo '<p>Creating tournament_rounds table...</p>';
                $sql = "CREATE TABLE tournament_rounds (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    tournament_structure_id INT NOT NULL,
                    round_number INT NOT NULL,
                    round_name VARCHAR(100) NOT NULL,
                    round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
                    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                    start_date DATETIME,
                    end_date DATETIME,
                    matches_count INT DEFAULT 0,
                    completed_matches INT DEFAULT 0,
                    advancement_criteria JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number),
                    INDEX idx_tournament_structure (tournament_structure_id),
                    INDEX idx_status (status)
                )";
                $conn->exec($sql);
                $changes_made[] = "Created tournament_rounds table";
                echo '<div class="success">✅ Created tournament_rounds table</div>';
            } else {
                echo '<div class="info">ℹ️ tournament_rounds table already exists</div>';
            }
            
            // Check and create tournament_participants table
            if (!in_array('tournament_participants', $existing_tables)) {
                echo '<p>Creating tournament_participants table...</p>';
                $sql = "CREATE TABLE tournament_participants (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    tournament_structure_id INT NOT NULL,
                    registration_id INT NOT NULL,
                    seed_number INT,
                    group_assignment VARCHAR(10),
                    current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
                    points DECIMAL(10,2) DEFAULT 0,
                    wins INT DEFAULT 0,
                    losses INT DEFAULT 0,
                    draws INT DEFAULT 0,
                    performance_data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
                    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_tournament_participant (tournament_structure_id, registration_id),
                    INDEX idx_tournament_structure (tournament_structure_id),
                    INDEX idx_status (current_status)
                )";
                $conn->exec($sql);
                $changes_made[] = "Created tournament_participants table";
                echo '<div class="success">✅ Created tournament_participants table</div>';
            } else {
                echo '<div class="info">ℹ️ tournament_participants table already exists</div>';
            }
            
            echo '</div>';
            
            // Step 3: Add missing columns to matches table
            echo '<div class="step">';
            echo '<h2>🔧 Step 3: Add Missing Columns to Matches Table</h2>';
            
            if (!empty($missing_columns)) {
                foreach ($missing_columns as $column_name => $column_definition) {
                    echo "<p>Adding {$column_name} column...</p>";
                    $sql = "ALTER TABLE matches ADD COLUMN {$column_name} {$column_definition}";
                    $conn->exec($sql);
                    $changes_made[] = "Added {$column_name} column to matches table";
                    echo "<div class='success'>✅ Added {$column_name} column</div>";
                }
            } else {
                echo '<div class="info">ℹ️ All required columns already exist in matches table</div>';
            }
            
            echo '</div>';
            
            // Step 4: Add foreign key constraints
            echo '<div class="step">';
            echo '<h2>🔗 Step 4: Add Foreign Key Constraints</h2>';
            
            // Check if foreign keys already exist
            $stmt = $conn->prepare("
                SELECT CONSTRAINT_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'matches' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            $stmt->execute();
            $existing_fks = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Add foreign key for tournament_structure_id if it doesn't exist
            $fk_tournament_structure = false;
            foreach ($existing_fks as $fk) {
                if (strpos($fk, 'tournament_structure') !== false) {
                    $fk_tournament_structure = true;
                    break;
                }
            }
            
            if (!$fk_tournament_structure && in_array('tournament_structure_id', $existing_matches_columns)) {
                try {
                    echo '<p>Adding foreign key constraint for tournament_structure_id...</p>';
                    $sql = "ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_structure 
                            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE";
                    $conn->exec($sql);
                    $changes_made[] = "Added foreign key constraint for tournament_structure_id";
                    echo '<div class="success">✅ Added foreign key constraint for tournament_structure_id</div>';
                } catch (Exception $e) {
                    echo '<div class="warning">⚠️ Could not add foreign key for tournament_structure_id: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
                echo '<div class="info">ℹ️ Foreign key constraint for tournament_structure_id already exists or column missing</div>';
            }
            
            // Add foreign key for tournament_round_id if it doesn't exist
            $fk_tournament_round = false;
            foreach ($existing_fks as $fk) {
                if (strpos($fk, 'tournament_round') !== false) {
                    $fk_tournament_round = true;
                    break;
                }
            }
            
            if (!$fk_tournament_round && in_array('tournament_round_id', $existing_matches_columns)) {
                try {
                    echo '<p>Adding foreign key constraint for tournament_round_id...</p>';
                    $sql = "ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_round 
                            FOREIGN KEY (tournament_round_id) REFERENCES tournament_rounds(id) ON DELETE CASCADE";
                    $conn->exec($sql);
                    $changes_made[] = "Added foreign key constraint for tournament_round_id";
                    echo '<div class="success">✅ Added foreign key constraint for tournament_round_id</div>';
                } catch (Exception $e) {
                    echo '<div class="warning">⚠️ Could not add foreign key for tournament_round_id: ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            } else {
                echo '<div class="info">ℹ️ Foreign key constraint for tournament_round_id already exists or column missing</div>';
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Error during schema fix: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
        }
        ?>
        
        <!-- Summary -->
        <div class="step">
            <h2>📊 Summary</h2>
            
            <?php if (!empty($changes_made)): ?>
                <div class="success">
                    <h3>✅ Changes Made Successfully:</h3>
                    <ul>
                        <?php foreach ($changes_made as $change): ?>
                            <li><?php echo htmlspecialchars($change); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php else: ?>
                <div class="info">
                    <h3>ℹ️ No Changes Needed</h3>
                    <p>All tournament schema components are already in place.</p>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($errors)): ?>
                <div class="error">
                    <h3>❌ Errors Encountered:</h3>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Next Steps -->
        <div class="step">
            <h2>🚀 Next Steps</h2>
            <p>Now that the schema has been fixed, you can:</p>
            <ul>
                <li><a href="check-matches-table.php" class="btn">🔍 Verify Schema Fix</a></li>
                <li><a href="test-tournament-creation-now.php" class="btn btn-success">🧪 Test Tournament Creation</a></li>
                <li><a href="manage-event.php?id=1" class="btn btn-warning">📋 Manage Events</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
