<?php
/**
 * Complete Enhanced Sport Types Integration
 * SC_IMS Sports Competition and Event Management System
 */

require_once '../config/database.php';

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Completing Enhanced Sport Types Integration</h2>\n";
    
    // Check if tournament_formats table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'tournament_formats'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p>❌ tournament_formats table does not exist. Creating it...</p>\n";
        
        // Create tournament_formats table
        $sql = "CREATE TABLE tournament_formats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            code VARCHAR(50) UNIQUE NOT NULL,
            description TEXT,
            sport_type_category ENUM('individual', 'team', 'academic', 'judged', 'traditional', 'performance', 'all') NOT NULL,
            min_participants INT DEFAULT 2,
            max_participants INT NULL,
            requires_seeding BOOLEAN DEFAULT FALSE,
            supports_byes B<PERSON><PERSON>EAN DEFAULT TRUE,
            advancement_type ENUM('elimination', 'points', 'hybrid') DEFAULT 'elimination',
            rounds_formula VARCHAR(100),
            matches_formula VARCHAR(100),
            algorithm_class VARCHAR(100),
            configuration JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $conn->exec($sql);
        echo "<p>✅ tournament_formats table created successfully</p>\n";
        
        // Insert basic tournament formats
        $formats = [
            [
                'name' => 'Single Elimination',
                'code' => 'single_elimination',
                'description' => 'Knockout tournament where teams/participants are eliminated after one loss',
                'sport_type_category' => 'traditional',
                'min_participants' => 2,
                'max_participants' => null,
                'requires_seeding' => true,
                'supports_byes' => true,
                'advancement_type' => 'elimination',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n-1',
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'configuration' => '{"bracket_seeding": true, "third_place_match": false}'
            ],
            [
                'name' => 'Double Elimination',
                'code' => 'double_elimination',
                'description' => 'Two-bracket system with winner\'s and loser\'s brackets',
                'sport_type_category' => 'traditional',
                'min_participants' => 3,
                'max_participants' => null,
                'requires_seeding' => true,
                'supports_byes' => true,
                'advancement_type' => 'elimination',
                'rounds_formula' => 'ceil(log2(n))*2-1',
                'matches_formula' => '2*n-2',
                'algorithm_class' => 'DoubleEliminationAlgorithm',
                'configuration' => '{"bracket_seeding": true, "grand_final_reset": true}'
            ],
            [
                'name' => 'Round Robin',
                'code' => 'round_robin',
                'description' => 'Every team/participant plays every other team/participant once',
                'sport_type_category' => 'traditional',
                'min_participants' => 3,
                'max_participants' => 16,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '1',
                'matches_formula' => 'n*(n-1)/2',
                'algorithm_class' => 'RoundRobinAlgorithm',
                'configuration' => '{"points_win": 3, "points_draw": 1, "points_loss": 0}'
            ],
            [
                'name' => 'Judged Rounds',
                'code' => 'judged_rounds',
                'description' => 'Multiple judged rounds with scoring criteria',
                'sport_type_category' => 'judged',
                'min_participants' => 3,
                'max_participants' => null,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '3',
                'matches_formula' => 'n*3',
                'algorithm_class' => 'JudgedRoundsAlgorithm',
                'configuration' => '{"rounds": ["preliminary", "semifinal", "final"], "judge_scoring": true}'
            ],
            [
                'name' => 'Performance Showcase',
                'code' => 'performance_showcase',
                'description' => 'Multi-round showcase format with artistic presentation',
                'sport_type_category' => 'performance',
                'min_participants' => 3,
                'max_participants' => 50,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '3',
                'matches_formula' => 'n*3',
                'algorithm_class' => 'PerformanceShowcaseAlgorithm',
                'configuration' => '{"rounds": ["audition", "semifinal", "final"], "audience_voting": true}'
            ],
            [
                'name' => 'Swiss System',
                'code' => 'swiss_system',
                'description' => 'Pairing based on current standings without elimination',
                'sport_type_category' => 'academic',
                'min_participants' => 4,
                'max_participants' => null,
                'requires_seeding' => false,
                'supports_byes' => true,
                'advancement_type' => 'points',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n*ceil(log2(n))/2',
                'algorithm_class' => 'SwissSystemAlgorithm',
                'configuration' => '{"pairing_algorithm": "swiss", "avoid_rematches": true}'
            ]
        ];
        
        $insertStmt = $conn->prepare("
            INSERT INTO tournament_formats 
            (name, code, description, sport_type_category, min_participants, max_participants, 
             requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula, 
             algorithm_class, configuration) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($formats as $format) {
            $insertStmt->execute([
                $format['name'],
                $format['code'],
                $format['description'],
                $format['sport_type_category'],
                $format['min_participants'],
                $format['max_participants'],
                $format['requires_seeding'],
                $format['supports_byes'],
                $format['advancement_type'],
                $format['rounds_formula'],
                $format['matches_formula'],
                $format['algorithm_class'],
                $format['configuration']
            ]);
        }
        
        echo "<p>✅ Basic tournament formats inserted successfully</p>\n";
        
    } else {
        echo "<p>✅ tournament_formats table already exists</p>\n";
        
        // Check if it has the performance category
        $stmt = $conn->query("SHOW COLUMNS FROM tournament_formats LIKE 'sport_type_category'");
        $column = $stmt->fetch();
        
        if ($column && strpos($column['Type'], 'performance') === false) {
            echo "<p>🔄 Adding 'performance' category to sport_type_category enum...</p>\n";
            
            $sql = "ALTER TABLE tournament_formats 
                    MODIFY COLUMN sport_type_category 
                    ENUM('individual', 'team', 'academic', 'judged', 'traditional', 'performance', 'all') NOT NULL";
            $conn->exec($sql);
            
            echo "<p>✅ 'performance' category added successfully</p>\n";
        } else {
            echo "<p>✅ 'performance' category already exists in sport_type_category</p>\n";
        }
    }
    
    // Verify integration by checking sport types and tournament formats compatibility
    echo "<h3>Verifying Integration</h3>\n";
    
    // Check sport types
    $stmt = $conn->query("SELECT category, COUNT(*) as count FROM sport_types GROUP BY category");
    $sportTypes = $stmt->fetchAll();
    
    echo "<p><strong>Sport Type Categories:</strong></p>\n<ul>\n";
    foreach ($sportTypes as $type) {
        echo "<li>{$type['category']}: {$type['count']} types</li>\n";
    }
    echo "</ul>\n";
    
    // Check tournament formats
    $stmt = $conn->query("SELECT sport_type_category, COUNT(*) as count FROM tournament_formats GROUP BY sport_type_category");
    $formatTypes = $stmt->fetchAll();
    
    echo "<p><strong>Tournament Format Categories:</strong></p>\n<ul>\n";
    foreach ($formatTypes as $type) {
        echo "<li>{$type['sport_type_category']}: {$type['count']} formats</li>\n";
    }
    echo "</ul>\n";
    
    // Test the enhanced sport types with tournament system
    echo "<h3>Testing Enhanced Sport Types Integration</h3>\n";
    
    $stmt = $conn->query("
        SELECT s.name as sport_name, st.category as sport_category, 
               COUNT(tf.id) as available_formats
        FROM sports s 
        LEFT JOIN sport_types st ON s.sport_type_id = st.id 
        LEFT JOIN tournament_formats tf ON (tf.sport_type_category = st.category OR tf.sport_type_category = 'all')
        GROUP BY s.id, s.name, st.category
        ORDER BY st.category, s.name
        LIMIT 10
    ");
    
    $testResults = $stmt->fetchAll();
    
    echo "<p><strong>Sport-Tournament Format Compatibility (Sample):</strong></p>\n<table border='1' style='border-collapse: collapse;'>\n";
    echo "<tr><th>Sport Name</th><th>Category</th><th>Available Formats</th></tr>\n";
    foreach ($testResults as $result) {
        echo "<tr><td>{$result['sport_name']}</td><td>{$result['sport_category']}</td><td>{$result['available_formats']}</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<h3>✅ Enhanced Sport Types Integration Completed Successfully!</h3>\n";
    echo "<p>The enhanced sport type system is now fully integrated with the tournament management system.</p>\n";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>\n";
}
?>
