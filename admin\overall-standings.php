<?php
/**
 * Overall Winner Calculation and Standings Interface
 * Manages cross-sport scoring and determines overall event winners
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/department_registration.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

// Get events for dropdown
$stmt = $conn->prepare("SELECT id, name, start_date, end_date, status FROM events ORDER BY start_date DESC");
$stmt->execute();
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

$selected_event_id = $_GET['event_id'] ?? ($events[0]['id'] ?? null);
$standings = [];
$event_info = null;

if ($selected_event_id) {
    // Get event information
    $stmt = $conn->prepare("
        SELECT e.*, d.name as winner_name, d.abbreviation as winner_abbr, d.color_code as winner_color
        FROM events e
        LEFT JOIN departments d ON e.overall_winner_department_id = d.id
        WHERE e.id = ?
    ");
    $stmt->execute([$selected_event_id]);
    $event_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get overall standings
    $standings = getOverallStandings($conn, $selected_event_id);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Overall Standings - SC_IMS Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .standings-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; }
        .event-title { font-size: 2em; margin-bottom: 10px; }
        .winner-announcement { background: rgba(255,255,255,0.1); padding: 20px; border-radius: 8px; margin-top: 20px; }
        .winner-badge { display: inline-flex; align-items: center; gap: 10px; background: #ffd700; color: #333; padding: 10px 20px; border-radius: 25px; font-weight: bold; }
        .standings-table { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .standings-row { display: grid; grid-template-columns: 60px 1fr 120px 120px 120px 120px 150px; align-items: center; padding: 15px 20px; border-bottom: 1px solid #eee; }
        .standings-header-row { background: #f8f9fa; font-weight: bold; color: #495057; }
        .rank-badge { width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; color: white; }
        .rank-1 { background: #ffd700; color: #333; }
        .rank-2 { background: #c0c0c0; color: #333; }
        .rank-3 { background: #cd7f32; color: white; }
        .rank-other { background: #6c757d; }
        .department-info { display: flex; align-items: center; gap: 15px; }
        .department-avatar { width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.9em; }
        .score-breakdown { display: flex; flex-direction: column; align-items: center; }
        .total-points { font-size: 1.2em; font-weight: bold; color: #007bff; }
        .score-details { font-size: 0.8em; color: #666; }
        .action-buttons { display: flex; gap: 10px; }
        .empty-standings { text-align: center; padding: 60px 20px; color: #666; }
        .scoring-controls { background: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .score-input-modal .modal-dialog { max-width: 600px; }
        .sport-score-form { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="main-content">
        <div class="content-header">
            <h1><i class="fas fa-trophy"></i> Overall Standings</h1>
            <div class="header-actions">
                <select id="eventSelect" class="form-control" style="width: 300px;">
                    <option value="">Select Event</option>
                    <?php foreach ($events as $event): ?>
                        <option value="<?php echo $event['id']; ?>" <?php echo $event['id'] == $selected_event_id ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($event['name']); ?> (<?php echo date('M Y', strtotime($event['start_date'])); ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
                <button class="btn btn-success" onclick="calculateStandings()" <?php echo !$selected_event_id ? 'disabled' : ''; ?>>
                    <i class="fas fa-calculator"></i> Calculate Standings
                </button>
            </div>
        </div>

        <?php if ($event_info): ?>
        <div class="standings-header">
            <div class="event-title"><?php echo htmlspecialchars($event_info['name']); ?></div>
            <div style="opacity: 0.9;">
                <i class="fas fa-calendar"></i> <?php echo date('F j, Y', strtotime($event_info['start_date'])); ?> - <?php echo date('F j, Y', strtotime($event_info['end_date'])); ?>
            </div>
            
            <?php if ($event_info['overall_winner_department_id']): ?>
            <div class="winner-announcement">
                <h3><i class="fas fa-crown"></i> Overall Winner</h3>
                <div class="winner-badge">
                    <div class="department-avatar" style="background-color: <?php echo $event_info['winner_color'] ?? '#ffd700'; ?>">
                        <?php echo strtoupper(substr($event_info['winner_abbr'] ?? $event_info['winner_name'], 0, 2)); ?>
                    </div>
                    <?php echo htmlspecialchars($event_info['winner_name']); ?>
                </div>
                <div style="margin-top: 10px; opacity: 0.8;">
                    <i class="fas fa-clock"></i> Announced on <?php echo date('F j, Y g:i A', strtotime($event_info['overall_winner_announced_at'])); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="scoring-controls">
            <div style="display: flex; justify-content: between; align-items: center;">
                <div>
                    <h4><i class="fas fa-chart-line"></i> Scoring Management</h4>
                    <p class="text-muted">Record sport results and manage overall scoring</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showScoreInputModal()" <?php echo !$selected_event_id ? 'disabled' : ''; ?>>
                        <i class="fas fa-plus"></i> Record Sport Result
                    </button>
                    <button class="btn btn-outline-info" onclick="showScoringSystemModal()" <?php echo !$selected_event_id ? 'disabled' : ''; ?>>
                        <i class="fas fa-cog"></i> Scoring System
                    </button>
                </div>
            </div>
        </div>

        <?php if (empty($standings)): ?>
            <div class="empty-standings">
                <i class="fas fa-trophy fa-3x" style="color: #ddd; margin-bottom: 20px;"></i>
                <h3>No Standings Available</h3>
                <p>No departments have been scored yet, or no event is selected.</p>
                <?php if ($selected_event_id): ?>
                    <button class="btn btn-primary" onclick="showScoreInputModal()">
                        <i class="fas fa-plus"></i> Record First Result
                    </button>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="standings-table">
                <div class="standings-row standings-header-row">
                    <div>Rank</div>
                    <div>Department</div>
                    <div>Total Points</div>
                    <div>Sports Won</div>
                    <div>Sports Played</div>
                    <div>Avg Position</div>
                    <div>Actions</div>
                </div>
                
                <?php foreach ($standings as $standing): ?>
                    <div class="standings-row">
                        <div>
                            <div class="rank-badge rank-<?php echo $standing['overall_rank'] <= 3 ? $standing['overall_rank'] : 'other'; ?>">
                                <?php echo $standing['overall_rank']; ?>
                            </div>
                        </div>
                        <div class="department-info">
                            <div class="department-avatar" style="background-color: <?php echo $standing['color_code'] ?? '#007bff'; ?>">
                                <?php echo strtoupper(substr($standing['abbreviation'] ?? $standing['department_name'], 0, 2)); ?>
                            </div>
                            <div>
                                <strong><?php echo htmlspecialchars($standing['department_name']); ?></strong>
                                <?php if ($standing['is_overall_winner']): ?>
                                    <i class="fas fa-crown" style="color: #ffd700; margin-left: 5px;" title="Overall Winner"></i>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="score-breakdown">
                            <div class="total-points"><?php echo number_format($standing['total_points'], 1); ?></div>
                            <div class="score-details">points</div>
                        </div>
                        <div class="score-breakdown">
                            <div class="total-points"><?php echo $standing['sports_won']; ?></div>
                            <div class="score-details">wins</div>
                        </div>
                        <div class="score-breakdown">
                            <div class="total-points"><?php echo $standing['sports_participated']; ?></div>
                            <div class="score-details">sports</div>
                        </div>
                        <div class="score-breakdown">
                            <div class="total-points"><?php echo number_format($standing['average_position'], 1); ?></div>
                            <div class="score-details">avg pos</div>
                        </div>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-info" onclick="showScoreBreakdown(<?php echo $standing['department_id']; ?>)">
                                <i class="fas fa-chart-bar"></i> Details
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Score Input Modal -->
    <div class="modal fade" id="scoreInputModal" tabindex="-1">
        <div class="modal-dialog score-input-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus"></i> Record Sport Result</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <form id="scoreInputForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="record_sport_score">
                        <input type="hidden" name="event_id" value="<?php echo $selected_event_id; ?>">
                        
                        <div class="sport-score-form">
                            <div class="form-group">
                                <label>Department *</label>
                                <select name="department_id" class="form-control" required>
                                    <option value="">Select Department</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>Sport *</label>
                                <select name="sport_id" class="form-control" required>
                                    <option value="">Select Sport</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>Final Position *</label>
                                <select name="position" class="form-control" required>
                                    <option value="">Select Position</option>
                                    <option value="1">1st Place</option>
                                    <option value="2">2nd Place</option>
                                    <option value="3">3rd Place</option>
                                    <option value="4">4th Place</option>
                                    <option value="5">5th Place</option>
                                    <option value="6">6th Place</option>
                                    <option value="7">7th Place</option>
                                    <option value="8">8th Place</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>Penalty Points</label>
                                <input type="number" name="penalty_points" class="form-control" step="0.1" value="0">
                                <small class="form-text text-muted">Points to deduct (if any)</small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Performance Notes</label>
                            <textarea name="performance_notes" class="form-control" rows="3" placeholder="Additional performance details or notes"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Record Result</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Score Breakdown Modal -->
    <div class="modal fade" id="scoreBreakdownModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-chart-bar"></i> Score Breakdown</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="scoreBreakdownContent">
                        <!-- Content will be loaded dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Event selection change
        $('#eventSelect').change(function() {
            const eventId = $(this).val();
            if (eventId) {
                window.location.href = `overall-standings.php?event_id=${eventId}`;
            }
        });

        // Calculate standings
        function calculateStandings() {
            const eventId = $('#eventSelect').val();
            if (!eventId) {
                alert('Please select an event first');
                return;
            }

            if (!confirm('This will recalculate all standings based on current sport results. Continue?')) {
                return;
            }

            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: {
                    action: 'calculate_overall_standings',
                    event_id: eventId,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert(`Standings calculated successfully! ${response.standings_count} departments ranked.`);
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while calculating standings');
                }
            });
        }

        // Show score input modal
        function showScoreInputModal() {
            const eventId = $('#eventSelect').val();
            if (!eventId) {
                alert('Please select an event first');
                return;
            }

            // Load departments and sports for the event
            loadDepartmentsAndSports(eventId);
            $('#scoreInputModal').modal('show');
        }

        // Load departments and sports for score input
        function loadDepartmentsAndSports(eventId) {
            // Load registered departments
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: {
                    action: 'get_department_registrations',
                    event_id: eventId,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select Department</option>';
                        response.registrations.forEach(function(reg) {
                            if (reg.status === 'approved') {
                                options += `<option value="${reg.department_id}">${reg.department_name}</option>`;
                            }
                        });
                        $('select[name="department_id"]').html(options);
                    }
                }
            });

            // Load sports for the event
            $.ajax({
                url: '../ajax/get-event-sports.php',
                method: 'POST',
                data: {
                    event_id: eventId,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        let options = '<option value="">Select Sport</option>';
                        response.sports.forEach(function(sport) {
                            options += `<option value="${sport.sport_id}">${sport.sport_name}</option>`;
                        });
                        $('select[name="sport_id"]').html(options);
                    }
                }
            });
        }

        // Score input form submission
        $('#scoreInputForm').submit(function(e) {
            e.preventDefault();
            
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: $(this).serialize(),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#scoreInputModal').modal('hide');
                        alert(`Result recorded successfully! ${response.points_earned} points earned.`);
                        location.reload();
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('An error occurred while recording the result');
                }
            });
        });

        // Show score breakdown
        function showScoreBreakdown(departmentId) {
            const eventId = $('#eventSelect').val();
            
            $.ajax({
                url: 'ajax/department-registration.php',
                method: 'POST',
                data: {
                    action: 'get_department_scores_breakdown',
                    event_id: eventId,
                    department_id: departmentId,
                    csrf_token: $('input[name="csrf_token"]').val()
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        let html = '<div class="table-responsive"><table class="table table-striped">';
                        html += '<thead><tr><th>Sport</th><th>Position</th><th>Points Earned</th><th>Bonus</th><th>Penalty</th><th>Final Score</th></tr></thead><tbody>';
                        
                        if (response.scores.length === 0) {
                            html += '<tr><td colspan="6" class="text-center text-muted">No scores recorded yet</td></tr>';
                        } else {
                            response.scores.forEach(function(score) {
                                html += `
                                    <tr>
                                        <td><strong>${score.sport_name}</strong> <span class="badge badge-secondary">${score.sport_type}</span></td>
                                        <td>${score.position ? score.position + getOrdinalSuffix(score.position) : 'N/A'}</td>
                                        <td>${score.points_earned}</td>
                                        <td>${score.bonus_points}</td>
                                        <td>${score.penalty_points}</td>
                                        <td><strong>${score.final_score}</strong></td>
                                    </tr>
                                `;
                            });
                        }
                        
                        html += '</tbody></table></div>';
                        $('#scoreBreakdownContent').html(html);
                        $('#scoreBreakdownModal').modal('show');
                    }
                }
            });
        }

        // Helper function for ordinal suffixes
        function getOrdinalSuffix(num) {
            const j = num % 10;
            const k = num % 100;
            if (j == 1 && k != 11) return "st";
            if (j == 2 && k != 12) return "nd";
            if (j == 3 && k != 13) return "rd";
            return "th";
        }
    </script>
</body>
</html>
