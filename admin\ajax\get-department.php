<?php
/**
 * Get Department Data for Modal Editing
 * SC_IMS Admin Panel AJAX Endpoint
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get department ID
$department_id = $_GET['id'] ?? 0;

if (!$department_id) {
    echo json_encode([
        'success' => false,
        'message' => 'Department ID is required'
    ]);
    exit;
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Fetch department data
    $stmt = $conn->prepare("SELECT * FROM departments WHERE id = ?");
    $stmt->execute([$department_id]);
    $department = $stmt->fetch();
    
    if (!$department) {
        echo json_encode([
            'success' => false,
            'message' => 'Department not found'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'department' => $department
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching department data: ' . $e->getMessage()
    ]);
}
?>
