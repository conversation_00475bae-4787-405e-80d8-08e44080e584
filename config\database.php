<?php
/**
 * Database Configuration for SC_IMS
 * Sports Competition and Event Management System
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'SC_IMS_db';
    private $username = 'root';
    private $password = '';
    private $conn;

    /**
     * Get database connection
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }

    /**
     * Create database if it doesn't exist
     */
    public function createDatabase() {
        try {
            $conn = new PDO(
                "mysql:host=" . $this->host,
                $this->username,
                $this->password
            );
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $sql = "CREATE DATABASE IF NOT EXISTS " . $this->db_name . " CHARACTER SET utf8 COLLATE utf8_general_ci";
            $conn->exec($sql);
            
            return true;
        } catch(PDOException $e) {
            echo "Database creation error: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Initialize database tables
     */
    public function initializeTables() {
        $conn = $this->getConnection();
        
        if (!$conn) {
            return false;
        }

        try {
            // Events table
            $sql = "CREATE TABLE IF NOT EXISTS events (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                location VARCHAR(255),
                max_participants_per_department INT DEFAULT 0,
                winner_id INT NULL,
                status ENUM('upcoming', 'ongoing', 'completed', 'cancelled') DEFAULT 'upcoming',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (winner_id) REFERENCES departments(id) ON DELETE SET NULL
            )";
            $conn->exec($sql);

            // Sports table
            $sql = "CREATE TABLE IF NOT EXISTS sports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                type ENUM('traditional', 'judged', 'academic', 'custom') NOT NULL,
                scoring_method ENUM('point_based', 'set_based', 'criteria_based', 'time_based') NOT NULL,
                bracket_format ENUM('single_elimination', 'double_elimination', 'round_robin', 'multi_stage') NOT NULL,
                rules TEXT,
                max_participants INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);

            // Departments table
            $sql = "CREATE TABLE IF NOT EXISTS departments (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                abbreviation VARCHAR(10) NOT NULL UNIQUE,
                color_code VARCHAR(7) DEFAULT '#000000',
                contact_info TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);

            // Event Sports table (many-to-many relationship)
            $sql = "CREATE TABLE IF NOT EXISTS event_sports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_id INT NOT NULL,
                sport_id INT NOT NULL,
                max_teams INT DEFAULT 8,
                registration_deadline DATETIME,
                venue VARCHAR(255),
                status ENUM('registration', 'ongoing', 'completed') DEFAULT 'registration',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
                FOREIGN KEY (sport_id) REFERENCES sports(id) ON DELETE CASCADE,
                UNIQUE KEY unique_event_sport (event_id, sport_id)
            )";
            $conn->exec($sql);

            // Registrations table
            $sql = "CREATE TABLE IF NOT EXISTS registrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                department_id INT NOT NULL,
                team_name VARCHAR(255),
                participants JSON,
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('registered', 'confirmed', 'cancelled') DEFAULT 'registered',
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
                UNIQUE KEY unique_registration (event_sport_id, department_id)
            )";
            $conn->exec($sql);

            // Brackets table
            $sql = "CREATE TABLE IF NOT EXISTS brackets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                bracket_data JSON NOT NULL,
                bracket_type ENUM('single_elimination', 'double_elimination', 'round_robin', 'multi_stage') NOT NULL,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Matches table (with tournament support)
            $sql = "CREATE TABLE IF NOT EXISTS matches (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                team1_id INT NOT NULL,
                team2_id INT,
                round_number INT DEFAULT 1,
                match_number INT DEFAULT 1,
                scheduled_time DATETIME,
                actual_start_time DATETIME,
                actual_end_time DATETIME,
                status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
                winner_id INT,
                venue VARCHAR(255),
                referee_notes TEXT,
                tournament_structure_id INT NULL COMMENT 'Tournament structure reference',
                tournament_round_id INT NULL COMMENT 'Tournament round reference',
                bracket_position VARCHAR(50) NULL COMMENT 'Position in tournament bracket',
                is_bye_match BOOLEAN DEFAULT FALSE COMMENT 'Whether this is a bye match',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                FOREIGN KEY (team1_id) REFERENCES registrations(id) ON DELETE CASCADE,
                FOREIGN KEY (team2_id) REFERENCES registrations(id) ON DELETE CASCADE,
                FOREIGN KEY (winner_id) REFERENCES registrations(id) ON DELETE SET NULL
            )";
            $conn->exec($sql);

            // Scores table
            $sql = "CREATE TABLE IF NOT EXISTS scores (
                id INT AUTO_INCREMENT PRIMARY KEY,
                match_id INT NOT NULL,
                team1_score INT DEFAULT 0,
                team2_score INT DEFAULT 0,
                period INT DEFAULT 1,
                notes TEXT,
                is_final BOOLEAN DEFAULT FALSE,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (match_id) REFERENCES matches(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Rankings table
            $sql = "CREATE TABLE IF NOT EXISTS rankings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_id INT NOT NULL,
                department_id INT NOT NULL,
                total_points DECIMAL(10,2) DEFAULT 0,
                wins INT DEFAULT 0,
                losses INT DEFAULT 0,
                draws INT DEFAULT 0,
                rank_position INT DEFAULT 0,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
                FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
                UNIQUE KEY unique_event_department (event_id, department_id)
            )";
            $conn->exec($sql);

            // Admin users table (for admin authentication only)
            $sql = "CREATE TABLE IF NOT EXISTS admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(255) NOT NULL UNIQUE,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(255),
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);

            // Admin sessions table
            $sql = "CREATE TABLE IF NOT EXISTS admin_sessions (
                id VARCHAR(128) PRIMARY KEY,
                admin_user_id INT NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Audit logs table
            $sql = "CREATE TABLE IF NOT EXISTS audit_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_user_id INT,
                action VARCHAR(100) NOT NULL,
                table_name VARCHAR(50) NOT NULL,
                record_id INT,
                old_values JSON,
                new_values JSON,
                ip_address VARCHAR(45),
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE SET NULL
            )";
            $conn->exec($sql);

            // Sport Categories table
            $sql = "CREATE TABLE IF NOT EXISTS sport_categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                category_name VARCHAR(255) NOT NULL,
                category_type ENUM('men', 'women', 'mixed', 'open', 'youth', 'senior', 'other') NOT NULL,
                category_type_custom VARCHAR(100) NULL,
                referee_name VARCHAR(255),
                referee_email VARCHAR(255),
                venue VARCHAR(255),
                max_participants INT DEFAULT 0,
                registration_deadline DATETIME,
                status ENUM('registration', 'ongoing', 'completed', 'cancelled') DEFAULT 'registration',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                INDEX idx_event_sport_category (event_sport_id, category_name)
            )";
            $conn->exec($sql);

            // Tournament Formats table
            $sql = "CREATE TABLE IF NOT EXISTS tournament_formats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                code VARCHAR(50) NOT NULL UNIQUE,
                description TEXT,
                sport_types VARCHAR(255) DEFAULT 'team,individual',
                min_participants INT DEFAULT 2,
                max_participants INT NULL,
                algorithm_class VARCHAR(100) DEFAULT 'SingleEliminationAlgorithm',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_sport_types (sport_types),
                INDEX idx_code (code)
            )";
            $conn->exec($sql);

            // Insert default tournament formats
            $default_formats = [
                ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null, 'SingleEliminationAlgorithm'],
                ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null, 'DoubleEliminationAlgorithm'],
                ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16, 'RoundRobinAlgorithm'],
                ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null, 'SwissSystemAlgorithm'],
                ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null, 'JudgedRoundsAlgorithm'],
                ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null, 'MultiStageAlgorithm']
            ];

            foreach ($default_formats as $format) {
                $stmt = $conn->prepare("
                    INSERT IGNORE INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants, algorithm_class)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute($format);
            }

            // Tournament Structures table
            $sql = "CREATE TABLE IF NOT EXISTS tournament_structures (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                tournament_format_id INT NOT NULL,
                name VARCHAR(255) NOT NULL,
                status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                participant_count INT DEFAULT 0,
                total_rounds INT DEFAULT 0,
                current_round INT DEFAULT 0,
                seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
                bracket_data JSON,
                advancement_rules JSON,
                scoring_config JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Tournament Rounds table
            $sql = "CREATE TABLE IF NOT EXISTS tournament_rounds (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tournament_structure_id INT NOT NULL,
                round_number INT NOT NULL,
                round_name VARCHAR(100) NOT NULL,
                round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
                status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                start_date DATETIME,
                end_date DATETIME,
                matches_count INT DEFAULT 0,
                completed_matches INT DEFAULT 0,
                advancement_criteria JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Tournament Participants table
            $sql = "CREATE TABLE IF NOT EXISTS tournament_participants (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tournament_structure_id INT NOT NULL,
                registration_id INT NOT NULL,
                seed_number INT,
                group_assignment VARCHAR(10),
                current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
                points DECIMAL(10,2) DEFAULT 0,
                wins INT DEFAULT 0,
                losses INT DEFAULT 0,
                draws INT DEFAULT 0,
                performance_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
                FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);

            // Create default admin user after tables are created
            $this->createDefaultAdmin();

            return true;
        } catch(PDOException $e) {
            echo "Table creation error: " . $e->getMessage();
            return false;
        }
    }

    /**
     * Create default admin user
     */
    public function createDefaultAdmin() {
        $conn = $this->getConnection();

        if (!$conn) {
            return false;
        }

        try {
            // Check if admin already exists
            $sql = "SELECT id FROM admin_users WHERE username = 'admin' LIMIT 1";
            $stmt = $conn->prepare($sql);
            $stmt->execute();

            if ($stmt->fetch()) {
                return true; // Admin already exists
            }

            // Create default admin
            $sql = "INSERT INTO admin_users (username, email, password_hash, full_name, is_active)
                    VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                'admin',
                '<EMAIL>',
                password_hash('admin123', PASSWORD_DEFAULT),
                'System Administrator',
                1
            ]);

            return true;
        } catch (Exception $e) {
            error_log("Error creating default admin: " . $e->getMessage());
            return false;
        }
    }
}
?>
