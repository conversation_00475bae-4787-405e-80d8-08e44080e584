<?php
/**
 * Admin Authentication System for SC_IMS
 * Handles admin-only login, session management, and access control
 */

// Determine the correct path to config files based on current location
$config_path = file_exists('../config/config.php') ? '../config/' : '../../config/';
require_once $config_path . 'config.php';
require_once $config_path . 'database.php';

/**
 * Start admin session
 */
function startAdminSession() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

/**
 * Check if admin is logged in
 */
function isAdminLoggedIn() {
    startAdminSession();
    return isset($_SESSION['admin_user_id']) && isset($_SESSION['admin_username']);
}

/**
 * Get current admin user
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        $sql = "SELECT id, username, email, full_name, last_login FROM admin_users WHERE id = ? AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$_SESSION['admin_user_id']]);
        return $stmt->fetch();
    } catch (Exception $e) {
        error_log("Error getting current admin: " . $e->getMessage());
        return null;
    }
}

/**
 * Require admin authentication
 */
function requireAdmin() {
    if (!isAdminLoggedIn()) {
        header('Location: ' . dirname($_SERVER['PHP_SELF']) . '/login.php');
        exit();
    }
    
    // Check session timeout
    if (isset($_SESSION['admin_last_activity']) && 
        (time() - $_SESSION['admin_last_activity']) > SESSION_TIMEOUT) {
        adminLogout();
        header('Location: ' . dirname($_SERVER['PHP_SELF']) . '/login.php?timeout=1');
        exit();
    }
    
    $_SESSION['admin_last_activity'] = time();
}

/**
 * Admin login
 */
function adminLogin($username, $password) {
    $database = new Database();
    $conn = $database->getConnection();
    
    try {
        $sql = "SELECT id, username, email, password_hash, full_name FROM admin_users 
                WHERE username = ? AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($password, $admin['password_hash'])) {
            startAdminSession();
            
            // Set session variables
            $_SESSION['admin_user_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_email'] = $admin['email'];
            $_SESSION['admin_full_name'] = $admin['full_name'];
            $_SESSION['admin_last_activity'] = time();
            
            // Update last login
            $sql = "UPDATE admin_users SET last_login = NOW() WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$admin['id']]);
            
            // Create session record
            $session_id = session_id();
            $sql = "INSERT INTO admin_sessions (id, admin_user_id, ip_address, user_agent, expires_at) 
                    VALUES (?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL ? SECOND))
                    ON DUPLICATE KEY UPDATE 
                    expires_at = DATE_ADD(NOW(), INTERVAL ? SECOND)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $session_id,
                $admin['id'],
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null,
                SESSION_TIMEOUT,
                SESSION_TIMEOUT
            ]);
            
            // Log activity
            logAdminActivity('ADMIN_LOGIN', 'admin_users', $admin['id']);
            
            return ['success' => true, 'message' => 'Login successful'];
        } else {
            return ['success' => false, 'message' => 'Invalid username or password'];
        }
    } catch (Exception $e) {
        error_log("Admin login error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Login failed. Please try again.'];
    }
}

/**
 * Admin logout
 */
function adminLogout() {
    startAdminSession();
    
    if (isAdminLoggedIn()) {
        $database = new Database();
        $conn = $database->getConnection();
        
        try {
            // Remove session record
            $session_id = session_id();
            $sql = "DELETE FROM admin_sessions WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$session_id]);
            
            // Log activity
            logAdminActivity('ADMIN_LOGOUT', 'admin_users', $_SESSION['admin_user_id']);
        } catch (Exception $e) {
            error_log("Admin logout error: " . $e->getMessage());
        }
    }
    
    // Clear session
    $_SESSION = array();
    
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    session_destroy();
}

/**
 * Log admin activity
 */
function logAdminActivity($action, $table_name, $record_id = null, $old_values = null, $new_values = null) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "INSERT INTO audit_logs (admin_user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $_SESSION['admin_user_id'] ?? null,
            $action,
            $table_name,
            $record_id,
            $old_values ? json_encode($old_values) : null,
            $new_values ? json_encode($new_values) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}

/**
 * Clean expired sessions
 */
function cleanExpiredSessions() {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $sql = "DELETE FROM admin_sessions WHERE expires_at < NOW()";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
    } catch (Exception $e) {
        error_log("Failed to clean expired sessions: " . $e->getMessage());
    }
}

// Clean expired sessions on each auth check
cleanExpiredSessions();
?>
