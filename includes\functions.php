<?php
/**
 * Common Functions for SC_IMS
 * Sports Competition and Event Management System
 */

// Include config for CSRF and other utility functions
require_once __DIR__ . '/../config/config.php';

/**
 * Get all events
 */
function getAllEvents($conn, $status = null) {
    $sql = "SELECT * FROM events";
    $params = [];
    
    if ($status) {
        $sql .= " WHERE status = ?";
        $params[] = $status;
    }
    
    $sql .= " ORDER BY start_date DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Get event by ID
 */
function getEventById($conn, $id) {
    $sql = "SELECT * FROM events WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
    return $stmt->fetch();
}

/**
 * Get all sports
 */
function getAllSports($conn) {
    $sql = "SELECT * FROM sports ORDER BY name";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Get sport by ID
 */
function getSportById($conn, $id) {
    $sql = "SELECT * FROM sports WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
    return $stmt->fetch();
}

/**
 * Get all departments
 */
function getAllDepartments($conn) {
    $sql = "SELECT * FROM departments ORDER BY name";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Get department by ID
 */
function getDepartmentById($conn, $id) {
    $sql = "SELECT * FROM departments WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
    return $stmt->fetch();
}

/**
 * Get sports for an event
 */
function getEventSports($conn, $event_id) {
    $sql = "SELECT es.*, s.name as sport_name, s.description as sport_description,
            COUNT(r.id) as registered_teams,
            COUNT(m.id) as total_matches
            FROM event_sports es
            JOIN sports s ON es.sport_id = s.id
            LEFT JOIN registrations r ON es.id = r.event_sport_id AND r.status = 'approved'
            LEFT JOIN matches m ON es.id = m.event_sport_id
            WHERE es.event_id = ?
            GROUP BY es.id
            ORDER BY es.created_at";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    return $stmt->fetchAll();
}

/**
 * Get registrations for an event sport
 */
function getEventSportRegistrations($conn, $event_sport_id) {
    $sql = "SELECT r.*, d.name as department_name, d.abbreviation, d.color_code 
            FROM registrations r 
            JOIN departments d ON r.department_id = d.id 
            WHERE r.event_sport_id = ? AND r.status = 'confirmed'
            ORDER BY d.name";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_sport_id]);
    return $stmt->fetchAll();
}

/**
 * Get matches for an event sport
 */
function getEventSportMatches($conn, $event_sport_id, $status = null) {
    $sql = "SELECT m.*, 
            d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color,
            dw.name as winner_name, dw.abbreviation as winner_abbr
            FROM matches m
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            LEFT JOIN registrations rw ON m.winner_id = rw.id
            LEFT JOIN departments dw ON rw.department_id = dw.id
            WHERE m.event_sport_id = ?";
    
    $params = [$event_sport_id];
    
    if ($status) {
        $sql .= " AND m.status = ?";
        $params[] = $status;
    }
    
    $sql .= " ORDER BY m.round_number, m.match_number";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

/**
 * Get match scores
 */
function getMatchScores($conn, $match_id) {
    $sql = "SELECT s.*, d.name as team_name, d.abbreviation, d.color_code 
            FROM scores s 
            JOIN registrations r ON s.team_id = r.id 
            JOIN departments d ON r.department_id = d.id 
            WHERE s.match_id = ? 
            ORDER BY s.timestamp";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$match_id]);
    return $stmt->fetchAll();
}

/**
 * Get current rankings for an event
 */
function getEventRankings($conn, $event_id) {
    $sql = "SELECT r.*, d.name as department_name, d.abbreviation, d.color_code 
            FROM rankings r 
            JOIN departments d ON r.department_id = d.id 
            WHERE r.event_id = ? 
            ORDER BY r.rank_position, r.total_points DESC, r.wins DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    return $stmt->fetchAll();
}

/**
 * Update rankings for an event
 */
function updateEventRankings($conn, $event_id) {
    try {
        // Get all departments participating in the event
        $sql = "SELECT DISTINCT d.id, d.name 
                FROM departments d 
                JOIN registrations r ON d.id = r.department_id 
                JOIN event_sports es ON r.event_sport_id = es.id 
                WHERE es.event_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_id]);
        $departments = $stmt->fetchAll();

        foreach ($departments as $dept) {
            // Calculate wins, losses, draws
            $sql = "SELECT 
                        COUNT(CASE WHEN m.winner_id = r.id THEN 1 END) as wins,
                        COUNT(CASE WHEN m.winner_id IS NOT NULL AND m.winner_id != r.id THEN 1 END) as losses,
                        COUNT(CASE WHEN m.status = 'completed' AND m.winner_id IS NULL THEN 1 END) as draws
                    FROM registrations r 
                    JOIN event_sports es ON r.event_sport_id = es.id 
                    JOIN matches m ON (m.team1_id = r.id OR m.team2_id = r.id) 
                    WHERE es.event_id = ? AND r.department_id = ? AND m.status = 'completed'";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$event_id, $dept['id']]);
            $stats = $stmt->fetch();

            // Calculate total points
            $total_points = ($stats['wins'] * DEFAULT_WIN_POINTS) + 
                           ($stats['draws'] * DEFAULT_DRAW_POINTS) + 
                           ($stats['losses'] * DEFAULT_LOSS_POINTS);

            // Update or insert ranking
            $sql = "INSERT INTO rankings (event_id, department_id, total_points, wins, losses, draws) 
                    VALUES (?, ?, ?, ?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    total_points = VALUES(total_points), 
                    wins = VALUES(wins), 
                    losses = VALUES(losses), 
                    draws = VALUES(draws)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([
                $event_id, 
                $dept['id'], 
                $total_points, 
                $stats['wins'], 
                $stats['losses'], 
                $stats['draws']
            ]);
        }

        // Update rank positions
        $sql = "SET @rank = 0";
        $conn->exec($sql);
        
        $sql = "UPDATE rankings 
                SET rank_position = (@rank := @rank + 1) 
                WHERE event_id = ? 
                ORDER BY total_points DESC, wins DESC, losses ASC";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_id]);

        return true;
    } catch (Exception $e) {
        error_log("Error updating rankings: " . $e->getMessage());
        return false;
    }
}

/**
 * Get live matches (ongoing)
 */
function getLiveMatches($conn) {
    $sql = "SELECT m.*, 
            es.event_id, e.name as event_name,
            s.name as sport_name,
            d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.status = 'ongoing'
            ORDER BY m.actual_start_time DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Get recent match results
 */
function getRecentResults($conn, $limit = 10) {
    $limit = (int)$limit; // Ensure it's an integer
    $sql = "SELECT m.*,
            es.event_id, e.name as event_name,
            s.name as sport_name,
            d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color,
            dw.name as winner_name, dw.abbreviation as winner_abbr
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            LEFT JOIN registrations rw ON m.winner_id = rw.id
            LEFT JOIN departments dw ON rw.department_id = dw.id
            WHERE m.status = 'completed'
            ORDER BY m.actual_end_time DESC
            LIMIT " . $limit;
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

/**
 * Get upcoming matches
 */
function getUpcomingMatches($conn, $limit = 10) {
    $limit = (int)$limit; // Ensure it's an integer
    $sql = "SELECT m.*,
            es.event_id, e.name as event_name,
            s.name as sport_name,
            d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.status = 'scheduled' AND m.scheduled_time > NOW()
            ORDER BY m.scheduled_time ASC
            LIMIT " . $limit;
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}



/**
 * Initialize sample data
 */
function initializeSampleData($conn) {
    try {
        // Check if data already exists
        $sql = "SELECT COUNT(*) as count FROM departments";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        if ($result['count'] > 0) {
            return true; // Data already exists
        }

        // Insert sample departments
        $departments = [
            ['Computer Science', 'CS', '#007bff'],
            ['Information Technology', 'IT', '#28a745'],
            ['Engineering', 'ENG', '#dc3545'],
            ['Business Administration', 'BA', '#ffc107'],
            ['Education', 'EDUC', '#6f42c1'],
            ['Arts and Sciences', 'AS', '#fd7e14']
        ];

        foreach ($departments as $dept) {
            $sql = "INSERT INTO departments (name, abbreviation, color_code) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute($dept);
        }

        // Insert sample sports
        $sports = [
            ['Basketball', 'traditional', 'point_based', 'single_elimination'],
            ['Volleyball', 'traditional', 'set_based', 'single_elimination'],
            ['Football', 'traditional', 'point_based', 'round_robin'],
            ['Chess', 'academic', 'point_based', 'round_robin'],
            ['Singing Contest', 'judged', 'criteria_based', 'single_elimination'],
            ['Dance Competition', 'judged', 'criteria_based', 'single_elimination']
        ];

        foreach ($sports as $sport) {
            $sql = "INSERT INTO sports (name, type, scoring_method, bracket_format) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute($sport);
        }

        return true;
    } catch (Exception $e) {
        error_log("Error initializing sample data: " . $e->getMessage());
        return false;
    }
}

// Additional functions for Phase 3

function getAvailableSports($conn, $event_id) {
    $sql = "SELECT s.*,
                   st.name as sport_type_name,
                   st.description as sport_type_description,
                   st.category as sport_type_category,
                   st.color_code,
                   st.icon_class
            FROM sports s
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            WHERE s.id NOT IN (
                SELECT sport_id FROM event_sports WHERE event_id = ?
            )
            ORDER BY st.category, s.name";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    return $stmt->fetchAll();
}

/**
 * Get event standings/rankings (Updated for Unified Registration System)
 */
function getEventStandings($conn, $event_id) {
    $sql = "SELECT
                d.id,
                d.name as department_name,
                d.abbreviation,
                d.color_code,
                edr.status as registration_status,
                COUNT(DISTINCT dsp.id) as sports_participated,
                COUNT(DISTINCT CASE WHEN m.winner_id = r.id THEN m.id END) as matches_won,
                COUNT(DISTINCT CASE WHEN m.status = 'completed' AND (m.team1_id = r.id OR m.team2_id = r.id) THEN m.id END) as total_matches,
                COALESCE(SUM(CASE
                    WHEN m.winner_id = r.id THEN 3
                    WHEN m.status = 'completed' AND m.winner_id IS NULL AND (m.team1_id = r.id OR m.team2_id = r.id) THEN 1
                    ELSE 0
                END), 0) as total_points
            FROM departments d
            INNER JOIN event_department_registrations edr ON d.id = edr.department_id AND edr.event_id = ?
            LEFT JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            LEFT JOIN registrations r ON r.department_sport_participation_id = dsp.id
            LEFT JOIN matches m ON (m.team1_id = r.id OR m.team2_id = r.id)
            WHERE edr.status = 'approved'
            GROUP BY d.id, d.name, d.abbreviation, d.color_code, edr.status
            ORDER BY total_points DESC, matches_won DESC, department_name";

    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    return $stmt->fetchAll();
}

/**
 * Get recent matches for an event
 */
function getEventRecentMatches($conn, $event_id, $limit = 5) {
    // Validate and sanitize limit parameter
    $limit = (int)$limit;
    if ($limit <= 0 || $limit > 100) {
        $limit = 5; // Default safe limit
    }

    $sql = "SELECT m.*,
                   s.name as sport_name,
                   d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
                   d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color,
                   dw.name as winner_name, dw.abbreviation as winner_abbr
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            LEFT JOIN registrations rw ON m.winner_id = rw.id
            LEFT JOIN departments dw ON rw.department_id = dw.id
            WHERE es.event_id = ? AND m.status = 'completed'
            ORDER BY m.actual_end_time DESC
            LIMIT " . $limit;

    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    return $stmt->fetchAll();
}

function getEventSportById($conn, $event_sport_id) {
    $sql = "SELECT es.*, s.name as sport_name, s.description as sport_description, e.name as event_name
            FROM event_sports es
            JOIN sports s ON es.sport_id = s.id
            JOIN events e ON es.event_id = e.id
            WHERE es.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_sport_id]);
    return $stmt->fetch();
}

function getRegistrations($conn, $event_sport_id) {
    $sql = "SELECT r.*, d.name as department_name, d.abbreviation, d.color_code
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ?
            ORDER BY r.registration_date DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_sport_id]);
    return $stmt->fetchAll();
}

function createRegistration($conn, $event_sport_id, $department_id, $participants) {
    try {
        $conn->beginTransaction();

        // Check if department already registered
        $sql = "SELECT id FROM registrations WHERE event_sport_id = ? AND department_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_sport_id, $department_id]);
        if ($stmt->fetch()) {
            throw new Exception('Department already registered for this sport');
        }

        // Create registration
        $sql = "INSERT INTO registrations (event_sport_id, department_id, participants, status) VALUES (?, ?, ?, 'pending')";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_sport_id, $department_id, json_encode($participants)]);

        $registration_id = $conn->lastInsertId();

        $conn->commit();
        return ['success' => true, 'registration_id' => $registration_id];

    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';
    return floor($time/31536000) . ' years ago';
}

function formatDate($date) {
    return date('M j, Y', strtotime($date));
}

function formatDateTime($datetime) {
    return date('M j, Y g:i A', strtotime($datetime));
}
?>
