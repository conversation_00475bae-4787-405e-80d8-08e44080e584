<?php
/**
 * Force Schema Rebuild - Definitive Tournament Database Fix
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script will forcefully rebuild the tournament schema by:
 * 1. Dropping and recreating problematic foreign key constraints
 * 2. Ensuring all tournament columns exist with correct definitions
 * 3. Rebuilding all tournament tables from scratch if needed
 * 4. Testing the complete workflow
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

// Disable foreign key checks temporarily
$conn->exec("SET FOREIGN_KEY_CHECKS = 0");

?>
<!DOCTYPE html>
<html>
<head>
    <title>Force Schema Rebuild</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Force Schema Rebuild</h1>
        <div class="warning">
            <strong>⚠️ WARNING:</strong> This will forcefully rebuild the tournament database schema. 
            Existing tournament data may be lost. Proceed only if you understand the consequences.
        </div>
        
        <?php
        $changes_made = [];
        $errors = [];
        
        try {
            // Step 1: Remove existing foreign key constraints on matches table
            echo '<div class="step">';
            echo '<h2>🗑️ Step 1: Remove Existing Foreign Key Constraints</h2>';
            
            // Get existing foreign keys on matches table
            $stmt = $conn->prepare("
                SELECT CONSTRAINT_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'matches' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            $stmt->execute();
            $existing_fks = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($existing_fks as $fk_name) {
                try {
                    $sql = "ALTER TABLE matches DROP FOREIGN KEY $fk_name";
                    $conn->exec($sql);
                    echo '<div class="success">✅ Dropped foreign key: ' . htmlspecialchars($fk_name) . '</div>';
                    $changes_made[] = "Dropped foreign key: $fk_name";
                } catch (Exception $e) {
                    echo '<div class="warning">⚠️ Could not drop foreign key ' . htmlspecialchars($fk_name) . ': ' . htmlspecialchars($e->getMessage()) . '</div>';
                }
            }
            
            if (empty($existing_fks)) {
                echo '<div class="info">ℹ️ No existing foreign keys found on matches table</div>';
            }
            
            echo '</div>';
            
            // Step 2: Drop and recreate tournament columns on matches table
            echo '<div class="step">';
            echo '<h2>🔧 Step 2: Rebuild Tournament Columns on Matches Table</h2>';
            
            // Get current matches table structure
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $current_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $tournament_columns = [
                'tournament_structure_id' => 'INT NULL',
                'tournament_round_id' => 'INT NULL',
                'bracket_position' => 'VARCHAR(50) NULL',
                'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
            ];
            
            // Drop existing tournament columns if they exist
            foreach ($tournament_columns as $column_name => $definition) {
                if (in_array($column_name, $current_columns)) {
                    try {
                        $sql = "ALTER TABLE matches DROP COLUMN $column_name";
                        $conn->exec($sql);
                        echo '<div class="success">✅ Dropped existing column: ' . $column_name . '</div>';
                        $changes_made[] = "Dropped column: $column_name";
                    } catch (Exception $e) {
                        echo '<div class="warning">⚠️ Could not drop column ' . $column_name . ': ' . htmlspecialchars($e->getMessage()) . '</div>';
                    }
                }
            }
            
            // Add tournament columns with correct definitions
            foreach ($tournament_columns as $column_name => $definition) {
                try {
                    $sql = "ALTER TABLE matches ADD COLUMN $column_name $definition";
                    $conn->exec($sql);
                    echo '<div class="success">✅ Added column: ' . $column_name . ' (' . $definition . ')</div>';
                    $changes_made[] = "Added column: $column_name";
                } catch (Exception $e) {
                    echo '<div class="error">❌ Failed to add column ' . $column_name . ': ' . htmlspecialchars($e->getMessage()) . '</div>';
                    $errors[] = "Failed to add column $column_name: " . $e->getMessage();
                }
            }
            
            echo '</div>';
            
            // Step 3: Ensure tournament tables exist
            echo '<div class="step">';
            echo '<h2>🏗️ Step 3: Ensure Tournament Tables Exist</h2>';
            
            // Check existing tables
            $stmt = $conn->prepare("SHOW TABLES");
            $stmt->execute();
            $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Create tournament_formats table if missing
            if (!in_array('tournament_formats', $existing_tables)) {
                echo '<p>Creating tournament_formats table...</p>';
                $sql = "CREATE TABLE tournament_formats (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    code VARCHAR(50) NOT NULL UNIQUE,
                    description TEXT,
                    sport_types VARCHAR(255) DEFAULT 'team,individual',
                    min_participants INT DEFAULT 2,
                    max_participants INT DEFAULT NULL,
                    algorithm_class VARCHAR(100) DEFAULT 'SingleEliminationAlgorithm',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $conn->exec($sql);
                echo '<div class="success">✅ Created tournament_formats table</div>';
                $changes_made[] = "Created tournament_formats table";
            } else {
                echo '<div class="info">ℹ️ tournament_formats table already exists</div>';
                
                // Ensure algorithm_class column exists
                $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'algorithm_class'");
                $stmt->execute();
                if (!$stmt->fetch()) {
                    $conn->exec("ALTER TABLE tournament_formats ADD COLUMN algorithm_class VARCHAR(100) DEFAULT 'SingleEliminationAlgorithm'");
                    echo '<div class="success">✅ Added algorithm_class column to tournament_formats</div>';
                    $changes_made[] = "Added algorithm_class column to tournament_formats";
                }
            }
            
            // Create tournament_structures table if missing
            if (!in_array('tournament_structures', $existing_tables)) {
                echo '<p>Creating tournament_structures table...</p>';
                $sql = "CREATE TABLE tournament_structures (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    event_sport_id INT NOT NULL,
                    tournament_format_id INT NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                    participant_count INT DEFAULT 0,
                    total_rounds INT DEFAULT 0,
                    current_round INT DEFAULT 0,
                    seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
                    bracket_data JSON,
                    advancement_rules JSON,
                    scoring_config JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )";
                $conn->exec($sql);
                echo '<div class="success">✅ Created tournament_structures table</div>';
                $changes_made[] = "Created tournament_structures table";
            } else {
                echo '<div class="info">ℹ️ tournament_structures table already exists</div>';
            }
            
            // Create tournament_rounds table if missing
            if (!in_array('tournament_rounds', $existing_tables)) {
                echo '<p>Creating tournament_rounds table...</p>';
                $sql = "CREATE TABLE tournament_rounds (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    tournament_structure_id INT NOT NULL,
                    round_number INT NOT NULL,
                    round_name VARCHAR(100) NOT NULL,
                    round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
                    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                    start_date DATETIME,
                    end_date DATETIME,
                    matches_count INT DEFAULT 0,
                    completed_matches INT DEFAULT 0,
                    advancement_criteria JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $conn->exec($sql);
                echo '<div class="success">✅ Created tournament_rounds table</div>';
                $changes_made[] = "Created tournament_rounds table";
            } else {
                echo '<div class="info">ℹ️ tournament_rounds table already exists</div>';
            }
            
            // Create tournament_participants table if missing
            if (!in_array('tournament_participants', $existing_tables)) {
                echo '<p>Creating tournament_participants table...</p>';
                $sql = "CREATE TABLE tournament_participants (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    tournament_structure_id INT NOT NULL,
                    registration_id INT NOT NULL,
                    seed_number INT,
                    group_assignment VARCHAR(10),
                    current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
                    points DECIMAL(10,2) DEFAULT 0,
                    wins INT DEFAULT 0,
                    losses INT DEFAULT 0,
                    draws INT DEFAULT 0,
                    performance_data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $conn->exec($sql);
                echo '<div class="success">✅ Created tournament_participants table</div>';
                $changes_made[] = "Created tournament_participants table";
            } else {
                echo '<div class="info">ℹ️ tournament_participants table already exists</div>';
            }
            
            echo '</div>';
            
            // Step 4: Add foreign key constraints (without CASCADE to avoid issues)
            echo '<div class="step">';
            echo '<h2>🔗 Step 4: Add Foreign Key Constraints (Optional)</h2>';
            
            // Note: We'll add foreign keys without CASCADE to avoid constraint issues
            try {
                // Add foreign key for tournament_structure_id (without CASCADE)
                $sql = "ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_structure 
                        FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE SET NULL";
                $conn->exec($sql);
                echo '<div class="success">✅ Added foreign key constraint for tournament_structure_id</div>';
                $changes_made[] = "Added foreign key constraint for tournament_structure_id";
            } catch (Exception $e) {
                echo '<div class="warning">⚠️ Could not add foreign key for tournament_structure_id: ' . htmlspecialchars($e->getMessage()) . '</div>';
                echo '<div class="info">ℹ️ This is not critical - the system will work without foreign keys</div>';
            }
            
            try {
                // Add foreign key for tournament_round_id (without CASCADE)
                $sql = "ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_round 
                        FOREIGN KEY (tournament_round_id) REFERENCES tournament_rounds(id) ON DELETE SET NULL";
                $conn->exec($sql);
                echo '<div class="success">✅ Added foreign key constraint for tournament_round_id</div>';
                $changes_made[] = "Added foreign key constraint for tournament_round_id";
            } catch (Exception $e) {
                echo '<div class="warning">⚠️ Could not add foreign key for tournament_round_id: ' . htmlspecialchars($e->getMessage()) . '</div>';
                echo '<div class="info">ℹ️ This is not critical - the system will work without foreign keys</div>';
            }
            
            echo '</div>';
            
            // Step 5: Test the rebuilt schema
            echo '<div class="step">';
            echo '<h2>🧪 Step 5: Test Rebuilt Schema</h2>';
            
            // Test 1: Column access
            $test_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            $all_columns_accessible = true;
            
            foreach ($test_columns as $column) {
                try {
                    $stmt = $conn->prepare("SELECT $column FROM matches LIMIT 1");
                    $stmt->execute();
                    echo '<div class="success">✅ ' . $column . ' - Accessible</div>';
                } catch (Exception $e) {
                    echo '<div class="error">❌ ' . $column . ' - Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    $all_columns_accessible = false;
                    $errors[] = "Column $column not accessible: " . $e->getMessage();
                }
            }
            
            // Test 2: Insert with tournament columns
            if ($all_columns_accessible) {
                try {
                    $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute();
                    $test_id = $conn->lastInsertId();
                    
                    echo '<div class="success">✅ Insert with tournament columns successful</div>';
                    
                    // Clean up
                    $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
                    echo '<div class="info">ℹ️ Test record cleaned up</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="error">❌ Insert with tournament columns failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    $errors[] = "Insert test failed: " . $e->getMessage();
                }
            }
            
            echo '</div>';
            
            // Step 6: Ensure basic tournament format exists
            echo '<div class="step">';
            echo '<h2>📊 Step 6: Ensure Basic Tournament Format Exists</h2>';
            
            $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_formats");
            $stmt->execute();
            $format_count = $stmt->fetchColumn();
            
            if ($format_count == 0) {
                echo '<p>Creating basic tournament format...</p>';
                $stmt = $conn->prepare("
                    INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants, algorithm_class) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    'Single Elimination',
                    'single_elimination',
                    'Basic single elimination tournament',
                    'team,individual',
                    2,
                    32,
                    'SingleEliminationAlgorithm'
                ]);
                echo '<div class="success">✅ Created basic tournament format</div>';
                $changes_made[] = "Created basic tournament format";
            } else {
                echo '<div class="info">ℹ️ Tournament formats already exist (' . $format_count . ' found)</div>';
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Schema rebuild error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
        } finally {
            // Re-enable foreign key checks
            $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
        }
        ?>
        
        <!-- Summary -->
        <div class="step">
            <h2>📊 Rebuild Summary</h2>
            
            <?php if (empty($errors)): ?>
                <div class="success">
                    <h3>🎉 Schema Rebuild Successful!</h3>
                    <p>The tournament database schema has been forcefully rebuilt and should now work correctly.</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h3>❌ Some Issues Remain:</h3>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($changes_made)): ?>
                <div class="info">
                    <h3>✅ Changes Made:</h3>
                    <ul>
                        <?php foreach ($changes_made as $change): ?>
                            <li><?php echo htmlspecialchars($change); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Next Steps -->
        <div class="step">
            <h2>🚀 Next Steps</h2>
            <p>
                <a href="diagnose-database-state.php" class="btn">🔍 Re-diagnose Database</a>
                <a href="test-tournament-creation-final.php" class="btn btn-success">🧪 Test Tournament Creation</a>
                <a href="manage-event.php?id=1" class="btn btn-warning">📋 Manage Events</a>
                <a href="index.php" class="btn">🏠 Admin Dashboard</a>
            </p>
        </div>
    </div>
</body>
</html>
