<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h3>Tournament Formats:</h3>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$formats = $stmt->fetchAll();

foreach ($formats as $format) {
    echo "<div style='margin: 1rem 0; padding: 1rem; border: 1px solid #ddd;'>";
    echo "<strong>ID: {$format['id']}</strong> - {$format['name']}<br>";
    echo "Algorithm: {$format['algorithm_class']}<br>";
    echo "Min Participants: {$format['min_participants']}<br>";
    echo "Sport Type: {$format['sport_type_category']}<br>";
    echo "</div>";
}

echo "<h3>Available Algorithm Classes:</h3>";
require_once '../includes/tournament_algorithms.php';

$classes = ['SingleEliminationAlgorithm', 'DoubleEliminationAlgorithm', 'RoundRobinAlgorithm', 'SwissSystemAlgorithm'];
foreach ($classes as $class) {
    $exists = class_exists($class) ? 'EXISTS' : 'MISSING';
    echo "<div>$class: $exists</div>";
}
?>
