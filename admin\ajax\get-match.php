<?php
/**
 * Get Match Data for Modal Editing
 * SC_IMS Admin Panel AJAX Endpoint
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get match ID
$match_id = $_GET['id'] ?? 0;

if (!$match_id) {
    echo json_encode([
        'success' => false,
        'message' => 'Match ID is required'
    ]);
    exit;
}

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Fetch match data with related information
    $stmt = $conn->prepare("
        SELECT m.*, 
               es.event_id, e.name as event_name,
               s.name as sport_name, s.type as sport_type,
               t1.team_name as team1_name, t1.department_id as team1_dept_id,
               t2.team_name as team2_name, t2.department_id as team2_dept_id,
               d1.name as team1_dept_name, d1.abbreviation as team1_abbr,
               d2.name as team2_dept_name, d2.abbreviation as team2_abbr
        FROM matches m
        JOIN event_sports es ON m.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        JOIN registrations t1 ON m.team1_id = t1.id
        JOIN departments d1 ON t1.department_id = d1.id
        LEFT JOIN registrations t2 ON m.team2_id = t2.id
        LEFT JOIN departments d2 ON t2.department_id = d2.id
        WHERE m.id = ?
    ");
    $stmt->execute([$match_id]);
    $match = $stmt->fetch();
    
    if (!$match) {
        echo json_encode([
            'success' => false,
            'message' => 'Match not found'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'match' => $match
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error fetching match data: ' . $e->getMessage()
    ]);
}
?>
