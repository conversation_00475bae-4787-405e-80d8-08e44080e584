<?php
/**
 * Comprehensive Database Schema Analysis
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Comprehensive Database Schema Analysis</h1>";
echo "<p>Analyzing current database schema and identifying all missing tournament components...</p>";

try {
    echo "<h2>📊 Step 1: Analyze Current Tables</h2>";
    
    // Get all existing tables
    $stmt = $conn->prepare("SHOW TABLES");
    $stmt->execute();
    $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>Existing Tables:</h3>";
    echo "<ul>";
    foreach ($existing_tables as $table) {
        echo "<li>{$table}</li>";
    }
    echo "</ul>";
    
    // Define required tournament tables
    $required_tournament_tables = [
        'tournament_formats',
        'tournament_structures', 
        'tournament_rounds',
        'tournament_participants'
    ];
    
    $missing_tables = [];
    foreach ($required_tournament_tables as $table) {
        if (!in_array($table, $existing_tables)) {
            $missing_tables[] = $table;
        }
    }
    
    echo "<h3>Tournament Tables Status:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'><th>Table</th><th>Status</th></tr>";
    
    foreach ($required_tournament_tables as $table) {
        $status = in_array($table, $existing_tables) ? 
            "<span style='color: #28a745;'>✅ EXISTS</span>" : 
            "<span style='color: #dc3545;'>❌ MISSING</span>";
        echo "<tr><td>{$table}</td><td>{$status}</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>📊 Step 2: Analyze Matches Table Structure</h2>";
    
    if (in_array('matches', $existing_tables)) {
        $stmt = $conn->prepare("DESCRIBE matches");
        $stmt->execute();
        $matches_columns = $stmt->fetchAll();
        
        echo "<h3>Current Matches Table Columns:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        $existing_matches_columns = [];
        foreach ($matches_columns as $column) {
            $existing_matches_columns[] = $column['Field'];
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Define required tournament columns for matches table
        $required_matches_columns = [
            'tournament_structure_id' => 'INT NULL',
            'tournament_round_id' => 'INT NULL',
            'bracket_position' => 'VARCHAR(50) NULL',
            'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
        ];
        
        $missing_matches_columns = [];
        foreach ($required_matches_columns as $column => $definition) {
            if (!in_array($column, $existing_matches_columns)) {
                $missing_matches_columns[$column] = $definition;
            }
        }
        
        echo "<h3>Required Tournament Columns Status:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'><th>Column</th><th>Definition</th><th>Status</th></tr>";
        
        foreach ($required_matches_columns as $column => $definition) {
            $status = in_array($column, $existing_matches_columns) ? 
                "<span style='color: #28a745;'>✅ EXISTS</span>" : 
                "<span style='color: #dc3545;'>❌ MISSING</span>";
            echo "<tr><td>{$column}</td><td>{$definition}</td><td>{$status}</td></tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p style='color: #dc3545;'>❌ <strong>CRITICAL:</strong> matches table does not exist!</p>";
        $missing_tables[] = 'matches';
    }
    
    echo "<h2>📊 Step 3: Analyze Tournament Formats Table</h2>";
    
    if (in_array('tournament_formats', $existing_tables)) {
        $stmt = $conn->prepare("DESCRIBE tournament_formats");
        $stmt->execute();
        $format_columns = $stmt->fetchAll();
        
        echo "<h3>Tournament Formats Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        
        foreach ($format_columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check format data
        $stmt = $conn->prepare("SELECT id, name, code FROM tournament_formats ORDER BY id");
        $stmt->execute();
        $formats = $stmt->fetchAll();
        
        echo "<h3>Available Tournament Formats:</h3>";
        if (!empty($formats)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f5f5f5;'><th>ID</th><th>Name</th><th>Code</th></tr>";
            foreach ($formats as $format) {
                echo "<tr><td>{$format['id']}</td><td>{$format['name']}</td><td>{$format['code']}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: #ffc107;'>⚠ No tournament formats found in database</p>";
        }
        
    } else {
        echo "<p style='color: #dc3545;'>❌ tournament_formats table does not exist!</p>";
    }
    
    echo "<h2>📊 Step 4: Summary of Issues</h2>";
    
    $total_issues = count($missing_tables) + (isset($missing_matches_columns) ? count($missing_matches_columns) : 0);
    
    if ($total_issues > 0) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>SCHEMA ISSUES FOUND: {$total_issues}</strong></h3>";
        
        if (!empty($missing_tables)) {
            echo "<h4>Missing Tables:</h4>";
            echo "<ul>";
            foreach ($missing_tables as $table) {
                echo "<li style='color: #721c24; font-weight: bold;'>{$table}</li>";
            }
            echo "</ul>";
        }
        
        if (isset($missing_matches_columns) && !empty($missing_matches_columns)) {
            echo "<h4>Missing Matches Table Columns:</h4>";
            echo "<ul>";
            foreach ($missing_matches_columns as $column => $definition) {
                echo "<li style='color: #721c24; font-weight: bold;'>{$column} ({$definition})</li>";
            }
            echo "</ul>";
        }
        
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='comprehensive-schema-fix.php' style='background: #dc3545; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🔧 FIX ALL SCHEMA ISSUES</a>";
        echo "</div>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SCHEMA IS COMPLETE!</strong></h3>";
        echo "<p style='font-size: 18px;'>All required tables and columns exist.</p>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE TOURNAMENT</a>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "<h2>📋 Detailed Schema Requirements</h2>";
    
    echo "<h3>Required Tournament Tables with Columns:</h3>";
    
    $schema_requirements = [
        'tournament_formats' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'name' => 'VARCHAR(100) NOT NULL',
            'code' => 'VARCHAR(50) NOT NULL UNIQUE',
            'description' => 'TEXT',
            'sport_types' => 'VARCHAR(255) DEFAULT "team,individual"',
            'min_participants' => 'INT DEFAULT 2',
            'max_participants' => 'INT DEFAULT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ],
        'tournament_structures' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'event_sport_id' => 'INT NOT NULL',
            'tournament_format_id' => 'INT NOT NULL',
            'name' => 'VARCHAR(255) NOT NULL',
            'status' => 'ENUM("setup", "registration", "seeding", "in_progress", "completed", "cancelled") DEFAULT "setup"',
            'participant_count' => 'INT DEFAULT 0',
            'total_rounds' => 'INT DEFAULT 0',
            'current_round' => 'INT DEFAULT 0',
            'seeding_method' => 'ENUM("random", "ranking", "manual", "hybrid") DEFAULT "random"',
            'bracket_data' => 'JSON',
            'advancement_rules' => 'JSON',
            'scoring_config' => 'JSON',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'tournament_rounds' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'tournament_structure_id' => 'INT NOT NULL',
            'round_number' => 'INT NOT NULL',
            'round_name' => 'VARCHAR(100) NOT NULL',
            'round_type' => 'ENUM("group", "elimination", "final", "consolation") DEFAULT "elimination"',
            'status' => 'ENUM("pending", "in_progress", "completed") DEFAULT "pending"',
            'start_date' => 'DATETIME',
            'end_date' => 'DATETIME',
            'matches_count' => 'INT DEFAULT 0',
            'completed_matches' => 'INT DEFAULT 0',
            'advancement_criteria' => 'JSON',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ],
        'tournament_participants' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'tournament_structure_id' => 'INT NOT NULL',
            'registration_id' => 'INT NOT NULL',
            'seed_number' => 'INT',
            'group_assignment' => 'VARCHAR(10)',
            'current_status' => 'ENUM("active", "eliminated", "bye", "withdrawn") DEFAULT "active"',
            'points' => 'DECIMAL(10,2) DEFAULT 0',
            'wins' => 'INT DEFAULT 0',
            'losses' => 'INT DEFAULT 0',
            'draws' => 'INT DEFAULT 0',
            'performance_data' => 'JSON',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
        ]
    ];
    
    foreach ($schema_requirements as $table_name => $columns) {
        $table_exists = in_array($table_name, $existing_tables);
        $status_color = $table_exists ? '#28a745' : '#dc3545';
        $status_text = $table_exists ? '✅ EXISTS' : '❌ MISSING';
        
        echo "<h4 style='color: {$status_color};'>{$table_name} - {$status_text}</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'><th>Column</th><th>Definition</th></tr>";
        
        foreach ($columns as $column => $definition) {
            echo "<tr><td>{$column}</td><td>{$definition}</td></tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red; font-size: 18px;'>❌ <strong>ERROR:</strong> " . $e->getMessage() . "</p>";
}

echo "<h3>🔧 Next Steps</h3>";
echo "<ol>";
echo "<li><strong>Review the analysis above</strong> to understand what's missing</li>";
echo "<li><strong>Run the comprehensive schema fix</strong> to add all missing components</li>";
echo "<li><strong>Test tournament creation</strong> to verify everything works</li>";
echo "</ol>";

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='comprehensive-schema-fix.php'>🔧 Run Comprehensive Schema Fix</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation Page</a></li>";
echo "</ul>";
?>
