-- Tournament Formats Update for Enhanced Sport Types
-- SC_IMS Sports Competition and Event Management System
-- Update tournament formats to support new sport type categories

-- =====================================================
-- 1. UPDATE TOURNAMENT FORMATS TABLE ENUM
-- =====================================================

-- Update the sport_type_category enum to include new categories
ALTER TABLE tournament_formats 
MODIFY COLUMN sport_type_category ENUM('individual', 'team', 'academic', 'judged', 'traditional', 'performance', 'all') NOT NULL;

-- =====================================================
-- 2. ADD NEW TOURNAMENT FORMATS FOR ENHANCED CATEGORIES
-- =====================================================

-- Insert formats for Traditional sports (combines team and individual)
INSERT INTO tournament_formats (name, code, description, sport_type_category, min_participants, max_participants, requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula, algorithm_class, configuration) VALUES

-- Traditional Sports Formats (for both team and individual traditional sports)
('Traditional Elimination', 'traditional_elimination', 'Standard elimination tournament for traditional sports', 'traditional', 2, NULL, TRUE, TRUE, 'elimination', 'ceil(log2(n))', 'n-1', 'SingleEliminationAlgorithm', '{"bracket_seeding": true, "third_place_match": true}'),

('Traditional Round Robin', 'traditional_round_robin', 'Round robin format for traditional sports', 'traditional', 3, 16, FALSE, FALSE, 'points', '1', 'n*(n-1)/2', 'RoundRobinAlgorithm', '{"points_win": 3, "points_draw": 1, "points_loss": 0}'),

-- Performance Arts Formats
('Performance Showcase', 'performance_showcase', 'Multi-round performance showcase with audience and judge scoring', 'performance', 3, 50, FALSE, FALSE, 'points', '3', 'n*3', 'PerformanceShowcaseAlgorithm', '{"rounds": ["audition", "semifinal", "final"], "audience_voting": true, "judge_scoring": true}'),

('Performance Competition', 'performance_competition', 'Competitive performance format with elimination rounds', 'performance', 4, NULL, FALSE, TRUE, 'elimination', 'ceil(log2(n))', 'n-1', 'PerformanceCompetitionAlgorithm', '{"performance_time_limits": true, "technical_scoring": true}'),

('Artistic Judging', 'artistic_judging', 'Artistic performance judged on multiple criteria', 'performance', 2, NULL, FALSE, FALSE, 'points', '1', 'n', 'ArtisticJudgingAlgorithm', '{"criteria": ["technique", "creativity", "presentation"], "judge_panels": true}');

-- =====================================================
-- 3. UPDATE EXISTING FORMATS FOR COMPATIBILITY
-- =====================================================

-- Update existing formats to work with traditional category as well
UPDATE tournament_formats 
SET sport_type_category = 'traditional' 
WHERE code IN ('single_elimination', 'double_elimination', 'round_robin', 'multi_stage') 
AND sport_type_category = 'team';

-- Add traditional category support to individual formats
INSERT INTO tournament_formats (name, code, description, sport_type_category, min_participants, max_participants, requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula, algorithm_class, configuration)
SELECT 
    CONCAT(name, ' (Traditional)'),
    CONCAT(code, '_traditional'),
    CONCAT(description, ' - Adapted for traditional individual sports'),
    'traditional',
    min_participants,
    max_participants,
    requires_seeding,
    supports_byes,
    advancement_type,
    rounds_formula,
    matches_formula,
    algorithm_class,
    configuration
FROM tournament_formats 
WHERE sport_type_category = 'individual' 
AND code IN ('elimination_rounds', 'best_performance');

-- =====================================================
-- 4. VERIFY TOURNAMENT FORMAT COMPATIBILITY
-- =====================================================

-- Create a view to show sport type and format compatibility
CREATE OR REPLACE VIEW sport_tournament_compatibility AS
SELECT 
    st.name as sport_type_name,
    st.category as sport_type_category,
    tf.name as format_name,
    tf.code as format_code,
    tf.description as format_description,
    tf.min_participants,
    tf.max_participants,
    tf.requires_seeding,
    tf.advancement_type
FROM sport_types st
CROSS JOIN tournament_formats tf
WHERE tf.sport_type_category = st.category 
   OR tf.sport_type_category = 'all'
ORDER BY st.category, tf.name;

-- =====================================================
-- 5. UPDATE SPORT TYPE TOURNAMENT FORMATS JSON
-- =====================================================

-- Update sport_types table with compatible tournament format codes
UPDATE sport_types SET tournament_formats = '["traditional_elimination", "traditional_round_robin", "single_elimination", "double_elimination", "round_robin", "multi_stage"]' 
WHERE category = 'traditional';

UPDATE sport_types SET tournament_formats = '["swiss_system", "knockout_rounds", "round_robin"]' 
WHERE category = 'academic';

UPDATE sport_types SET tournament_formats = '["judged_rounds", "talent_showcase", "elimination_rounds"]' 
WHERE category = 'judged';

UPDATE sport_types SET tournament_formats = '["performance_showcase", "performance_competition", "artistic_judging", "talent_showcase"]' 
WHERE category = 'performance';

-- =====================================================
-- 6. VERIFICATION QUERIES
-- =====================================================

-- Show all sport types and their compatible formats
-- SELECT * FROM sport_tournament_compatibility;

-- Show format distribution by category
-- SELECT sport_type_category, COUNT(*) as format_count 
-- FROM tournament_formats 
-- GROUP BY sport_type_category 
-- ORDER BY sport_type_category;

-- Show sports and their assigned types
-- SELECT s.name as sport_name, st.name as sport_type, st.category 
-- FROM sports s 
-- LEFT JOIN sport_types st ON s.sport_type_id = st.id 
-- ORDER BY st.category, s.name;
