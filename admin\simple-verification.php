<?php
/**
 * Simple Verification - Quick Test of Tournament Schema Fix
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Verification - Tournament Schema Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .test { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Simple Verification - Tournament Schema Fix</h1>
        <p><strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        $all_tests_passed = true;
        $test_results = [];
        
        // Test 1: Check if tournament columns exist
        echo '<div class="test">';
        echo '<h2>Test 1: Tournament Columns Existence</h2>';
        
        try {
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            $missing_columns = [];
            
            foreach ($required_columns as $column) {
                if (in_array($column, $columns)) {
                    echo '<p>✅ Column "' . $column . '" exists</p>';
                } else {
                    echo '<p>❌ Column "' . $column . '" missing</p>';
                    $missing_columns[] = $column;
                    $all_tests_passed = false;
                }
            }
            
            if (empty($missing_columns)) {
                echo '<div class="success">✅ All tournament columns exist in matches table</div>';
                $test_results['columns'] = true;
            } else {
                echo '<div class="error">❌ Missing columns: ' . implode(', ', $missing_columns) . '</div>';
                $test_results['columns'] = false;
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Error checking columns: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $test_results['columns'] = false;
            $all_tests_passed = false;
        }
        
        echo '</div>';
        
        // Test 2: Test INSERT operation
        echo '<div class="test">';
        echo '<h2>Test 2: INSERT Operation with Tournament Columns</h2>';
        
        if ($test_results['columns']) {
            try {
                $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $test_id = $conn->lastInsertId();
                
                echo '<div class="success">✅ INSERT with tournament columns successful (Test ID: ' . $test_id . ')</div>';
                
                // Clean up test record
                $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
                echo '<p>✓ Test record cleaned up</p>';
                
                $test_results['insert'] = true;
                
            } catch (Exception $e) {
                echo '<div class="error">❌ INSERT failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                $test_results['insert'] = false;
                $all_tests_passed = false;
            }
        } else {
            echo '<div class="info">⏭️ Skipping INSERT test due to missing columns</div>';
            $test_results['insert'] = false;
            $all_tests_passed = false;
        }
        
        echo '</div>';
        
        // Test 3: Test Tournament Manager (if available)
        echo '<div class="test">';
        echo '<h2>Test 3: Tournament Manager Class</h2>';
        
        try {
            if (file_exists('../includes/tournament_manager.php')) {
                require_once '../includes/tournament_manager.php';
                $tournamentManager = new TournamentManager($conn);
                echo '<div class="success">✅ Tournament manager class loads successfully</div>';
                $test_results['manager'] = true;
            } else {
                echo '<div class="error">❌ Tournament manager file not found</div>';
                $test_results['manager'] = false;
                $all_tests_passed = false;
            }
        } catch (Exception $e) {
            echo '<div class="error">❌ Tournament manager error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $test_results['manager'] = false;
            $all_tests_passed = false;
        }
        
        echo '</div>';
        
        // Overall Result
        echo '<div class="test">';
        echo '<h2>📊 Overall Test Results</h2>';
        
        if ($all_tests_passed) {
            echo '<div class="success">';
            echo '<h3>🎉 ALL TESTS PASSED!</h3>';
            echo '<p><strong>The tournament_structure_id column issue has been resolved!</strong></p>';
            echo '<p>You can now create tournaments without database errors.</p>';
            echo '</div>';
        } else {
            echo '<div class="error">';
            echo '<h3>❌ Some Tests Failed</h3>';
            echo '<p>The tournament schema fix was not completely successful.</p>';
            echo '</div>';
        }
        
        // Test Summary Table
        echo '<h3>Test Summary:</h3>';
        echo '<table>';
        echo '<tr><th>Test</th><th>Result</th><th>Status</th></tr>';
        
        $tests = [
            'columns' => 'Tournament Columns Exist',
            'insert' => 'INSERT Operation Works',
            'manager' => 'Tournament Manager Loads'
        ];
        
        foreach ($tests as $key => $name) {
            $result = $test_results[$key] ?? false;
            $status = $result ? '✅ PASS' : '❌ FAIL';
            $row_style = $result ? 'background: #d4edda;' : 'background: #f8d7da;';
            
            echo '<tr style="' . $row_style . '">';
            echo '<td>' . $name . '</td>';
            echo '<td>' . $status . '</td>';
            echo '<td>' . ($result ? 'Working' : 'Failed') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        
        echo '</div>';
        ?>
        
        <!-- Action Buttons -->
        <div class="test">
            <h2>🛠️ Available Actions</h2>
            <p>
                <?php if ($all_tests_passed): ?>
                    <a href="test-tournament-creation-final.php" class="btn btn-success">🧪 Test Tournament Creation</a>
                    <a href="manage-event.php?id=1" class="btn">📋 Manage Events</a>
                    <a href="index.php" class="btn">🏠 Admin Dashboard</a>
                <?php else: ?>
                    <a href="direct-schema-fix.php" class="btn btn-warning">🔧 Run Schema Fix Again</a>
                    <a href="diagnose-database-state.php" class="btn">🔍 Detailed Diagnosis</a>
                    <a href="force-schema-rebuild.php" class="btn btn-warning">🔄 Force Rebuild</a>
                <?php endif; ?>
            </p>
        </div>
        
        <!-- Simple Confirmation Method -->
        <div class="test">
            <h2>✅ Simple Confirmation Method</h2>
            <div class="info">
                <h3>Quick Way to Verify the Fix is Working:</h3>
                <ol>
                    <li><strong>Go to Admin Dashboard:</strong> <a href="index.php">Click here</a></li>
                    <li><strong>Navigate to "Manage Events"</strong></li>
                    <li><strong>Select any event and click "Manage"</strong></li>
                    <li><strong>Find a sport and click "Create Tournament"</strong></li>
                    <li><strong>If no "tournament_structure_id" error appears, the fix is working!</strong></li>
                </ol>
                
                <?php if ($all_tests_passed): ?>
                    <p><strong>✅ Based on these tests, tournament creation should work without errors.</strong></p>
                <?php else: ?>
                    <p><strong>❌ The fix needs to be applied again. Please run the schema fix.</strong></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
