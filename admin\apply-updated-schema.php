<?php
/**
 * Apply Updated Database Schema
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script applies the updated database schema that includes tournament columns
 * in the matches table by default, preventing the recurring schema issue.
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Apply Updated Database Schema</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1000px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Apply Updated Database Schema</h1>
        <p><strong>Operation Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="info">
            <h3>What This Script Does:</h3>
            <p>This script applies the updated database schema that includes tournament columns in the matches table by default. This prevents the recurring "tournament_structure_id" column error by ensuring the schema is permanently correct.</p>
        </div>
        
        <?php
        $success = true;
        $changes_made = [];
        $errors = [];
        
        try {
            // Step 1: Check current state
            echo '<div class="step">';
            echo '<h2>Step 1: Checking Current Database State</h2>';
            
            // Check if matches table has tournament columns
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $current_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $tournament_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            $missing_columns = [];
            
            foreach ($tournament_columns as $column) {
                if (!in_array($column, $current_columns)) {
                    $missing_columns[] = $column;
                }
            }
            
            if (empty($missing_columns)) {
                echo '<div class="success">✅ All tournament columns already exist in matches table</div>';
            } else {
                echo '<div class="warning">⚠️ Missing tournament columns: ' . implode(', ', $missing_columns) . '</div>';
            }
            
            // Check tournament tables
            $stmt = $conn->prepare("SHOW TABLES");
            $stmt->execute();
            $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $required_tournament_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
            $missing_tables = [];
            
            foreach ($required_tournament_tables as $table) {
                if (!in_array($table, $existing_tables)) {
                    $missing_tables[] = $table;
                }
            }
            
            if (empty($missing_tables)) {
                echo '<div class="success">✅ All tournament tables exist</div>';
            } else {
                echo '<div class="warning">⚠️ Missing tournament tables: ' . implode(', ', $missing_tables) . '</div>';
            }
            
            echo '</div>';
            
            // Step 2: Apply the updated schema using the Database class
            echo '<div class="step">';
            echo '<h2>Step 2: Applying Updated Schema</h2>';
            
            // Disable foreign key checks temporarily
            $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
            echo '<p>✓ Disabled foreign key checks</p>';
            
            // If tournament columns are missing, add them
            if (!empty($missing_columns)) {
                echo '<h3>Adding Missing Tournament Columns</h3>';
                
                $column_definitions = [
                    'tournament_structure_id' => 'INT NULL COMMENT "Tournament structure reference"',
                    'tournament_round_id' => 'INT NULL COMMENT "Tournament round reference"',
                    'bracket_position' => 'VARCHAR(50) NULL COMMENT "Position in tournament bracket"',
                    'is_bye_match' => 'BOOLEAN DEFAULT FALSE COMMENT "Whether this is a bye match"'
                ];
                
                foreach ($missing_columns as $column) {
                    try {
                        $sql = "ALTER TABLE matches ADD COLUMN $column " . $column_definitions[$column];
                        $conn->exec($sql);
                        echo '<p>✅ Added column: ' . $column . '</p>';
                        $changes_made[] = "Added column: $column";
                    } catch (Exception $e) {
                        echo '<p>❌ Failed to add column ' . $column . ': ' . htmlspecialchars($e->getMessage()) . '</p>';
                        $errors[] = "Failed to add column $column: " . $e->getMessage();
                        $success = false;
                    }
                }
            }
            
            // Create missing tournament tables using the updated Database class
            if (!empty($missing_tables)) {
                echo '<h3>Creating Missing Tournament Tables</h3>';
                
                // Use the updated initializeTables method which now includes tournament tables
                try {
                    // Create a new database instance to use the updated schema
                    $database_updater = new Database();
                    
                    // Create tournament_formats if missing
                    if (in_array('tournament_formats', $missing_tables)) {
                        $sql = "CREATE TABLE IF NOT EXISTS tournament_formats (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            name VARCHAR(255) NOT NULL,
                            code VARCHAR(50) NOT NULL UNIQUE,
                            description TEXT,
                            sport_types VARCHAR(255) DEFAULT 'team,individual',
                            min_participants INT DEFAULT 2,
                            max_participants INT NULL,
                            algorithm_class VARCHAR(100) DEFAULT 'SingleEliminationAlgorithm',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX idx_sport_types (sport_types),
                            INDEX idx_code (code)
                        )";
                        $conn->exec($sql);
                        echo '<p>✅ Created tournament_formats table</p>';
                        $changes_made[] = "Created tournament_formats table";
                        
                        // Insert default formats
                        $default_formats = [
                            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null, 'SingleEliminationAlgorithm'],
                            ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null, 'DoubleEliminationAlgorithm'],
                            ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16, 'RoundRobinAlgorithm'],
                            ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null, 'SwissSystemAlgorithm'],
                            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null, 'JudgedRoundsAlgorithm'],
                            ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null, 'MultiStageAlgorithm']
                        ];
                        
                        foreach ($default_formats as $format) {
                            $stmt = $conn->prepare("
                                INSERT IGNORE INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants, algorithm_class)
                                VALUES (?, ?, ?, ?, ?, ?, ?)
                            ");
                            $stmt->execute($format);
                        }
                        echo '<p>✅ Inserted default tournament formats</p>';
                    }
                    
                    // Create tournament_structures if missing
                    if (in_array('tournament_structures', $missing_tables)) {
                        $sql = "CREATE TABLE IF NOT EXISTS tournament_structures (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            event_sport_id INT NOT NULL,
                            tournament_format_id INT NOT NULL,
                            name VARCHAR(255) NOT NULL,
                            status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                            participant_count INT DEFAULT 0,
                            total_rounds INT DEFAULT 0,
                            current_round INT DEFAULT 0,
                            seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
                            bracket_data JSON,
                            advancement_rules JSON,
                            scoring_config JSON,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                            FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE CASCADE
                        )";
                        $conn->exec($sql);
                        echo '<p>✅ Created tournament_structures table</p>';
                        $changes_made[] = "Created tournament_structures table";
                    }
                    
                    // Create tournament_rounds if missing
                    if (in_array('tournament_rounds', $missing_tables)) {
                        $sql = "CREATE TABLE IF NOT EXISTS tournament_rounds (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            tournament_structure_id INT NOT NULL,
                            round_number INT NOT NULL,
                            round_name VARCHAR(100) NOT NULL,
                            round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
                            status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                            start_date DATETIME,
                            end_date DATETIME,
                            matches_count INT DEFAULT 0,
                            completed_matches INT DEFAULT 0,
                            advancement_criteria JSON,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE
                        )";
                        $conn->exec($sql);
                        echo '<p>✅ Created tournament_rounds table</p>';
                        $changes_made[] = "Created tournament_rounds table";
                    }
                    
                    // Create tournament_participants if missing
                    if (in_array('tournament_participants', $missing_tables)) {
                        $sql = "CREATE TABLE IF NOT EXISTS tournament_participants (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            tournament_structure_id INT NOT NULL,
                            registration_id INT NOT NULL,
                            seed_number INT,
                            group_assignment VARCHAR(10),
                            current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
                            points DECIMAL(10,2) DEFAULT 0,
                            wins INT DEFAULT 0,
                            losses INT DEFAULT 0,
                            draws INT DEFAULT 0,
                            performance_data JSON,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
                            FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE
                        )";
                        $conn->exec($sql);
                        echo '<p>✅ Created tournament_participants table</p>';
                        $changes_made[] = "Created tournament_participants table";
                    }
                    
                } catch (Exception $e) {
                    echo '<p>❌ Error creating tournament tables: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    $errors[] = "Error creating tournament tables: " . $e->getMessage();
                    $success = false;
                }
            }
            
            // Re-enable foreign key checks
            $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
            echo '<p>✓ Re-enabled foreign key checks</p>';
            
            echo '</div>';
            
            // Step 3: Test the updated schema
            echo '<div class="step">';
            echo '<h2>Step 3: Testing Updated Schema</h2>';
            
            // Test column access
            foreach ($tournament_columns as $column) {
                try {
                    $stmt = $conn->prepare("SELECT $column FROM matches LIMIT 1");
                    $stmt->execute();
                    echo '<p>✅ Column ' . $column . ' is accessible</p>';
                } catch (Exception $e) {
                    echo '<p>❌ Column ' . $column . ' access failed: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    $errors[] = "Column $column access failed: " . $e->getMessage();
                    $success = false;
                }
            }
            
            // Test INSERT with tournament columns
            try {
                $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $test_id = $conn->lastInsertId();
                
                echo '<p>✅ INSERT with tournament columns successful (Test ID: ' . $test_id . ')</p>';
                
                // Clean up test record
                $conn->prepare("DELETE FROM matches WHERE id = ?")->execute([$test_id]);
                echo '<p>✓ Test record cleaned up</p>';
                
            } catch (Exception $e) {
                echo '<p>❌ INSERT test failed: ' . htmlspecialchars($e->getMessage()) . '</p>';
                $errors[] = "INSERT test failed: " . $e->getMessage();
                $success = false;
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Schema update error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
            $success = false;
        } finally {
            // Ensure foreign key checks are re-enabled
            try {
                $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
            } catch (Exception $e) {
                // Ignore errors here
            }
        }
        ?>
        
        <!-- Results Summary -->
        <div class="step">
            <h2>📊 Schema Update Results</h2>
            
            <?php if ($success): ?>
                <div class="success">
                    <h3>🎉 SCHEMA UPDATE SUCCESSFUL!</h3>
                    <p><strong>The database schema has been permanently updated to include tournament support.</strong></p>
                    <p>The "tournament_structure_id" column error should no longer occur.</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h3>❌ Schema Update Issues</h3>
                    <p>Some issues occurred during the schema update:</p>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($changes_made)): ?>
                <div class="info">
                    <h3>✅ Changes Applied:</h3>
                    <ul>
                        <?php foreach ($changes_made as $change): ?>
                            <li><?php echo htmlspecialchars($change); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Next Steps -->
        <div class="step">
            <h2>🚀 Next Steps</h2>
            <p>
                <?php if ($success): ?>
                    <a href="simple-verification.php" class="btn btn-success">✅ Verify Fix</a>
                    <a href="test-tournament-creation-final.php" class="btn btn-success">🧪 Test Tournament Creation</a>
                    <a href="manage-event.php?id=1" class="btn">📋 Manage Events</a>
                    <a href="index.php" class="btn">🏠 Admin Dashboard</a>
                <?php else: ?>
                    <a href="direct-schema-fix.php" class="btn btn-warning">🔧 Try Direct Fix</a>
                    <a href="diagnose-database-state.php" class="btn">🔍 Diagnose Issues</a>
                <?php endif; ?>
            </p>
        </div>
        
        <div class="info">
            <h3>🔒 Permanent Fix Applied</h3>
            <p>This update modifies the database initialization script to include tournament columns by default. This means:</p>
            <ul>
                <li>✅ Future database recreations will include tournament support</li>
                <li>✅ The schema fix is now permanent and won't need to be reapplied</li>
                <li>✅ Tournament creation should work without column errors</li>
            </ul>
        </div>
    </div>
</body>
</html>
