/**
 * Admin Panel JavaScript for SC_IMS
 * Sports Competition and Event Management System
 */

// Global admin object
const Admin = {
    // Initialize admin functionality
    init: function() {
        this.initSidebar();
        this.initModals();
        this.initForms();
        this.initTables();
        this.initConfirmations();
    },

    // Sidebar functionality
    initSidebar: function() {
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.admin-sidebar');
        const mainContent = document.querySelector('.admin-main');

        if (sidebarToggle && sidebar && mainContent) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });
        }

        // Mobile sidebar
        const mobileToggle = document.querySelector('.mobile-sidebar-toggle');
        if (mobileToggle && sidebar) {
            mobileToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }

        // Close sidebar on mobile when clicking outside
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    },

    // Modal functionality
    initModals: function() {
        // Open modal
        document.addEventListener('click', function(e) {
            if (e.target.hasAttribute('data-modal')) {
                e.preventDefault();
                const modalId = e.target.getAttribute('data-modal');
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            }
        });

        // Close modal
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal') || e.target.classList.contains('modal-close')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    modal.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    openModal.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }
        });
    },

    // Form functionality
    initForms: function() {
        // Auto-submit forms with data-auto-submit
        document.addEventListener('change', function(e) {
            const form = e.target.closest('form[data-auto-submit]');
            if (form) {
                form.submit();
            }
        });

        // Form validation
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.hasAttribute('data-validate')) {
                if (!Admin.validateForm(form)) {
                    e.preventDefault();
                }
            }
        });

        // Real-time validation
        document.addEventListener('input', function(e) {
            if (e.target.hasAttribute('data-validate-field')) {
                Admin.validateField(e.target);
            }
        });
    },

    // Table functionality
    initTables: function() {
        // Sortable tables
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('sortable')) {
                Admin.sortTable(e.target);
            }
        });

        // Select all checkboxes
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('select-all')) {
                const checkboxes = document.querySelectorAll('input[type="checkbox"][name="' + e.target.getAttribute('data-target') + '"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
            }
        });

        // Bulk actions
        document.addEventListener('click', function(e) {
            if (e.target.hasAttribute('data-bulk-action')) {
                e.preventDefault();
                const action = e.target.getAttribute('data-bulk-action');
                const form = e.target.closest('form');
                const selected = form.querySelectorAll('input[type="checkbox"]:checked');
                
                if (selected.length === 0) {
                    Admin.showAlert('Please select at least one item.', 'warning');
                    return;
                }

                if (confirm('Are you sure you want to perform this action on ' + selected.length + ' item(s)?')) {
                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'bulk_action';
                    actionInput.value = action;
                    form.appendChild(actionInput);
                    form.submit();
                }
            }
        });
    },

    // Confirmation dialogs
    initConfirmations: function() {
        document.addEventListener('click', function(e) {
            if (e.target.hasAttribute('data-confirm')) {
                const message = e.target.getAttribute('data-confirm');
                if (!confirm(message)) {
                    e.preventDefault();
                }
            }
        });
    },

    // Form validation
    validateForm: function(form) {
        let isValid = true;
        const fields = form.querySelectorAll('[required], [data-validate-field]');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    },

    validateField: function(field) {
        const value = field.value.trim();
        const type = field.getAttribute('data-validate-field') || field.type;
        let isValid = true;
        let message = '';

        // Remove existing error
        this.clearFieldError(field);

        // Required validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'This field is required.';
        }

        // Type-specific validation
        if (value && isValid) {
            switch (type) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        message = 'Please enter a valid email address.';
                    }
                    break;

                case 'password':
                    if (value.length < 8) {
                        isValid = false;
                        message = 'Password must be at least 8 characters long.';
                    }
                    break;

                case 'username':
                    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
                    if (!usernameRegex.test(value)) {
                        isValid = false;
                        message = 'Username must be 3-20 characters, letters, numbers, and underscores only.';
                    }
                    break;

                case 'phone':
                    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
                    if (!phoneRegex.test(value.replace(/[\s\-\(\)]/g, ''))) {
                        isValid = false;
                        message = 'Please enter a valid phone number.';
                    }
                    break;
            }
        }

        // Show error if invalid
        if (!isValid) {
            this.showFieldError(field, message);
        }

        return isValid;
    },

    showFieldError: function(field, message) {
        field.classList.add('is-invalid');
        
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            errorElement.style.color = '#dc3545';
            errorElement.style.fontSize = '0.875rem';
            errorElement.style.marginTop = '0.25rem';
            field.parentNode.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
    },

    clearFieldError: function(field) {
        field.classList.remove('is-invalid');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    },

    // Table sorting
    sortTable: function(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');

        // Remove existing sort classes
        header.parentNode.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });

        // Add new sort class
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');

        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            // Try to parse as numbers
            const aNum = parseFloat(aValue);
            const bNum = parseFloat(bValue);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? aNum - bNum : bNum - aNum;
            }
            
            // String comparison
            return isAscending ? 
                aValue.localeCompare(bValue) : 
                bValue.localeCompare(aValue);
        });

        // Reorder rows in DOM
        rows.forEach(row => tbody.appendChild(row));
    },

    // Show alert message
    showAlert: function(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container') || document.body;
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.style.position = 'fixed';
        alert.style.top = '20px';
        alert.style.right = '20px';
        alert.style.zIndex = '9999';
        alert.style.minWidth = '300px';
        alert.innerHTML = `
            ${message}
            <button type="button" class="alert-close" style="float: right; background: none; border: none; font-size: 1.2rem; cursor: pointer;">&times;</button>
        `;

        alertContainer.appendChild(alert);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 5000);

        // Manual close
        alert.querySelector('.alert-close').addEventListener('click', () => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        });
    },

    // AJAX helper
    ajax: function(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        const config = Object.assign(defaults, options);

        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('AJAX error:', error);
                this.showAlert('An error occurred. Please try again.', 'danger');
                throw error;
            });
    },

    // Utility functions
    formatDate: function(date) {
        return new Date(date).toLocaleDateString();
    },

    formatDateTime: function(date) {
        return new Date(date).toLocaleString();
    },

    formatCurrency: function(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    Admin.init();
});

// Export for use in other scripts
window.Admin = Admin;
