<?php
/**
 * Final Tournament System Test
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

echo "<h1>Final Tournament System Test</h1>";
echo "<p>Testing the complete tournament creation process after all fixes...</p>";

try {
    // Test 1: Check database schema
    echo "<h3>Test 1: Database Schema ✅</h3>";
    
    $tables_to_check = [
        'tournament_formats',
        'tournament_structures', 
        'tournament_participants',
        'tournament_rounds',
        'tournament_matches',
        'sport_categories',
        'registrations'
    ];
    
    foreach ($tables_to_check as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "<p style='color: green;'>✓ Table '{$table}' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '{$table}' missing</p>";
        }
    }
    
    // Test 2: Check algorithm classes
    echo "<h3>Test 2: Algorithm Classes ✅</h3>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats WHERE algorithm_class IS NOT NULL");
    $stmt->execute();
    $formats_with_algorithms = $stmt->fetch()['count'];
    
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM tournament_formats");
    $stmt->execute();
    $total_formats = $stmt->fetch()['total'];
    
    echo "<p>Tournament formats with algorithm classes: {$formats_with_algorithms}/{$total_formats}</p>";
    
    if ($formats_with_algorithms == $total_formats) {
        echo "<p style='color: green;'>✓ All tournament formats have algorithm classes assigned</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Some tournament formats missing algorithm classes</p>";
    }
    
    // Test 3: Check registrations
    echo "<h3>Test 3: Registration Data ✅</h3>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM registrations WHERE status IN ('confirmed', 'approved')");
    $stmt->execute();
    $confirmed_registrations = $stmt->fetch()['count'];
    
    echo "<p>Confirmed/approved registrations: {$confirmed_registrations}</p>";
    
    if ($confirmed_registrations >= 2) {
        echo "<p style='color: green;'>✓ Sufficient registrations for tournament testing</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Need more registrations for tournament testing</p>";
        echo "<p><a href='create-test-registrations.php'>Create Test Registrations</a></p>";
    }
    
    // Test 4: Test AJAX endpoint directly
    echo "<h3>Test 4: AJAX Endpoint Test ✅</h3>";
    
    // Get first event sport with registrations
    $stmt = $conn->prepare("
        SELECT es.id, COUNT(r.id) as reg_count
        FROM event_sports es
        LEFT JOIN registrations r ON es.id = r.event_sport_id AND r.status IN ('confirmed', 'approved')
        GROUP BY es.id
        HAVING reg_count >= 2
        LIMIT 1
    ");
    $stmt->execute();
    $test_event_sport = $stmt->fetch();
    
    if ($test_event_sport) {
        echo "<p>Testing with event_sport_id: {$test_event_sport['id']} ({$test_event_sport['reg_count']} registrations)</p>";
        
        // Simulate AJAX call
        $_POST = [
            'event_sport_id' => $test_event_sport['id'],
            'tournament_name' => 'Test Tournament',
            'format_id' => 7, // Single elimination
            'seeding_method' => 'random'
        ];
        
        // Capture output
        ob_start();
        try {
            include 'ajax/create-tournament.php';
            $ajax_output = ob_get_clean();
            
            $response = json_decode($ajax_output, true);
            if ($response && isset($response['success']) && $response['success']) {
                echo "<p style='color: green;'>✓ AJAX endpoint working: Tournament created successfully</p>";
                echo "<p>Tournament ID: {$response['tournament_id']}</p>";
                echo "<p>Participants: {$response['participants_count']}</p>";
            } else {
                echo "<p style='color: red;'>❌ AJAX endpoint failed</p>";
                echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>❌ AJAX endpoint error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No event sports with sufficient registrations for testing</p>";
    }
    
    // Test 5: Check for any remaining 'categories' table references
    echo "<h3>Test 5: Code Verification ✅</h3>";
    
    $files_to_check = [
        'ajax/create-tournament.php',
        'test-tournament-creation.php',
        '../includes/tournament_manager.php'
    ];
    
    $categories_references = 0;
    foreach ($files_to_check as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            if (strpos($content, 'categories c') !== false || strpos($content, 'JOIN categories') !== false) {
                $categories_references++;
                echo "<p style='color: red;'>❌ Found 'categories' table reference in {$file}</p>";
            }
        }
    }
    
    if ($categories_references == 0) {
        echo "<p style='color: green;'>✓ No remaining 'categories' table references found</p>";
    }
    
    echo "<h2>🎉 Final Test Results:</h2>";
    
    if ($formats_with_algorithms == $total_formats && $confirmed_registrations >= 2 && $categories_references == 0) {
        echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ ALL TESTS PASSED!</p>";
        echo "<p style='color: green;'>The tournament creation system is now fully operational.</p>";
        echo "<p><strong>You can now:</strong></p>";
        echo "<ul>";
        echo "<li>Create tournaments from the category management page</li>";
        echo "<li>Generate automatic brackets based on sport types</li>";
        echo "<li>Manage tournament progression and scoring</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: orange; font-size: 18px; font-weight: bold;'>⚠ SOME ISSUES REMAIN</p>";
        echo "<p>Please address the issues noted above before using the tournament system.</p>";
    }
    
    echo "<p><a href='manage-category.php?event_id=1&sport_id=1&category_id=2'>Test Tournament Creation</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Test Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
