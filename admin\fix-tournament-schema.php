<?php
/**
 * Fix Tournament Schema Issues
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script fixes the tournament database schema issues:
 * 1. Adds missing algorithm_class column to tournament_formats table
 * 2. Updates tournament formats with correct algorithm class names
 * 3. Ensures all required tournament tables exist
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

$errors = [];
$messages = [];

try {
    echo "<h1>Tournament Schema Fix</h1>";
    echo "<p>Fixing tournament database schema issues...</p>";
    
    // Check if algorithm_class column exists
    $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'algorithm_class'");
    $stmt->execute();
    $column_exists = $stmt->fetch();
    
    if (!$column_exists) {
        echo "<h3>Adding algorithm_class column to tournament_formats table...</h3>";
        
        $sql = "ALTER TABLE tournament_formats ADD COLUMN algorithm_class VARCHAR(100) NULL AFTER description";
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ Added algorithm_class column</p>";
    } else {
        echo "<p style='color: green;'>✓ algorithm_class column already exists</p>";
    }
    
    // Update tournament formats with algorithm classes
    echo "<h3>Updating tournament formats with algorithm classes...</h3>";
    
    $algorithm_mappings = [
        'single_elimination' => 'SingleEliminationAlgorithm',
        'double_elimination' => 'DoubleEliminationAlgorithm',
        'round_robin' => 'RoundRobinAlgorithm',
        'multi_stage' => 'RoundRobinAlgorithm', // Use round robin for multi-stage for now
        'swiss_system' => 'SwissSystemAlgorithm',
        'knockout_rounds' => 'SingleEliminationAlgorithm', // Use single elimination for knockout
        'judged_rounds' => 'RoundRobinAlgorithm', // Use round robin for judged rounds for now
        'talent_showcase' => 'RoundRobinAlgorithm', // Use round robin for talent showcase for now
        'performance_competition' => 'RoundRobinAlgorithm', // Use round robin for performance competition for now
        'artistic_judging' => 'RoundRobinAlgorithm', // Use round robin for artistic judging for now
        'elimination_rounds' => 'SingleEliminationAlgorithm'
    ];
    
    foreach ($algorithm_mappings as $code => $algorithm_class) {
        $stmt = $conn->prepare("UPDATE tournament_formats SET algorithm_class = ? WHERE code = ?");
        $stmt->execute([$algorithm_class, $code]);
        
        if ($stmt->rowCount() > 0) {
            echo "<p style='color: green;'>✓ Updated {$code} with {$algorithm_class}</p>";
        } else {
            echo "<p style='color: orange;'>⚠ No format found with code: {$code}</p>";
        }
    }
    
    // Check if sport_type_category column exists and add it if missing
    $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'sport_type_category'");
    $stmt->execute();
    $category_column_exists = $stmt->fetch();
    
    if (!$category_column_exists) {
        echo "<h3>Adding sport_type_category column...</h3>";
        
        $sql = "ALTER TABLE tournament_formats ADD COLUMN sport_type_category ENUM('individual', 'team', 'academic', 'judged', 'all') DEFAULT 'all' AFTER algorithm_class";
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ Added sport_type_category column</p>";
        
        // Update sport type categories based on format codes
        $category_mappings = [
            'single_elimination' => 'all',
            'double_elimination' => 'all',
            'round_robin' => 'all',
            'multi_stage' => 'team',
            'swiss_system' => 'academic',
            'knockout_rounds' => 'academic',
            'judged_rounds' => 'judged',
            'talent_showcase' => 'judged',
            'performance_competition' => 'judged',
            'artistic_judging' => 'judged',
            'elimination_rounds' => 'individual'
        ];
        
        foreach ($category_mappings as $code => $category) {
            $stmt = $conn->prepare("UPDATE tournament_formats SET sport_type_category = ? WHERE code = ?");
            $stmt->execute([$category, $code]);
            
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✓ Updated {$code} with category {$category}</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✓ sport_type_category column already exists</p>";
    }
    
    // Verify the updates
    echo "<h3>Verification - Current Tournament Formats:</h3>";
    $stmt = $conn->prepare("SELECT id, name, code, algorithm_class, sport_type_category FROM tournament_formats ORDER BY id");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Algorithm Class</th><th>Sport Type Category</th></tr>";
    
    foreach ($formats as $format) {
        $algorithm_status = $format['algorithm_class'] ? 'style="color: green;"' : 'style="color: red;"';
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td {$algorithm_status}>{$format['algorithm_class']}</td>";
        echo "<td>{$format['sport_type_category']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Schema Fix Complete!</h3>";
    echo "<p style='color: green; font-weight: bold;'>✓ Tournament schema has been successfully updated.</p>";
    echo "<p><a href='test-tournament-creation.php'>Test Tournament Creation</a></p>";
    echo "<p><a href='manage-category.php?event_id=1&sport_id=1&category_id=2'>Back to Category Management</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
