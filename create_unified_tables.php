<?php
/**
 * <PERSON><PERSON>t to create unified registration tables
 */

require_once 'config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "Creating Unified Registration Tables...\n";

// Table 1: Event Department Registrations
$sql1 = "CREATE TABLE IF NOT EXISTS event_department_registrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    department_id INT NOT NULL,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'approved', 'rejected', 'withdrawn') DEFAULT 'pending',
    contact_person VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    notes TEXT,
    total_participants INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    <PERSON>OREIG<PERSON> KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_department (event_id, department_id)
)";

// Table 2: Department Sport Participations
$sql2 = "CREATE TABLE IF NOT EXISTS department_sport_participations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_department_registration_id INT NOT NULL,
    event_sport_id INT NOT NULL,
    team_name VARCHAR(255),
    participants JSON,
    participation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('registered', 'confirmed', 'withdrawn') DEFAULT 'registered',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_department_registration_id) REFERENCES event_department_registrations(id) ON DELETE CASCADE,
    FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
    UNIQUE KEY unique_dept_sport_participation (event_department_registration_id, event_sport_id)
)";

// Table 3: Department Overall Scores
$sql3 = "CREATE TABLE IF NOT EXISTS department_overall_scores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_department_registration_id INT NOT NULL,
    sport_id INT NOT NULL,
    position INT,
    points_earned DECIMAL(10,2) DEFAULT 0,
    bonus_points DECIMAL(10,2) DEFAULT 0,
    penalty_points DECIMAL(10,2) DEFAULT 0,
    final_score DECIMAL(10,2) DEFAULT 0,
    performance_data JSON,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_department_registration_id) REFERENCES event_department_registrations(id) ON DELETE CASCADE,
    FOREIGN KEY (sport_id) REFERENCES sports(id) ON DELETE CASCADE,
    UNIQUE KEY unique_dept_sport_score (event_department_registration_id, sport_id)
)";

// Table 4: Event Overall Standings
$sql4 = "CREATE TABLE IF NOT EXISTS event_overall_standings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    department_id INT NOT NULL,
    total_points DECIMAL(10,2) DEFAULT 0,
    sports_participated INT DEFAULT 0,
    sports_won INT DEFAULT 0,
    sports_podium INT DEFAULT 0,
    average_position DECIMAL(5,2),
    overall_rank INT,
    is_overall_winner BOOLEAN DEFAULT FALSE,
    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_dept_standing (event_id, department_id)
)";

$tables = [
    'event_department_registrations' => $sql1,
    'department_sport_participations' => $sql2,
    'department_overall_scores' => $sql3,
    'event_overall_standings' => $sql4
];

foreach ($tables as $table_name => $sql) {
    try {
        $conn->exec($sql);
        echo "✅ Created table: $table_name\n";
    } catch (PDOException $e) {
        echo "❌ Error creating table $table_name: " . $e->getMessage() . "\n";
    }
}

echo "\nVerifying tables...\n";
foreach (array_keys($tables) as $table) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "✅ Table '$table' exists\n";
        } else {
            echo "❌ Table '$table' missing\n";
        }
    } catch (PDOException $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "\n";
    }
}

echo "\nDone!\n";
?>
