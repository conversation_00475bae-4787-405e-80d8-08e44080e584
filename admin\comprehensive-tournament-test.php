<?php
/**
 * Comprehensive Tournament Test
 * SC_IMS Sports Competition and Event Management System
 * 
 * Complete end-to-end test of tournament creation after emergency fix
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Comprehensive Tournament Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Comprehensive Tournament Test</h1>
        <p><strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="highlight">
            <h3>🎯 Test Objectives</h3>
            <p>This comprehensive test will:</p>
            <ul>
                <li>✅ Verify database schema is correct</li>
                <li>✅ Test tournament format consistency</li>
                <li>✅ Create a complete tournament end-to-end</li>
                <li>✅ Confirm no "tournament_structure_id" errors occur</li>
            </ul>
        </div>
        
        <?php
        $overall_success = true;
        $test_results = [];
        
        try {
            // Step 1: Verify database schema
            echo '<div class="step">';
            echo '<h2>Step 1: Database Schema Verification</h2>';
            
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            $schema_ok = true;
            
            foreach ($required_columns as $column) {
                if (in_array($column, $columns)) {
                    echo '<p>✅ ' . $column . ' column exists</p>';
                } else {
                    echo '<p>❌ ' . $column . ' column missing</p>';
                    $schema_ok = false;
                    $overall_success = false;
                }
            }
            
            if ($schema_ok) {
                echo '<div class="success">✅ Database schema is correct</div>';
                $test_results['schema'] = true;
            } else {
                echo '<div class="error">❌ Database schema is incomplete</div>';
                $test_results['schema'] = false;
            }
            
            echo '</div>';
            
            // Step 2: Verify tournament tables
            echo '<div class="step">';
            echo '<h2>Step 2: Tournament Tables Verification</h2>';
            
            $tournament_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
            $tables_ok = true;
            
            foreach ($tournament_tables as $table) {
                $stmt = $conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if ($stmt->fetch()) {
                    echo '<p>✅ ' . $table . ' table exists</p>';
                } else {
                    echo '<p>❌ ' . $table . ' table missing</p>';
                    $tables_ok = false;
                    $overall_success = false;
                }
            }
            
            if ($tables_ok) {
                echo '<div class="success">✅ All tournament tables exist</div>';
                $test_results['tables'] = true;
            } else {
                echo '<div class="error">❌ Some tournament tables are missing</div>';
                $test_results['tables'] = false;
            }
            
            echo '</div>';
            
            // Step 3: Check tournament formats
            echo '<div class="step">';
            echo '<h2>Step 3: Tournament Formats Check</h2>';
            
            $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY name");
            $stmt->execute();
            $formats = $stmt->fetchAll();
            
            if ($formats) {
                echo '<h3>Available Tournament Formats:</h3>';
                echo '<table>';
                echo '<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Types</th><th>Algorithm</th></tr>';
                foreach ($formats as $format) {
                    echo '<tr>';
                    echo '<td>' . $format['id'] . '</td>';
                    echo '<td>' . htmlspecialchars($format['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($format['code']) . '</td>';
                    echo '<td>' . htmlspecialchars($format['sport_types']) . '</td>';
                    echo '<td>' . htmlspecialchars($format['algorithm_class'] ?? 'N/A') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                
                echo '<div class="success">✅ Tournament formats are available (' . count($formats) . ' formats)</div>';
                $test_results['formats'] = true;
            } else {
                echo '<div class="error">❌ No tournament formats found</div>';
                $test_results['formats'] = false;
                $overall_success = false;
            }
            
            echo '</div>';
            
            // Step 4: Prepare test data
            echo '<div class="step">';
            echo '<h2>Step 4: Test Data Preparation</h2>';
            
            // Ensure we have an event
            $stmt = $conn->prepare("SELECT id FROM events LIMIT 1");
            $stmt->execute();
            $event_id = $stmt->fetchColumn();
            
            if (!$event_id) {
                $stmt = $conn->prepare("INSERT INTO events (name, description, start_date, end_date, status) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute(['Comprehensive Test Event', 'Event for comprehensive tournament test', date('Y-m-d'), date('Y-m-d', strtotime('+7 days')), 'active']);
                $event_id = $conn->lastInsertId();
                echo '<p>✅ Created test event (ID: ' . $event_id . ')</p>';
            } else {
                echo '<p>✅ Using existing event (ID: ' . $event_id . ')</p>';
            }
            
            // Ensure we have a sport
            $stmt = $conn->prepare("SELECT id FROM sports LIMIT 1");
            $stmt->execute();
            $sport_id = $stmt->fetchColumn();
            
            if (!$sport_id) {
                $stmt = $conn->prepare("INSERT INTO sports (name, type, scoring_method, bracket_format) VALUES (?, ?, ?, ?)");
                $stmt->execute(['Comprehensive Test Sport', 'traditional', 'point_based', 'single_elimination']);
                $sport_id = $conn->lastInsertId();
                echo '<p>✅ Created test sport (ID: ' . $sport_id . ')</p>';
            } else {
                echo '<p>✅ Using existing sport (ID: ' . $sport_id . ')</p>';
            }
            
            // Ensure we have an event_sport
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$event_id, $sport_id]);
            $event_sport_id = $stmt->fetchColumn();
            
            if (!$event_sport_id) {
                $stmt = $conn->prepare("INSERT INTO event_sports (event_id, sport_id, status) VALUES (?, ?, ?)");
                $stmt->execute([$event_id, $sport_id, 'active']);
                $event_sport_id = $conn->lastInsertId();
                echo '<p>✅ Created event-sport association (ID: ' . $event_sport_id . ')</p>';
            } else {
                echo '<p>✅ Using existing event-sport (ID: ' . $event_sport_id . ')</p>';
            }
            
            // Ensure we have departments and registrations
            $stmt = $conn->prepare("SELECT COUNT(*) FROM departments");
            $stmt->execute();
            $dept_count = $stmt->fetchColumn();
            
            if ($dept_count < 4) {
                for ($i = 1; $i <= 4; $i++) {
                    $stmt = $conn->prepare("INSERT IGNORE INTO departments (name, abbreviation, contact_info) VALUES (?, ?, ?)");
                    $stmt->execute(["Comprehensive Test Dept $i", "CTD$i", "Contact info for dept $i"]);
                }
                echo '<p>✅ Created test departments</p>';
            }
            
            // Create registrations for the event-sport
            $stmt = $conn->prepare("SELECT id FROM departments LIMIT 4");
            $stmt->execute();
            $departments = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $registration_ids = [];
            foreach ($departments as $dept_id) {
                // Check if registration exists
                $stmt = $conn->prepare("SELECT id FROM registrations WHERE event_sport_id = ? AND department_id = ?");
                $stmt->execute([$event_sport_id, $dept_id]);
                $reg_id = $stmt->fetchColumn();
                
                if (!$reg_id) {
                    $stmt = $conn->prepare("INSERT INTO registrations (event_sport_id, department_id, team_name, status) VALUES (?, ?, ?, ?)");
                    $stmt->execute([$event_sport_id, $dept_id, "Team $dept_id", 'confirmed']);
                    $reg_id = $conn->lastInsertId();
                }
                $registration_ids[] = $reg_id;
            }
            
            echo '<p>✅ Ensured ' . count($registration_ids) . ' registrations exist</p>';
            echo '<div class="success">✅ Test data preparation complete</div>';
            $test_results['data_prep'] = true;
            
            echo '</div>';
            
            // Step 5: Test tournament creation
            echo '<div class="step">';
            echo '<h2>Step 5: Tournament Creation Test</h2>';
            
            if ($schema_ok && $tables_ok && $test_results['formats']) {
                try {
                    // Include tournament manager
                    require_once '../includes/tournament_manager.php';
                    echo '<p>✅ Tournament manager loaded</p>';
                    
                    // Get tournament format
                    $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = 'single_elimination' LIMIT 1");
                    $stmt->execute();
                    $format_id = $stmt->fetchColumn();
                    
                    if (!$format_id) {
                        echo '<div class="error">❌ Single elimination format not found</div>';
                        $test_results['tournament_creation'] = false;
                        $overall_success = false;
                    } else {
                        echo '<p>✅ Using tournament format (ID: ' . $format_id . ')</p>';
                        
                        // Create tournament manager instance
                        $tournamentManager = new TournamentManager($conn);
                        echo '<p>✅ Tournament manager instance created</p>';
                        
                        // Test tournament creation
                        $config = [
                            'seeding_method' => 'random',
                            'scoring_config' => [
                                'points_win' => 3,
                                'points_draw' => 1,
                                'points_loss' => 0
                            ]
                        ];
                        
                        $tournament_name = "Comprehensive Test Tournament " . date('H:i:s');
                        echo '<p>Creating tournament: ' . htmlspecialchars($tournament_name) . '</p>';
                        
                        $tournament_id = $tournamentManager->createTournament($event_sport_id, $format_id, $tournament_name, $config);
                        
                        echo '<div class="success">';
                        echo '<h3>🎉 TOURNAMENT CREATION SUCCESSFUL!</h3>';
                        echo '<p><strong>Tournament ID:</strong> ' . $tournament_id . '</p>';
                        echo '<p><strong>✅ No "tournament_structure_id" column errors occurred!</strong></p>';
                        echo '<p><strong>✅ Tournament format inconsistency has been resolved!</strong></p>';
                        echo '</div>';
                        
                        $test_results['tournament_creation'] = true;
                        
                        // Show tournament details
                        $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE id = ?");
                        $stmt->execute([$tournament_id]);
                        $tournament = $stmt->fetch();
                        
                        if ($tournament) {
                            echo '<h3>Tournament Details:</h3>';
                            echo '<table>';
                            echo '<tr><th>Property</th><th>Value</th></tr>';
                            echo '<tr><td>ID</td><td>' . $tournament['id'] . '</td></tr>';
                            echo '<tr><td>Name</td><td>' . htmlspecialchars($tournament['name']) . '</td></tr>';
                            echo '<tr><td>Status</td><td>' . htmlspecialchars($tournament['status']) . '</td></tr>';
                            echo '<tr><td>Participants</td><td>' . $tournament['participant_count'] . '</td></tr>';
                            echo '<tr><td>Total Rounds</td><td>' . $tournament['total_rounds'] . '</td></tr>';
                            echo '</table>';
                        }
                        
                        // Show created matches
                        $stmt = $conn->prepare("SELECT COUNT(*) FROM matches WHERE tournament_structure_id = ?");
                        $stmt->execute([$tournament_id]);
                        $match_count = $stmt->fetchColumn();
                        
                        echo '<p>✅ Created ' . $match_count . ' tournament matches</p>';
                        
                        if ($match_count > 0) {
                            $stmt = $conn->prepare("
                                SELECT m.id, m.round_number, m.bracket_position, m.is_bye_match, m.status,
                                       r1.team_name as team1_name, r2.team_name as team2_name
                                FROM matches m
                                LEFT JOIN registrations r1 ON m.team1_id = r1.id
                                LEFT JOIN registrations r2 ON m.team2_id = r2.id
                                WHERE m.tournament_structure_id = ?
                                ORDER BY m.round_number, m.bracket_position
                                LIMIT 5
                            ");
                            $stmt->execute([$tournament_id]);
                            $matches = $stmt->fetchAll();
                            
                            echo '<h3>Sample Tournament Matches:</h3>';
                            echo '<table>';
                            echo '<tr><th>ID</th><th>Round</th><th>Position</th><th>Team 1</th><th>Team 2</th><th>Status</th></tr>';
                            foreach ($matches as $match) {
                                echo '<tr>';
                                echo '<td>' . $match['id'] . '</td>';
                                echo '<td>' . $match['round_number'] . '</td>';
                                echo '<td>' . htmlspecialchars($match['bracket_position']) . '</td>';
                                echo '<td>' . htmlspecialchars($match['team1_name'] ?? 'BYE') . '</td>';
                                echo '<td>' . htmlspecialchars($match['team2_name'] ?? 'BYE') . '</td>';
                                echo '<td>' . htmlspecialchars($match['status']) . '</td>';
                                echo '</tr>';
                            }
                            echo '</table>';
                        }
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="error">';
                    echo '<h3>❌ TOURNAMENT CREATION FAILED</h3>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
                    echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
                    echo '</div>';
                    
                    $test_results['tournament_creation'] = false;
                    $overall_success = false;
                }
            } else {
                echo '<div class="warning">⚠️ Skipping tournament creation test due to prerequisite failures</div>';
                $test_results['tournament_creation'] = false;
                $overall_success = false;
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Test error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $overall_success = false;
        }
        ?>
        
        <!-- Final Results -->
        <div class="step">
            <h2>🏁 Comprehensive Test Results</h2>
            
            <?php if ($overall_success && ($test_results['tournament_creation'] ?? false)): ?>
                <div class="success">
                    <h3>🎉 ALL TESTS PASSED!</h3>
                    <p><strong>✅ The "tournament_structure_id" column error has been permanently resolved!</strong></p>
                    <p><strong>✅ Tournament format inconsistency has been fixed!</strong></p>
                    <p><strong>✅ Tournament creation is working correctly!</strong></p>
                    <p><strong>✅ The database schema fix is permanent and persistent!</strong></p>
                </div>
                
                <div class="highlight">
                    <h3>🎯 Confirmation</h3>
                    <p>Your sports competition management system is now fully functional for tournament creation. Both critical issues have been resolved:</p>
                    <ul>
                        <li><strong>Database Error Fixed:</strong> No more "tournament_structure_id" column errors</li>
                        <li><strong>Format Consistency:</strong> Tournament formats are now synchronized across the system</li>
                    </ul>
                </div>
                
            <?php else: ?>
                <div class="error">
                    <h3>❌ Some Tests Failed</h3>
                    <p>The comprehensive test was not completely successful. Please review the errors above.</p>
                </div>
            <?php endif; ?>
            
            <h3>Test Summary:</h3>
            <table>
                <tr><th>Test</th><th>Result</th></tr>
                <tr style="background: <?php echo ($test_results['schema'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Database Schema</td>
                    <td><?php echo ($test_results['schema'] ?? false) ? '✅ PASS' : '❌ FAIL'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['tables'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Tournament Tables</td>
                    <td><?php echo ($test_results['tables'] ?? false) ? '✅ PASS' : '❌ FAIL'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['formats'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Tournament Formats</td>
                    <td><?php echo ($test_results['formats'] ?? false) ? '✅ PASS' : '❌ FAIL'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['data_prep'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Test Data Preparation</td>
                    <td><?php echo ($test_results['data_prep'] ?? false) ? '✅ PASS' : '❌ FAIL'; ?></td>
                </tr>
                <tr style="background: <?php echo ($test_results['tournament_creation'] ?? false) ? '#d4edda' : '#f8d7da'; ?>">
                    <td>Tournament Creation</td>
                    <td><?php echo ($test_results['tournament_creation'] ?? false) ? '✅ PASS' : '❌ FAIL'; ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Actions -->
        <div class="step">
            <h2>🛠️ Available Actions</h2>
            <p>
                <?php if ($overall_success): ?>
                    <a href="manage-event.php?id=<?php echo $event_id; ?>" class="btn btn-success">📋 Manage Events</a>
                    <a href="index.php" class="btn">🏠 Admin Dashboard</a>
                <?php else: ?>
                    <a href="emergency-database-fix.php" class="btn btn-warning">🚨 Re-run Emergency Fix</a>
                    <a href="critical-database-investigation.php" class="btn">🔍 Investigate Issues</a>
                <?php endif; ?>
            </p>
        </div>
    </div>
</body>
</html>
