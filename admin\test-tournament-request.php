<?php
/**
 * Test Tournament Creation Request
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Test Tournament Creation Request</h1>";
echo "<p>This will simulate the exact request being sent and debug the issue...</p>";

$badminton_event_sport_id = 18;

echo "<h2>1. Check Tournament Formats Table</h2>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$formats = $stmt->fetchAll();

if (empty($formats)) {
    echo "<p style='color: red; font-size: 18px;'>❌ <strong>CRITICAL ISSUE:</strong> No tournament formats found!</p>";
    echo "<p><a href='emergency-tournament-fix.php' style='background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-weight: bold;'>🚨 Run Emergency Fix</a></p>";
    exit;
} else {
    echo "<p style='color: green;'>✅ Found " . count($formats) . " tournament formats</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f5f5f5;'><th>ID</th><th>Name</th><th>Code</th></tr>";
    foreach ($formats as $format) {
        echo "<tr><td>{$format['id']}</td><td>{$format['name']}</td><td>{$format['code']}</td></tr>";
    }
    echo "</table>";
}

echo "<h2>2. Test Format ID 1 Specifically</h2>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
$stmt->execute([1]);
$format_1 = $stmt->fetch();

if ($format_1) {
    echo "<p style='color: green;'>✅ Format ID 1 exists: {$format_1['name']}</p>";
} else {
    echo "<p style='color: red;'>❌ Format ID 1 does not exist!</p>";
    echo "<p>Available format IDs: ";
    foreach ($formats as $f) {
        echo "<strong>{$f['id']}</strong> ";
    }
    echo "</p>";
}

echo "<h2>3. Simulate Tournament Creation Request</h2>";

if (isset($_POST['test_create'])) {
    echo "<h3>🔄 Processing Test Request...</h3>";
    
    $eventSportId = $_POST['event_sport_id'];
    $tournamentName = $_POST['tournament_name'];
    $formatId = $_POST['format_id'];
    $seedingMethod = $_POST['seeding_method'];
    
    echo "<p><strong>Request Parameters:</strong></p>";
    echo "<ul>";
    echo "<li><strong>event_sport_id:</strong> {$eventSportId}</li>";
    echo "<li><strong>tournament_name:</strong> {$tournamentName}</li>";
    echo "<li><strong>format_id:</strong> {$formatId}</li>";
    echo "<li><strong>seeding_method:</strong> {$seedingMethod}</li>";
    echo "</ul>";
    
    try {
        // Test the exact validation from create-tournament.php
        echo "<h4>Step 1: Validate Format ID</h4>";
        $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
        $stmt->execute([$formatId]);
        $format = $stmt->fetch();
        
        if (!$format) {
            throw new Exception("Invalid tournament format - Format ID {$formatId} not found");
        }
        echo "<p style='color: green;'>✅ Format validation passed: {$format['name']}</p>";
        
        echo "<h4>Step 2: Check Participants</h4>";
        $stmt = $conn->prepare("
            SELECT 
                dsp.id,
                COALESCE(dsp.team_name, d.name) as team_name,
                edr.department_id,
                d.name as department_name,
                dsp.status
            FROM event_department_registrations edr
            JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            JOIN departments d ON edr.department_id = d.id
            WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
        ");
        $stmt->execute([$eventSportId]);
        $participants = $stmt->fetchAll();
        
        echo "<p><strong>Participants found:</strong> " . count($participants) . "</p>";
        
        if (count($participants) < $format['min_participants']) {
            throw new Exception("Not enough participants for this tournament format. Minimum required: " . $format['min_participants']);
        }
        echo "<p style='color: green;'>✅ Participant validation passed</p>";
        
        echo "<h4>✅ All Validations Passed!</h4>";
        echo "<p style='color: green; font-size: 18px;'><strong>Tournament creation should work with these parameters!</strong></p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red; font-size: 18px;'>❌ <strong>ERROR:</strong> " . $e->getMessage() . "</p>";
    }
}

echo "<h3>🧪 Test Tournament Creation</h3>";
echo "<form method='POST'>";
echo "<table>";
echo "<tr><td><strong>Event Sport ID:</strong></td><td><input type='number' name='event_sport_id' value='{$badminton_event_sport_id}' readonly></td></tr>";
echo "<tr><td><strong>Tournament Name:</strong></td><td><input type='text' name='tournament_name' value='Badminton - Mixed Doubles Tournament' style='width: 300px;'></td></tr>";
echo "<tr><td><strong>Format ID:</strong></td><td>";
echo "<select name='format_id'>";
foreach ($formats as $format) {
    $selected = ($format['id'] == 1) ? 'selected' : '';
    echo "<option value='{$format['id']}' {$selected}>{$format['id']} - {$format['name']}</option>";
}
echo "</select>";
echo "</td></tr>";
echo "<tr><td><strong>Seeding Method:</strong></td><td>";
echo "<select name='seeding_method'>";
echo "<option value='random' selected>Random</option>";
echo "<option value='ranking'>Ranking</option>";
echo "</select>";
echo "</td></tr>";
echo "</table>";
echo "<br>";
echo "<button type='submit' name='test_create' value='1' style='background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;'>🧪 Test Tournament Creation</button>";
echo "</form>";

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='emergency-tournament-fix.php'>🚨 Emergency Tournament Format Fix</a></li>";
echo "<li><a href='instant-badminton-fix.php'>🔧 Fix Registration Status</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation Page</a></li>";
echo "</ul>";
?>
