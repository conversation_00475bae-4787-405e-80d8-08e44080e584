<?php
/**
 * Final Tournament Creation Test
 * SC_IMS Sports Competition and Event Management System
 * 
 * This is the definitive test to verify tournament creation works after schema rebuild
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Final Tournament Creation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏆 Final Tournament Creation Test</h1>
        <p><strong>Test Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        $test_results = [];
        $errors = [];
        
        try {
            // Step 1: Pre-flight checks
            echo '<div class="step">';
            echo '<h2>✈️ Step 1: Pre-flight Schema Checks</h2>';
            
            // Check tournament columns exist
            $stmt = $conn->prepare("DESCRIBE matches");
            $stmt->execute();
            $matches_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
            
            $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
            $missing_columns = [];
            
            foreach ($required_columns as $column) {
                if (in_array($column, $matches_columns)) {
                    echo '<div class="success">✅ ' . $column . ' column exists</div>';
                    $test_results['column_' . $column] = true;
                } else {
                    echo '<div class="error">❌ ' . $column . ' column missing</div>';
                    $missing_columns[] = $column;
                    $test_results['column_' . $column] = false;
                }
            }
            
            if (!empty($missing_columns)) {
                echo '<div class="error">❌ Critical columns missing: ' . implode(', ', $missing_columns) . '</div>';
                echo '<p><a href="force-schema-rebuild.php" class="btn btn-danger">🔧 Run Schema Rebuild</a></p>';
                $errors[] = "Missing columns: " . implode(', ', $missing_columns);
            } else {
                echo '<div class="success">✅ All required columns present</div>';
            }
            
            echo '</div>';
            
            // Step 2: Test basic INSERT operations
            echo '<div class="step">';
            echo '<h2>💾 Step 2: Test Database INSERT Operations</h2>';
            
            if (empty($missing_columns)) {
                try {
                    // Test basic insert
                    $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, status) VALUES (999, 999, 999, 'test')";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute();
                    $basic_id = $conn->lastInsertId();
                    echo '<div class="success">✅ Basic INSERT successful</div>';
                    $test_results['basic_insert'] = true;
                    
                    // Test tournament columns insert
                    $sql = "INSERT INTO matches (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) VALUES (999, 999, 999, NULL, NULL, 'TEST', 0, 'test')";
                    $stmt = $conn->prepare($sql);
                    $stmt->execute();
                    $tournament_id = $conn->lastInsertId();
                    echo '<div class="success">✅ Tournament columns INSERT successful</div>';
                    $test_results['tournament_insert'] = true;
                    
                    // Clean up test records
                    $conn->prepare("DELETE FROM matches WHERE id IN (?, ?)")->execute([$basic_id, $tournament_id]);
                    echo '<div class="info">ℹ️ Test records cleaned up</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="error">❌ INSERT test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    $errors[] = "INSERT test failed: " . $e->getMessage();
                    $test_results['tournament_insert'] = false;
                }
            } else {
                echo '<div class="warning">⚠️ Skipping INSERT tests due to missing columns</div>';
                $test_results['tournament_insert'] = false;
            }
            
            echo '</div>';
            
            // Step 3: Prepare test data
            echo '<div class="step">';
            echo '<h2>📋 Step 3: Prepare Test Data</h2>';
            
            // Ensure we have an event
            $stmt = $conn->prepare("SELECT id FROM events LIMIT 1");
            $stmt->execute();
            $event_id = $stmt->fetchColumn();
            
            if (!$event_id) {
                echo '<p>Creating test event...</p>';
                $stmt = $conn->prepare("INSERT INTO events (name, description, start_date, end_date, status) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute(['Test Event', 'Test event for tournament creation', date('Y-m-d'), date('Y-m-d', strtotime('+7 days')), 'active']);
                $event_id = $conn->lastInsertId();
                echo '<div class="success">✅ Created test event (ID: ' . $event_id . ')</div>';
            } else {
                echo '<div class="info">ℹ️ Using existing event (ID: ' . $event_id . ')</div>';
            }
            
            // Ensure we have a sport
            $stmt = $conn->prepare("SELECT id FROM sports LIMIT 1");
            $stmt->execute();
            $sport_id = $stmt->fetchColumn();
            
            if (!$sport_id) {
                echo '<p>Creating test sport...</p>';
                $stmt = $conn->prepare("INSERT INTO sports (name, type, description) VALUES (?, ?, ?)");
                $stmt->execute(['Test Sport', 'team', 'Test sport for tournament creation']);
                $sport_id = $conn->lastInsertId();
                echo '<div class="success">✅ Created test sport (ID: ' . $sport_id . ')</div>';
            } else {
                echo '<div class="info">ℹ️ Using existing sport (ID: ' . $sport_id . ')</div>';
            }
            
            // Ensure we have an event_sport
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$event_id, $sport_id]);
            $event_sport_id = $stmt->fetchColumn();
            
            if (!$event_sport_id) {
                echo '<p>Creating event-sport association...</p>';
                $stmt = $conn->prepare("INSERT INTO event_sports (event_id, sport_id, status) VALUES (?, ?, ?)");
                $stmt->execute([$event_id, $sport_id, 'active']);
                $event_sport_id = $conn->lastInsertId();
                echo '<div class="success">✅ Created event-sport association (ID: ' . $event_sport_id . ')</div>';
            } else {
                echo '<div class="info">ℹ️ Using existing event-sport (ID: ' . $event_sport_id . ')</div>';
            }
            
            // Ensure we have departments and registrations
            $stmt = $conn->prepare("SELECT COUNT(*) FROM departments");
            $stmt->execute();
            $dept_count = $stmt->fetchColumn();
            
            if ($dept_count < 2) {
                echo '<p>Creating test departments...</p>';
                for ($i = 1; $i <= 4; $i++) {
                    $stmt = $conn->prepare("INSERT IGNORE INTO departments (name, contact_person, contact_email, contact_phone) VALUES (?, ?, ?, ?)");
                    $stmt->execute(["Test Department $i", "Contact $i", "test$<EMAIL>", "123-456-789$i"]);
                }
                echo '<div class="success">✅ Created test departments</div>';
            }
            
            // Create registrations
            $stmt = $conn->prepare("SELECT id FROM departments LIMIT 4");
            $stmt->execute();
            $departments = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $registration_count = 0;
            foreach ($departments as $dept_id) {
                // Create event department registration
                $stmt = $conn->prepare("INSERT IGNORE INTO event_department_registrations (event_id, department_id, registration_date, status) VALUES (?, ?, NOW(), 'approved')");
                $stmt->execute([$event_id, $dept_id]);
                $edr_id = $conn->lastInsertId() ?: $conn->prepare("SELECT id FROM event_department_registrations WHERE event_id = ? AND department_id = ?")->execute([$event_id, $dept_id]) ?: $conn->fetchColumn();
                
                // Create sport registration
                $stmt = $conn->prepare("INSERT IGNORE INTO registrations (event_department_registration_id, event_sport_id, team_name, registration_date, status) VALUES (?, ?, ?, NOW(), 'approved')");
                $stmt->execute([$edr_id, $event_sport_id, "Team $dept_id"]);
                $registration_count++;
            }
            
            echo '<div class="success">✅ Ensured ' . $registration_count . ' registrations exist</div>';
            
            echo '</div>';
            
            // Step 4: Test Tournament Manager
            echo '<div class="step">';
            echo '<h2>🏆 Step 4: Test Tournament Manager</h2>';
            
            if ($test_results['tournament_insert'] ?? false) {
                try {
                    // Include tournament manager
                    require_once '../includes/tournament_manager.php';
                    echo '<div class="success">✅ Tournament manager loaded successfully</div>';
                    
                    // Ensure tournament format exists
                    $stmt = $conn->prepare("SELECT id FROM tournament_formats LIMIT 1");
                    $stmt->execute();
                    $format_id = $stmt->fetchColumn();
                    
                    if (!$format_id) {
                        echo '<p>Creating tournament format...</p>';
                        $stmt = $conn->prepare("INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants, algorithm_class) VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $stmt->execute(['Single Elimination', 'single_elimination', 'Basic single elimination', 'team,individual', 2, 32, 'SingleEliminationAlgorithm']);
                        $format_id = $conn->lastInsertId();
                        echo '<div class="success">✅ Created tournament format (ID: ' . $format_id . ')</div>';
                    } else {
                        echo '<div class="info">ℹ️ Using existing tournament format (ID: ' . $format_id . ')</div>';
                    }
                    
                    // Create tournament manager instance
                    $tournamentManager = new TournamentManager($conn);
                    echo '<div class="success">✅ Tournament manager instance created</div>';
                    
                    // Test tournament creation
                    $config = [
                        'seeding_method' => 'random',
                        'scoring_config' => [
                            'points_win' => 3,
                            'points_draw' => 1,
                            'points_loss' => 0
                        ]
                    ];
                    
                    $tournament_name = "Final Test Tournament " . date('H:i:s');
                    echo '<p>Creating tournament: ' . htmlspecialchars($tournament_name) . '</p>';
                    
                    $tournament_id = $tournamentManager->createTournament($event_sport_id, $format_id, $tournament_name, $config);
                    
                    echo '<div class="success">🎉 <strong>TOURNAMENT CREATION SUCCESSFUL!</strong></div>';
                    echo '<div class="success">✅ Tournament ID: ' . $tournament_id . '</div>';
                    $test_results['tournament_creation'] = true;
                    
                    // Show tournament details
                    $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE id = ?");
                    $stmt->execute([$tournament_id]);
                    $tournament = $stmt->fetch();
                    
                    if ($tournament) {
                        echo '<h3>Tournament Details:</h3>';
                        echo '<table>';
                        echo '<tr><th>Property</th><th>Value</th></tr>';
                        echo '<tr><td>ID</td><td>' . $tournament['id'] . '</td></tr>';
                        echo '<tr><td>Name</td><td>' . htmlspecialchars($tournament['name']) . '</td></tr>';
                        echo '<tr><td>Status</td><td>' . htmlspecialchars($tournament['status']) . '</td></tr>';
                        echo '<tr><td>Participants</td><td>' . $tournament['participant_count'] . '</td></tr>';
                        echo '<tr><td>Total Rounds</td><td>' . $tournament['total_rounds'] . '</td></tr>';
                        echo '<tr><td>Current Round</td><td>' . $tournament['current_round'] . '</td></tr>';
                        echo '</table>';
                    }
                    
                    // Show created matches
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM matches WHERE tournament_structure_id = ?");
                    $stmt->execute([$tournament_id]);
                    $match_count = $stmt->fetchColumn();
                    
                    echo '<div class="success">✅ Created ' . $match_count . ' tournament matches</div>';
                    
                    if ($match_count > 0) {
                        $stmt = $conn->prepare("
                            SELECT m.id, m.round_number, m.bracket_position, m.is_bye_match, m.status,
                                   r1.team_name as team1_name, r2.team_name as team2_name
                            FROM matches m
                            LEFT JOIN registrations r1 ON m.team1_id = r1.id
                            LEFT JOIN registrations r2 ON m.team2_id = r2.id
                            WHERE m.tournament_structure_id = ?
                            ORDER BY m.round_number, m.bracket_position
                            LIMIT 10
                        ");
                        $stmt->execute([$tournament_id]);
                        $matches = $stmt->fetchAll();
                        
                        echo '<h3>Sample Tournament Matches:</h3>';
                        echo '<table>';
                        echo '<tr><th>ID</th><th>Round</th><th>Position</th><th>Team 1</th><th>Team 2</th><th>Status</th><th>Bye</th></tr>';
                        foreach ($matches as $match) {
                            echo '<tr>';
                            echo '<td>' . $match['id'] . '</td>';
                            echo '<td>' . $match['round_number'] . '</td>';
                            echo '<td>' . htmlspecialchars($match['bracket_position']) . '</td>';
                            echo '<td>' . htmlspecialchars($match['team1_name'] ?? 'BYE') . '</td>';
                            echo '<td>' . htmlspecialchars($match['team2_name'] ?? 'BYE') . '</td>';
                            echo '<td>' . htmlspecialchars($match['status']) . '</td>';
                            echo '<td>' . ($match['is_bye_match'] ? 'Yes' : 'No') . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="error">❌ <strong>TOURNAMENT CREATION FAILED:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
                    echo '<div class="error">File: ' . htmlspecialchars($e->getFile()) . ' Line: ' . $e->getLine() . '</div>';
                    $errors[] = "Tournament creation failed: " . $e->getMessage();
                    $test_results['tournament_creation'] = false;
                    
                    // Show stack trace for debugging
                    echo '<details>';
                    echo '<summary>Show Stack Trace</summary>';
                    echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
                    echo '</details>';
                }
            } else {
                echo '<div class="warning">⚠️ Skipping tournament creation test due to failed INSERT tests</div>';
                $test_results['tournament_creation'] = false;
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Test error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
        }
        ?>
        
        <!-- Final Results -->
        <div class="step">
            <h2>📊 Final Test Results</h2>
            
            <?php
            $all_tests_passed = empty($errors) && 
                                ($test_results['tournament_insert'] ?? false) && 
                                ($test_results['tournament_creation'] ?? false);
            ?>
            
            <?php if ($all_tests_passed): ?>
                <div class="success">
                    <h3>🎉 ALL TESTS PASSED!</h3>
                    <p><strong>Tournament creation is now working correctly!</strong></p>
                    <p>You can now create tournaments without the "tournament_structure_id" column error.</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h3>❌ Some Tests Failed</h3>
                    <?php if (!empty($errors)): ?>
                        <p><strong>Errors encountered:</strong></p>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <h3>Test Summary:</h3>
            <table>
                <tr><th>Test</th><th>Result</th></tr>
                <tr><td>Schema Columns</td><td><?php echo empty($missing_columns) ? '✅ PASS' : '❌ FAIL'; ?></td></tr>
                <tr><td>Database INSERT</td><td><?php echo ($test_results['tournament_insert'] ?? false) ? '✅ PASS' : '❌ FAIL'; ?></td></tr>
                <tr><td>Tournament Creation</td><td><?php echo ($test_results['tournament_creation'] ?? false) ? '✅ PASS' : '❌ FAIL'; ?></td></tr>
            </table>
        </div>
        
        <!-- Actions -->
        <div class="step">
            <h2>🛠️ Next Actions</h2>
            <p>
                <?php if (!$all_tests_passed): ?>
                    <a href="force-schema-rebuild.php" class="btn btn-danger">🔧 Force Schema Rebuild</a>
                    <a href="diagnose-database-state.php" class="btn btn-warning">🔍 Diagnose Database</a>
                <?php else: ?>
                    <a href="manage-event.php?id=<?php echo $event_id; ?>" class="btn btn-success">📋 Manage Event</a>
                    <a href="index.php" class="btn">🏠 Admin Dashboard</a>
                <?php endif; ?>
            </p>
        </div>
    </div>
</body>
</html>
