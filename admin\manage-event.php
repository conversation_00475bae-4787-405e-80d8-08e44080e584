<?php
/**
 * Comprehensive Event Management Page for SC_IMS Admin Panel
 * Central hub for managing all aspects of a specific event
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? null;
if (!$event_id) {
    header('Location: events.php');
    exit;
}

$event = getEventById($conn, $event_id);
if (!$event) {
    header('Location: events.php');
    exit;
}

// Get event sports with registration counts
$event_sports = getEventSports($conn, $event_id);

// Get all available sports not yet added to this event
$available_sports = getAvailableSports($conn, $event_id);

// Get all departments
$departments = getAllDepartments($conn);

// Get event standings/rankings
$standings = getEventStandings($conn, $event_id);

// Get recent matches for this event
$recent_matches = getEventRecentMatches($conn, $event_id);

// Calculate event progress
$total_sports = count($event_sports);
$completed_sports = 0;
$total_matches = 0;
$completed_matches = 0;

foreach ($event_sports as $sport) {
    $matches = getEventSportMatches($conn, $sport['id']);
    $total_matches += count($matches);
    $sport_completed = true;
    
    foreach ($matches as $match) {
        if ($match['status'] === 'completed') {
            $completed_matches++;
        } else {
            $sport_completed = false;
        }
    }
    
    if ($sport_completed && count($matches) > 0) {
        $completed_sports++;
    }
}

$event_progress = $total_matches > 0 ? round(($completed_matches / $total_matches) * 100, 1) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Event: <?php echo htmlspecialchars($event['name']); ?> - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
    
    <style>
        .event-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .event-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .event-meta {
            display: flex;
            gap: 30px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .meta-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .meta-item i {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .progress-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .progress-bar {
            width: 100%;
            height: 12px;
            background: #e9ecef;
            border-radius: 6px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .section-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            padding: 5px;
            margin-bottom: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .tab-button {
            flex: 1;
            padding: 18px 24px;
            border: none;
            background: transparent;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            min-height: 60px;
            color: #6c757d;
        }

        .tab-button:hover {
            background: #f8f9fa;
            color: #495057;
            transform: translateY(-1px);
        }

        .tab-button.active {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: #fff;
            border-radius: 2px;
        }

        .tab-button i {
            font-size: 16px;
        }

        .tab-button span {
            font-weight: 600;
        }

        .tab-content {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive design for tabs */
        @media (max-width: 768px) {
            .section-tabs {
                flex-direction: column;
                padding: 0;
                margin: 20px 0;
            }

            .tab-button {
                border-radius: 0;
                border-bottom: 1px solid #e9ecef;
                justify-content: flex-start;
                padding: 16px 20px;
                min-height: auto;
            }

            .tab-button:first-child {
                border-top-left-radius: 12px;
                border-top-right-radius: 12px;
            }

            .tab-button:last-child {
                border-bottom: none;
                border-bottom-left-radius: 12px;
                border-bottom-right-radius: 12px;
            }

            .tab-button.active::after {
                display: none;
            }

            .tab-content {
                padding: 20px;
                margin: 10px 0;
            }
        }

        @media (max-width: 480px) {
            .tab-button span {
                font-size: 13px;
            }

            .tab-button i {
                font-size: 14px;
            }

            .tab-content {
                padding: 15px;
            }
        }

        /* Keyboard shortcuts info */
        .keyboard-shortcuts-info {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
            animation: slideDown 0.3s ease-out;
        }

        .shortcuts-content {
            display: flex;
            align-items: center;
            gap: 12px;
            justify-content: space-between;
        }

        .shortcuts-content i.fa-keyboard {
            font-size: 18px;
        }

        .shortcuts-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        .shortcuts-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .standings-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .standings-table th,
        .standings-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .standings-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .rank-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            font-weight: 700;
            color: white;
        }
        
        .rank-1 { background: #ffd700; color: #333; }
        .rank-2 { background: #c0c0c0; color: #333; }
        .rank-3 { background: #cd7f32; color: white; }
        .rank-other { background: #6c757d; }
        
        .department-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        /* Enhanced Sport Card Styles */
        .sports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        .sport-card {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .sport-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .sport-card-header {
            padding: 24px 24px 16px 24px;
            border-bottom: 1px solid #f1f3f4;
        }

        .sport-title-section {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 16px;
        }

        .sport-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            flex-shrink: 0;
        }

        .sport-title-info {
            flex: 1;
            min-width: 0;
        }

        .sport-name {
            margin: 0 0 8px 0;
            font-size: 1.25rem;
            font-weight: 600;
            line-height: 1.3;
        }

        .sport-link {
            color: #2c3e50;
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .sport-link:hover {
            color: #667eea;
        }

        .sport-type-badge {
            margin-top: 4px;
        }

        .badge {
            display: inline-block;
            padding: 4px 10px;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 20px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-team, .badge-traditional-team-sports {
            background: #e3f2fd;
            color: #1565c0;
        }

        .badge-individual, .badge-traditional-individual-sports {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .badge-academic, .badge-academic-games {
            background: #fff3e0;
            color: #ef6c00;
        }

        .badge-judged, .badge-judged-competitions {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .badge-performance, .badge-performance-arts {
            background: #fce4ec;
            color: #c2185b;
        }

        .sport-actions {
            margin-top: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-action {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            flex: 1;
            min-width: 0;
            justify-content: center;
        }

        .btn-categories {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .btn-categories:hover {
            background: #c8e6c9;
            color: #1b5e20;
        }

        .btn-manage {
            background: #e3f2fd;
            color: #1565c0;
        }

        .btn-manage:hover {
            background: #bbdefb;
            color: #0d47a1;
        }

        .btn-remove {
            background: #ffebee;
            color: #c62828;
        }

        .btn-remove:hover {
            background: #ffcdd2;
            color: #b71c1c;
        }

        .sport-card-body {
            padding: 0 24px 24px 24px;
        }

        .sport-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-bottom: 20px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
            transition: background-color 0.2s ease;
        }

        .stat-item:hover {
            background: #e9ecef;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            flex-shrink: 0;
        }

        .stat-content {
            flex: 1;
            min-width: 0;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            line-height: 1.2;
        }

        .stat-label {
            font-size: 0.75rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 2px;
        }

        .sport-format {
            margin-bottom: 16px;
            padding: 12px 16px;
            background: #f1f3f4;
            border-radius: 10px;
        }

        .format-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .format-info i {
            color: #667eea;
        }

        .format-label {
            color: #6c757d;
            font-weight: 500;
        }

        .format-value {
            color: #2c3e50;
            font-weight: 600;
        }

        .sport-deadline {
            padding: 16px;
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-radius: 12px;
            border-left: 4px solid #ffc107;
        }

        .deadline-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .deadline-content i {
            color: #856404;
            font-size: 18px;
        }

        .deadline-info {
            flex: 1;
        }

        .deadline-label {
            display: block;
            font-size: 0.75rem;
            color: #856404;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 2px;
        }

        .deadline-date {
            font-size: 0.9rem;
            color: #856404;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sports-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .sport-card-header {
                padding: 20px 20px 12px 20px;
            }

            .sport-card-body {
                padding: 0 20px 20px 20px;
            }

            .sport-title-section {
                gap: 12px;
                margin-bottom: 12px;
            }

            .sport-icon {
                width: 40px;
                height: 40px;
                font-size: 18px;
            }

            .sport-name {
                font-size: 1.1rem;
            }

            .action-buttons {
                flex-direction: column;
                gap: 8px;
            }

            .btn-action {
                justify-content: center;
            }

            .btn-text {
                display: inline;
            }

            .sport-stats {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .stat-item {
                padding: 12px;
            }

            .stat-icon {
                width: 36px;
                height: 36px;
                font-size: 14px;
            }

            .stat-number {
                font-size: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .btn-text {
                display: none;
            }

            .btn-action {
                padding: 10px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <a href="events.php"><i class="fas fa-calendar-alt"></i> Events</a>
                    </div>
                    <div class="breadcrumb-separator">/</div>
                    <div class="breadcrumb-item">
                        <span>Manage Event</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <span class="admin-name">Welcome, <?php echo htmlspecialchars($current_admin['username']); ?></span>
                <a href="logout.php" class="btn btn-outline">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </header>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Event Header -->
            <div class="event-header">
                <h1 class="event-title"><?php echo htmlspecialchars($event['name']); ?></h1>
                <p><?php echo htmlspecialchars($event['description'] ?? 'No description available'); ?></p>
                
                <div class="event-meta">
                    <div class="meta-item">
                        <i class="fas fa-calendar"></i>
                        <span><?php echo date('M j, Y', strtotime($event['start_date'])); ?> - <?php echo date('M j, Y', strtotime($event['end_date'])); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span><?php echo htmlspecialchars($event['location'] ?? 'Location TBD'); ?></span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-info-circle"></i>
                        <span class="status-badge status-<?php echo $event['status']; ?>">
                            <?php echo ucfirst($event['status']); ?>
                        </span>
                    </div>
                </div>
            </div>

            <!-- Progress Section -->
            <div class="progress-section">
                <h3><i class="fas fa-chart-line"></i> Event Progress</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo $event_progress; ?>%"></div>
                </div>
                <p><?php echo $event_progress; ?>% Complete (<?php echo $completed_matches; ?>/<?php echo $total_matches; ?> matches finished)</p>
            </div>

            <!-- Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_sports; ?></div>
                    <div class="stat-label">Sports</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($departments); ?></div>
                    <div class="stat-label">Departments</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $total_matches; ?></div>
                    <div class="stat-label">Total Matches</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $completed_matches; ?></div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>

            <!-- Section Tabs -->
            <div class="section-tabs">
                <button class="tab-button active" data-tab="standings" onclick="switchTab('standings')"
                        title="View current event standings and rankings (Ctrl+1)">
                    <i class="fas fa-trophy"></i>
                    <span>Standings</span>
                </button>
                <button class="tab-button" data-tab="sports" onclick="switchTab('sports')"
                        title="Manage sports for this event (Ctrl+2)">
                    <i class="fas fa-futbol"></i>
                    <span>Sports Management</span>
                </button>
                <button class="tab-button" data-tab="registrations" onclick="switchTab('registrations')"
                        title="Manage department registrations (Ctrl+3)">
                    <i class="fas fa-users"></i>
                    <span>Registrations</span>
                </button>
                <button class="tab-button" data-tab="matches" onclick="switchTab('matches')"
                        title="View recent completed matches (Ctrl+4)">
                    <i class="fas fa-calendar-check"></i>
                    <span>Recent Matches</span>
                </button>
            </div>

            <!-- IMMEDIATE SCRIPT - NO DELAYS -->
            <script>
                // Define function immediately after buttons
                function switchTab(tabName) {
                    console.log('switchTab called:', tabName);

                    // Hide all tabs
                    var tabs = document.querySelectorAll('.tab-content');
                    for (var i = 0; i < tabs.length; i++) {
                        tabs[i].classList.remove('active');
                    }

                    // Remove active from all buttons
                    var buttons = document.querySelectorAll('.tab-button');
                    for (var i = 0; i < buttons.length; i++) {
                        buttons[i].classList.remove('active');
                    }

                    // Show selected tab
                    var targetTab = document.getElementById(tabName + '-tab');
                    if (targetTab) {
                        targetTab.classList.add('active');
                    }

                    // Activate clicked button
                    var targetButton = document.querySelector('[data-tab="' + tabName + '"]');
                    if (targetButton) {
                        targetButton.classList.add('active');
                    }

                    console.log('Tab switched to:', tabName);
                }

                // Test function
                function testTabs() {
                    console.log('Testing tabs...');
                    switchTab('sports');
                }

                // Add keyboard shortcuts
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey) {
                        switch(e.key) {
                            case '1':
                                e.preventDefault();
                                switchTab('standings');
                                break;
                            case '2':
                                e.preventDefault();
                                switchTab('sports');
                                break;
                            case '3':
                                e.preventDefault();
                                switchTab('registrations');
                                break;
                            case '4':
                                e.preventDefault();
                                switchTab('matches');
                                break;
                        }
                    }
                });

                console.log('Tab functions and keyboard shortcuts defined');
            </script>

            <!-- Debug Test Buttons -->
            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
                <strong>Debug Controls:</strong>
                <button onclick="switchTab('standings')" class="btn btn-sm btn-secondary">Test Standings</button>
                <button onclick="switchTab('sports')" class="btn btn-sm btn-secondary">Test Sports</button>
                <button onclick="switchTab('registrations')" class="btn btn-sm btn-secondary">Test Registrations</button>
                <button onclick="switchTab('matches')" class="btn btn-sm btn-secondary">Test Matches</button>
                <button onclick="testTabs()" class="btn btn-sm btn-primary">Test Function</button>
                <br><br>
                <button onclick="alert('Basic JavaScript works!')" class="btn btn-sm btn-info">Test Basic JS</button>
                <button onclick="console.log('switchTab type:', typeof switchTab)" class="btn btn-sm btn-warning">Check Function</button>
            </div>

            <!-- DEBUG: HTML structure checkpoint -->
            <script>
                console.log('=== HTML STRUCTURE LOADED ===');
                console.log('Tab buttons found:', document.querySelectorAll('.tab-button').length);
                console.log('Tab contents found:', document.querySelectorAll('.tab-content').length);
            </script>

            <!-- Keyboard Shortcuts Info -->
            <div class="keyboard-shortcuts-info" id="keyboardShortcutsInfo" style="display: none;">
                <div class="shortcuts-content">
                    <i class="fas fa-keyboard"></i>
                    <span>Tip: Use Ctrl+1-4 to quickly switch between tabs</span>
                    <button onclick="hideKeyboardShortcuts()" class="shortcuts-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <!-- Standings Tab -->
            <div id="standings-tab" class="tab-content active">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3><i class="fas fa-trophy"></i> Current Standings</h3>
                    <div style="display: flex; gap: 10px;">
                        <button class="btn btn-sm btn-success" onclick="exportResults()" title="Export results">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <?php if ($event['status'] === 'completed'): ?>
                            <button class="btn btn-sm btn-warning" onclick="announceWinner()" title="Announce winner">
                                <i class="fas fa-bullhorn"></i> Announce Winner
                            </button>
                        <?php endif; ?>
                        <button class="btn btn-sm btn-outline" onclick="refreshStandings()" title="Refresh standings">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>

                <?php if (!empty($standings) && $event['status'] === 'completed'): ?>
                    <!-- Winner Announcement Section -->
                    <div class="winner-announcement" style="background: linear-gradient(135deg, #ffd700, #ffed4e); padding: 25px; border-radius: 12px; margin-bottom: 25px; text-align: center; box-shadow: 0 8px 32px rgba(255,215,0,0.3);">
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; margin-bottom: 15px;">
                            <i class="fas fa-crown" style="font-size: 2rem; color: #b8860b;"></i>
                            <h2 style="margin: 0; color: #b8860b; font-weight: 700;">Event Champion</h2>
                            <i class="fas fa-crown" style="font-size: 2rem; color: #b8860b;"></i>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                            <span class="department-color" style="background-color: <?php echo $standings[0]['color_code'] ?? '#6c757d'; ?>; width: 30px; height: 30px;"></span>
                            <div>
                                <h3 style="margin: 0; color: #b8860b; font-size: 1.8rem;"><?php echo htmlspecialchars($standings[0]['department_name']); ?></h3>
                                <p style="margin: 5px 0 0 0; color: #8b7355; font-weight: 600;">
                                    <?php echo $standings[0]['total_points']; ?> points • <?php echo $standings[0]['matches_won']; ?> wins • <?php echo $standings[0]['win_rate']; ?>% win rate
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Podium Section -->
                    <?php if (count($standings) >= 3): ?>
                        <div class="podium-section" style="background: white; padding: 25px; border-radius: 12px; margin-bottom: 25px; box-shadow: 0 4px 16px rgba(0,0,0,0.1);">
                            <h4 style="text-align: center; margin-bottom: 20px; color: #2c3e50;"><i class="fas fa-medal"></i> Top 3 Departments</h4>
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; text-align: center;">
                                <?php for ($i = 0; $i < min(3, count($standings)); $i++): ?>
                                    <?php $dept = $standings[$i]; ?>
                                    <div class="podium-place" style="padding: 20px; border-radius: 12px; <?php echo $i === 0 ? 'background: linear-gradient(135deg, #ffd700, #ffed4e);' : ($i === 1 ? 'background: linear-gradient(135deg, #c0c0c0, #e8e8e8);' : 'background: linear-gradient(135deg, #cd7f32, #daa520);'); ?>">
                                        <div style="font-size: 2rem; margin-bottom: 10px;">
                                            <?php echo $i === 0 ? '🥇' : ($i === 1 ? '🥈' : '🥉'); ?>
                                        </div>
                                        <div style="display: flex; align-items: center; justify-content: center; gap: 8px; margin-bottom: 10px;">
                                            <span class="department-color" style="background-color: <?php echo $dept['color_code'] ?? '#6c757d'; ?>"></span>
                                            <strong><?php echo htmlspecialchars($dept['abbreviation']); ?></strong>
                                        </div>
                                        <div style="font-size: 1.5rem; font-weight: 700; margin-bottom: 5px;"><?php echo $dept['total_points']; ?></div>
                                        <div style="font-size: 0.9rem; opacity: 0.8;">points</div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php elseif (!empty($standings) && $event_progress > 0): ?>
                    <!-- Current Leader Section -->
                    <div class="current-leader" style="background: linear-gradient(135deg, #3498db, #5dade2); color: white; padding: 20px; border-radius: 12px; margin-bottom: 20px; text-align: center;">
                        <h4 style="margin: 0 0 10px 0;"><i class="fas fa-star"></i> Current Leader</h4>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 10px;">
                            <span class="department-color" style="background-color: <?php echo $standings[0]['color_code'] ?? '#6c757d'; ?>; border: 2px solid white;"></span>
                            <div>
                                <strong style="font-size: 1.2rem;"><?php echo htmlspecialchars($standings[0]['department_name']); ?></strong>
                                <div style="font-size: 0.9rem; opacity: 0.9;"><?php echo $standings[0]['total_points']; ?> points</div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($standings)): ?>
                    <table class="standings-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Department</th>
                                <th>Sports</th>
                                <th>Matches Won</th>
                                <th>Total Matches</th>
                                <th>Points</th>
                                <th>Win Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($standings as $index => $dept): ?>
                                <?php
                                $rank = $index + 1;
                                $win_rate = $dept['total_matches'] > 0 ? round(($dept['matches_won'] / $dept['total_matches']) * 100, 1) : 0;
                                $rank_class = $rank <= 3 ? "rank-$rank" : "rank-other";
                                ?>
                                <tr>
                                    <td>
                                        <span class="rank-badge <?php echo $rank_class; ?>">
                                            <?php echo $rank; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <span class="department-color" style="background-color: <?php echo $dept['color_code'] ?? '#6c757d'; ?>"></span>
                                            <div>
                                                <strong><?php echo htmlspecialchars($dept['department_name']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($dept['abbreviation']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo $dept['sports_participated']; ?></td>
                                    <td><?php echo $dept['matches_won']; ?></td>
                                    <td><?php echo $dept['total_matches']; ?></td>
                                    <td><strong><?php echo $dept['total_points']; ?></strong></td>
                                    <td><?php echo $win_rate; ?>%</td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-trophy fa-3x text-muted"></i>
                        <h4>No Standings Available</h4>
                        <p>Standings will appear once matches are completed.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sports Management Tab -->
            <div id="sports-tab" class="tab-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3><i class="fas fa-futbol"></i> Sports Management</h3>
                    <?php if (!empty($available_sports)): ?>
                        <button class="btn-modal-trigger" onclick="openModal('addSportModal')">
                            <i class="fas fa-plus"></i> Add Sport
                        </button>
                    <?php endif; ?>
                </div>

                <?php if (!empty($event_sports)): ?>
                    <div class="sports-grid">
                        <?php foreach ($event_sports as $sport): ?>
                            <div class="sport-card">
                                <div class="sport-card-header">
                                    <div class="sport-title-section">
                                        <div class="sport-icon">
                                            <i class="fas fa-trophy"></i>
                                        </div>
                                        <div class="sport-title-info">
                                            <h4 class="sport-name">
                                                <a href="sport-categories.php?event_id=<?php echo $event_id; ?>&sport_id=<?php echo $sport['sport_id']; ?>"
                                                   class="sport-link"
                                                   title="Manage <?php echo htmlspecialchars($sport['sport_name']); ?> Categories">
                                                    <?php echo htmlspecialchars($sport['sport_name']); ?>
                                                </a>
                                            </h4>
                                            <?php if (!empty($sport['sport_type_name'])): ?>
                                                <div class="sport-type-badge">
                                                    <span class="badge badge-<?php echo strtolower(str_replace(' ', '-', $sport['sport_type_category'] ?? 'traditional')); ?>">
                                                        <?php echo htmlspecialchars($sport['sport_type_name']); ?>
                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="sport-actions">
                                        <div class="action-buttons">
                                            <button class="btn btn-action btn-remove"
                                                    onclick="removeSport(<?php echo $sport['id']; ?>, '<?php echo htmlspecialchars($sport['sport_name']); ?>')"
                                                    title="Remove Sport">
                                                <i class="fas fa-trash"></i>
                                                <span class="btn-text">Remove</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="sport-card-body">
                                    <div class="sport-stats">
                                        <div class="stat-item">
                                            <div class="stat-icon">
                                                <i class="fas fa-users"></i>
                                            </div>
                                            <div class="stat-content">
                                                <div class="stat-number"><?php echo $sport['registered_teams']; ?></div>
                                                <div class="stat-label">Registered Teams</div>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-icon">
                                                <i class="fas fa-calendar-check"></i>
                                            </div>
                                            <div class="stat-content">
                                                <div class="stat-number"><?php echo $sport['total_matches']; ?></div>
                                                <div class="stat-label">Total Matches</div>
                                            </div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="stat-icon">
                                                <i class="fas fa-hashtag"></i>
                                            </div>
                                            <div class="stat-content">
                                                <div class="stat-number"><?php echo $sport['max_teams'] ?? '∞'; ?></div>
                                                <div class="stat-label">Max Teams</div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (!empty($sport['bracket_type'])): ?>
                                        <div class="sport-format">
                                            <div class="format-info">
                                                <i class="fas fa-sitemap"></i>
                                                <span class="format-label">Tournament Format:</span>
                                                <span class="format-value"><?php echo ucwords(str_replace('_', ' ', $sport['bracket_type'])); ?></span>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($sport['registration_deadline']): ?>
                                        <div class="sport-deadline">
                                            <div class="deadline-content">
                                                <i class="fas fa-clock"></i>
                                                <div class="deadline-info">
                                                    <span class="deadline-label">Registration Deadline</span>
                                                    <span class="deadline-date"><?php echo date('M j, Y g:i A', strtotime($sport['registration_deadline'])); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <i class="fas fa-futbol fa-3x text-muted"></i>
                        <h4>No Sports Added</h4>
                        <p>Add sports to this event to get started.</p>
                        <?php if (!empty($available_sports)): ?>
                            <button class="btn btn-primary" onclick="openModal('addSportModal')">
                                <i class="fas fa-plus"></i> Add First Sport
                            </button>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Registrations Tab -->
            <div id="registrations-tab" class="tab-content">
                <?php
                // Get unified department registrations for this event
                require_once '../includes/department_registration.php';
                $unified_registrations = getEventDepartmentRegistrations($conn, $event_id);
                $registration_stats = getDepartmentRegistrationStats($conn, $event_id);
                ?>

                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3><i class="fas fa-users"></i> Department Registrations</h3>
                    <button class="btn btn-primary" onclick="openModal('registerDepartmentModal')">
                        <i class="fas fa-plus"></i> Register Department
                    </button>
                </div>

                <!-- Unified Registration Info -->
                <div class="alert alert-info" style="margin-bottom: 20px; padding: 15px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-info-circle" style="color: #2196f3; font-size: 1.2rem;"></i>
                        <div>
                            <strong style="color: #1976d2;">Unified Registration System</strong>
                            <p style="margin: 5px 0 0 0; color: #1976d2; font-size: 0.9rem;">
                                Departments register once for the entire event and automatically participate in ALL sports. No separate sport-by-sport registration required.
                            </p>
                        </div>
                    </div>
                </div>

                <?php if (!empty($registration_stats)): ?>
                <!-- Registration Statistics -->
                <div class="registration-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                        <div class="stat-number" style="font-size: 2em; font-weight: bold; color: #007bff;"><?php echo $registration_stats['total_departments'] ?? 0; ?></div>
                        <div class="stat-label" style="color: #666; margin-top: 5px;">Registered Departments</div>
                    </div>
                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                        <div class="stat-number" style="font-size: 2em; font-weight: bold; color: #28a745;"><?php echo count($event_sports); ?></div>
                        <div class="stat-label" style="color: #666; margin-top: 5px;">Sports in Event</div>
                    </div>
                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                        <div class="stat-number" style="font-size: 2em; font-weight: bold; color: #ffc107;"><?php echo $registration_stats['total_sport_participations'] ?? 0; ?></div>
                        <div class="stat-label" style="color: #666; margin-top: 5px;">Total Participations</div>
                    </div>
                    <div class="stat-card" style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                        <div class="stat-number" style="font-size: 2em; font-weight: bold; color: #17a2b8;"><?php echo $registration_stats['approved_departments'] ?? 0; ?></div>
                        <div class="stat-label" style="color: #666; margin-top: 5px;">Approved Departments</div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if (!empty($unified_registrations)): ?>
                <!-- Unified Department Registrations -->
                <div class="registrations-container">
                    <?php foreach ($unified_registrations as $registration): ?>
                        <div class="registration-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 20px;">
                            <div class="registration-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <div class="department-info" style="display: flex; align-items: center; gap: 15px;">
                                    <div class="department-badge" style="background-color: <?php echo $registration['color_code'] ?? '#007bff'; ?>; width: 50px; height: 50px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.2rem;">
                                        <?php echo strtoupper(substr($registration['abbreviation'] ?? $registration['department_name'], 0, 2)); ?>
                                    </div>
                                    <div>
                                        <h4 style="margin: 0; color: #2c3e50;"><?php echo htmlspecialchars($registration['department_name']); ?></h4>
                                        <div style="display: flex; gap: 15px; align-items: center; margin-top: 5px;">
                                            <span class="status-badge status-<?php echo $registration['status']; ?>" style="padding: 4px 8px; border-radius: 4px; font-size: 0.8rem; font-weight: 600;">
                                                <?php echo ucfirst($registration['status']); ?>
                                            </span>
                                            <span style="color: #666; font-size: 0.9em;">
                                                <i class="fas fa-calendar"></i> <?php echo date('M j, Y', strtotime($registration['registration_date'])); ?>
                                            </span>
                                            <span style="color: #666; font-size: 0.9em;">
                                                <i class="fas fa-trophy"></i> <?php echo $registration['sports_count']; ?> sports
                                            </span>
                                            <span style="color: #666; font-size: 0.9em;">
                                                <i class="fas fa-users"></i> <?php echo $registration['total_participants']; ?> participants
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="registration-actions">
                                    <button class="btn btn-sm btn-outline-primary" onclick="showSportParticipations(<?php echo $registration['id']; ?>)">
                                        <i class="fas fa-list"></i> View Sports
                                    </button>
                                    <span class="text-success" style="font-size: 0.9em; margin-left: 10px;">
                                        <i class="fas fa-check-circle"></i> Auto-participating in all event sports
                                    </span>
                                    <div class="dropdown" style="display: inline-block; margin-left: 10px;">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-toggle="dropdown">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <div class="dropdown-menu">
                                            <a class="dropdown-item" onclick="updateRegistrationStatus(<?php echo $registration['id']; ?>, 'approved')">
                                                <i class="fas fa-check"></i> Approve
                                            </a>
                                            <a class="dropdown-item" onclick="updateRegistrationStatus(<?php echo $registration['id']; ?>, 'rejected')">
                                                <i class="fas fa-times"></i> Reject
                                            </a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item text-danger" onclick="removeRegistration(<?php echo $registration['id']; ?>)">
                                                <i class="fas fa-trash"></i> Remove
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="sports-list" id="sports-list-<?php echo $registration['id']; ?>" style="display: none;">
                                <!-- Sports will be loaded dynamically -->
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                    <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <i class="fas fa-users fa-3x text-muted"></i>
                        <h4>No Department Registrations</h4>
                        <p>No departments have registered for this event yet. Departments will automatically participate in all sports when registered.</p>
                        <button class="btn btn-primary" onclick="openModal('registerDepartmentModal')">
                            <i class="fas fa-plus"></i> Register First Department
                        </button>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Matches Tab -->
            <div id="matches-tab" class="tab-content">
                <h3><i class="fas fa-calendar-check"></i> Recent Matches</h3>

                <?php if (!empty($recent_matches)): ?>
                    <div class="matches-list">
                        <?php foreach ($recent_matches as $match): ?>
                            <div class="match-card" style="background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.1); margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div class="match-info" style="flex: 1;">
                                        <div class="sport-name" style="font-size: 0.9rem; color: #6c757d; margin-bottom: 5px;">
                                            <?php echo htmlspecialchars($match['sport_name']); ?>
                                        </div>
                                        <div class="teams" style="display: flex; align-items: center; gap: 15px;">
                                            <div class="team" style="display: flex; align-items: center; gap: 8px;">
                                                <span class="department-color" style="background-color: <?php echo $match['team1_color'] ?? '#6c757d'; ?>"></span>
                                                <span style="font-weight: 600;"><?php echo htmlspecialchars($match['team1_name']); ?></span>
                                                <span style="color: #6c757d;">(<?php echo htmlspecialchars($match['team1_abbr']); ?>)</span>
                                            </div>
                                            <span style="color: #6c757d;">vs</span>
                                            <?php if ($match['team2_name']): ?>
                                                <div class="team" style="display: flex; align-items: center; gap: 8px;">
                                                    <span class="department-color" style="background-color: <?php echo $match['team2_color'] ?? '#6c757d'; ?>"></span>
                                                    <span style="font-weight: 600;"><?php echo htmlspecialchars($match['team2_name']); ?></span>
                                                    <span style="color: #6c757d;">(<?php echo htmlspecialchars($match['team2_abbr']); ?>)</span>
                                                </div>
                                            <?php else: ?>
                                                <span style="color: #6c757d; font-style: italic;">BYE</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="match-result" style="text-align: center; margin: 0 20px;">
                                        <?php if ($match['winner_name']): ?>
                                            <div style="font-size: 1.2rem; font-weight: 700; color: #28a745;">
                                                Winner: <?php echo htmlspecialchars($match['winner_abbr']); ?>
                                            </div>
                                            <?php if ($match['team1_score'] !== null && $match['team2_score'] !== null): ?>
                                                <div style="color: #6c757d; margin-top: 5px;">
                                                    <?php echo $match['team1_score']; ?> - <?php echo $match['team2_score']; ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <div style="color: #6c757d;">No winner recorded</div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="match-time" style="text-align: right; color: #6c757d; font-size: 0.9rem;">
                                        <?php echo date('M j, Y g:i A', strtotime($match['actual_end_time'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <a href="matches.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline">
                            <i class="fas fa-list"></i> View All Matches
                        </a>
                    </div>
                <?php else: ?>
                    <div class="empty-state" style="text-align: center; padding: 60px 20px; color: #6c757d;">
                        <i class="fas fa-calendar-check fa-3x text-muted"></i>
                        <h4>No Completed Matches</h4>
                        <p>Recent match results will appear here once matches are completed.</p>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Add Sport Modal -->
    <div id="addSportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">Add Sport to Event</h3>
                <button type="button" class="modal-close" onclick="closeModal('addSportModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addSportForm" class="modal-form" onsubmit="submitAddSport(event)">
                <div class="modal-body">
                    <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- Auto-Registration Info -->
                    <div class="alert alert-info" style="margin-bottom: 20px; padding: 15px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px;">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-info-circle" style="color: #2196f3; font-size: 1.2rem;"></i>
                            <div>
                                <strong style="color: #1976d2;">Automatic Department Registration</strong>
                                <p style="margin: 5px 0 0 0; color: #1976d2; font-size: 0.9rem;">
                                    All departments currently registered for this event will automatically be added as participants in this sport.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="sport_id">Sport:</label>
                        <select name="sport_id" id="sport_id" class="form-control" required onchange="loadBracketTypes()">
                            <option value="">Select a sport...</option>
                            <?php foreach ($available_sports as $sport): ?>
                                <option value="<?php echo $sport['id']; ?>"
                                        data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>"
                                        data-sport-type-name="<?php echo htmlspecialchars($sport['sport_type_name'] ?? ''); ?>">
                                    <?php echo htmlspecialchars($sport['name']); ?>
                                    <?php if ($sport['sport_type_name']): ?>
                                        (<?php echo htmlspecialchars($sport['sport_type_name']); ?>)
                                    <?php else: ?>
                                        (<?php echo ucfirst($sport['type']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="error-message">Please select a sport</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="tournament_format_id">Tournament Format:</label>
                        <select name="tournament_format_id" id="tournament_format_id" class="form-control" required>
                            <option value="">Select a sport first...</option>
                        </select>
                        <div class="error-message">Please select a tournament format</div>
                        <div class="format-info" id="format_info" style="margin-top: 8px; padding: 10px; background: #f8f9fa; border-radius: 6px; font-size: 0.9rem; color: #6c757d; display: none;">
                            <div class="format-description"></div>
                            <div class="format-requirements" style="margin-top: 5px; font-size: 0.85rem;"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="seeding_method">Seeding Method:</label>
                        <select name="seeding_method" id="seeding_method" class="form-control">
                            <option value="random">Random Seeding</option>
                            <option value="ranking">Ranking-Based Seeding</option>
                            <option value="manual">Manual Seeding</option>
                            <option value="hybrid">Hybrid Seeding (Top 4 by ranking, rest random)</option>
                        </select>
                        <div class="help-text" style="font-size: 0.85rem; color: #6c757d; margin-top: 4px;">
                            Choose how participants will be seeded in the tournament bracket
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label" for="max_teams">Maximum Teams (optional):</label>
                            <input type="number" name="max_teams" id="max_teams" class="form-control" min="2" max="32" placeholder="Leave empty for unlimited">
                            <div class="error-message">Please enter a valid number</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label" for="registration_deadline">Registration Deadline (optional):</label>
                            <input type="datetime-local" name="registration_deadline" id="registration_deadline" class="form-control">
                            <div class="error-message">Please enter a valid date</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addSportModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="addSportSubmitBtn">
                        <i class="fas fa-plus"></i> Add Sport
                    </button>
                </div>
            </form>
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
        </div>
    </div>

    <!-- Register Department Modal -->
    <div id="registerDepartmentModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">Register Department for Event</h3>
                <button type="button" class="modal-close" onclick="closeModal('registerDepartmentModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="registerDepartmentForm" class="modal-form" onsubmit="submitUnifiedRegistration(event)">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- Unified Registration Info -->
                    <div class="alert alert-info" style="margin-bottom: 20px; padding: 12px; background: #e3f2fd; border: 1px solid #2196f3; border-radius: 6px; font-size: 0.9rem;">
                        <i class="fas fa-info-circle" style="color: #2196f3; margin-right: 8px;"></i>
                        <strong>Unified Registration:</strong> This department will automatically participate in ALL sports in this event.
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="register_department_id">Department:</label>
                        <select name="department_id" id="register_department_id" class="form-control" required>
                            <option value="">Select a department...</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo $dept['id']; ?>">
                                    <?php echo htmlspecialchars($dept['name']); ?>
                                    (<?php echo htmlspecialchars($dept['abbreviation']); ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="error-message">Please select a department</div>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i> Contact information will be used from the department's existing records.
                        </small>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="register_status">Registration Status:</label>
                        <select name="status" id="register_status" class="form-control" required>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                        <div class="error-message">Please select a status</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="registration_notes">Notes (optional):</label>
                        <textarea name="notes" id="registration_notes" class="form-control" rows="3" placeholder="Additional notes about this registration..."></textarea>
                    </div>

                    <!-- Sports Preview -->
                    <div class="sports-preview" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                        <h5 style="margin: 0 0 10px 0; color: #495057;">
                            <i class="fas fa-trophy"></i> Sports in this Event (<?php echo count($event_sports); ?>)
                        </h5>
                        <div style="display: flex; flex-wrap: wrap; gap: 8px;">
                            <?php foreach ($event_sports as $sport): ?>
                                <span class="badge badge-primary" style="padding: 6px 10px; background: #007bff; color: white; border-radius: 4px; font-size: 0.8rem;">
                                    <?php echo htmlspecialchars($sport['sport_name']); ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                        <p style="margin: 10px 0 0 0; font-size: 0.85rem; color: #6c757d;">
                            The department will automatically participate in all these sports.
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('registerDepartmentModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="registerDepartmentSubmitBtn">
                        <i class="fas fa-user-plus"></i> Register Department
                    </button>
                </div>
            </form>
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
        </div>
    </div>

    <!-- Simple tab system already defined above -->

    <style>
        /* Enhanced Modal Styles for Event Management */
        .modal {
            display: none;
            position: fixed;
            z-index: 1050;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(3px);
            animation: fadeIn 0.3s ease;
        }

        .modal.show {
            display: flex !important;
            align-items: center;
            justify-content: center;
        }

        .modal-dialog {
            background: white;
            border-radius: 12px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
            max-width: 650px;
            width: 95%;
            max-height: 90vh;
            overflow: hidden;
            transform: scale(0.9);
            transition: transform 0.3s ease;
            position: relative;
            margin: 20px;
        }

        .modal.show .modal-dialog {
            transform: scale(1);
        }

        .modal-header {
            padding: 24px 28px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        .modal-title {
            margin: 0;
            font-size: 1.375rem;
            font-weight: 600;
            color: #1f2937;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            color: #6b7280;
            cursor: pointer;
            padding: 8px;
            border-radius: 6px;
            transition: all 0.2s ease;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            background: #f3f4f6;
            color: #374151;
            transform: scale(1.1);
        }

        .modal-body {
            padding: 28px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 20px 28px 24px;
            border-top: 1px solid #e5e7eb;
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            background: #f8fafc;
        }

        .modal-form .form-group {
            margin-bottom: 24px;
        }

        .modal-form .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .modal-form .form-row.single {
            grid-template-columns: 1fr;
        }

        .modal-form .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }

        .modal-form .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
        }

        .modal-form .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .modal-form .form-control.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .modal-form .error-message {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 6px;
            display: none;
        }

        .modal-form .error-message.show {
            display: block;
        }

        .modal-loading {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            z-index: 10;
            align-items: center;
            justify-content: center;
        }

        .modal-loading.show {
            display: flex;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive Modal Design */
        @media (max-width: 768px) {
            .modal-dialog {
                width: 95%;
                max-height: 95vh;
                margin: 10px;
                max-width: none;
            }

            .modal-form .form-row {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding-left: 20px;
                padding-right: 20px;
            }

            .modal-body {
                padding-top: 20px;
                padding-bottom: 20px;
            }

            .modal-title {
                font-size: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .modal-dialog {
                width: 98%;
                margin: 5px;
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding-left: 16px;
                padding-right: 16px;
            }

            .modal-footer {
                flex-direction: column;
            }

            .modal-footer .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>

    <script>
        // Make sure showTab is globally available
        window.showTab = function(tabName, clickedButton) {
            console.log('=== showTab called ===', tabName, clickedButton);

            try {
                // Hide all tab contents
                const allContents = document.querySelectorAll('.tab-content');
                console.log('Found tab contents:', allContents.length);
                allContents.forEach(content => {
                    content.classList.remove('active');
                    console.log('Hiding:', content.id);
                });

                // Remove active class from all tab buttons
                const allButtons = document.querySelectorAll('.tab-button');
                console.log('Found tab buttons:', allButtons.length);
                allButtons.forEach(button => {
                    button.classList.remove('active');
                });

                // Show selected tab content
                const targetTab = document.getElementById(tabName + '-tab');
                if (targetTab) {
                    targetTab.classList.add('active');
                    console.log('SUCCESS: Showing tab:', targetTab.id);
                } else {
                    console.error('ERROR: Tab not found:', tabName + '-tab');
                    return false;
                }

                // Add active class to clicked button
                if (clickedButton) {
                    clickedButton.classList.add('active');
                    console.log('SUCCESS: Activated button');
                } else {
                    // Fallback: find the button by data-tab attribute
                    const button = document.querySelector(`[data-tab="${tabName}"]`);
                    if (button) {
                        button.classList.add('active');
                        console.log('SUCCESS: Found and activated button by data-tab');
                    }
                }

                // Save to localStorage
                localStorage.setItem('activeEventTab', tabName);
                console.log('SUCCESS: Tab switch completed');
                return true;

            } catch (error) {
                console.error('ERROR in showTab:', error);
                return false;
            }
        };

        // Also create a legacy alias
        function showTab(tabName, clickedButton) {
            return window.showTab(tabName, clickedButton);
        }

        console.log('=== showTab function defined ===', typeof window.showTab);

        // Initialize tabs when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM ready, initializing tabs');

            // Restore saved tab or use default
            const savedTab = localStorage.getItem('activeEventTab');
            if (savedTab && document.getElementById(savedTab + '-tab')) {
                console.log('Restoring saved tab:', savedTab);
                const button = document.querySelector(`[data-tab="${savedTab}"]`);
                showTab(savedTab, button);
            } else {
                console.log('Using default tab: standings');
                const button = document.querySelector(`[data-tab="standings"]`);
                if (button) {
                    showTab('standings', button);
                }
            }

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key >= '1' && e.key <= '4') {
                    e.preventDefault();
                    const tabMap = {
                        '1': 'standings',
                        '2': 'sports',
                        '3': 'registrations',
                        '4': 'matches'
                    };
                    const tabName = tabMap[e.key];
                    if (tabName) {
                        const button = document.querySelector(`[data-tab="${tabName}"]`);
                        showTab(tabName, button);
                    }
                }
            });
        });

        // Test functions for debugging - GLOBAL SCOPE
        window.testTabClick = function(tabName) {
            console.log('=== testTabClick called ===', tabName);
            try {
                const button = document.querySelector(`[data-tab="${tabName}"]`);
                console.log('Found button:', button);
                return window.showTab(tabName, button);
            } catch (error) {
                console.error('ERROR in testTabClick:', error);
                return false;
            }
        };

        window.testAllTabs = function() {
            console.log('=== testAllTabs called ===');
            try {
                const tabs = ['standings', 'sports', 'registrations', 'matches'];
                let index = 0;

                function testNext() {
                    if (index < tabs.length) {
                        console.log(`Testing tab ${index + 1}: ${tabs[index]}`);
                        const button = document.querySelector(`[data-tab="${tabs[index]}"]`);
                        window.showTab(tabs[index], button);
                        index++;
                        setTimeout(testNext, 1000);
                    } else {
                        console.log('All tabs tested successfully');
                    }
                }

                testNext();
            } catch (error) {
                console.error('ERROR in testAllTabs:', error);
            }
        };

        console.log('=== Test functions defined ===', typeof window.testTabClick, typeof window.testAllTabs);

        // Test all tabs after page loads
        window.addEventListener('load', function() {
            console.log('Page fully loaded, running tab test...');
            setTimeout(() => {
                console.log('Testing tab functionality...');
                window.testTabClick('standings');
            }, 1000);
        });

        // Keyboard shortcuts helper functions
        function showKeyboardShortcuts() {
            const info = document.getElementById('keyboardShortcutsInfo');
            if (info) {
                info.style.display = 'block';
                setTimeout(() => {
                    hideKeyboardShortcuts();
                }, 5000); // Auto-hide after 5 seconds
            }
        }

        function hideKeyboardShortcuts() {
            const info = document.getElementById('keyboardShortcutsInfo');
            if (info) {
                info.style.display = 'none';
            }
        }

        // Sport management functions
        function removeSport(sportId, sportName) {
            if (confirm(`Are you sure you want to remove "${sportName}" from this event? This will also remove all registrations and matches for this sport.`)) {
                const formData = new FormData();
                formData.append('action', 'remove_sport');
                formData.append('event_sport_id', sportId);
                formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

                fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error removing sport');
                });
            }
        }

        function registerDepartment(eventSportId) {
            document.getElementById('register_event_sport_id').value = eventSportId;
            openModal('registerDepartmentModal');
        }

        // Load bracket types based on selected sport
        function loadBracketTypes() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const formatInfo = document.getElementById('format_info');
            const seedingSelect = document.getElementById('seeding_method');

            const sportId = sportSelect.value;

            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            formatInfo.style.display = 'none';

            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                return;
            }

            // Get sport type from the selected option
            const selectedSportOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedSportOption.dataset.type || 'traditional';

            // Fetch tournament formats from database
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => response.json())
            .then(data => {
                formatSelect.innerHTML = '<option value="">Select tournament format...</option>';

                if (data.success && data.formats) {
                    data.formats.forEach(format => {
                        const option = document.createElement('option');
                        option.value = format.id; // Use database ID instead of code
                        option.textContent = format.name;
                        option.dataset.description = format.description;
                        option.dataset.minParticipants = format.min_participants;
                        option.dataset.maxParticipants = format.max_participants || 'Unlimited';
                        option.dataset.seedingRequired = format.requires_seeding;
                        option.dataset.advancementType = format.advancement_type;
                        formatSelect.appendChild(option);
                    });
                } else {
                    formatSelect.innerHTML = '<option value="">No formats available</option>';
                }

                // Set default seeding method based on sport type
                const defaultSeeding = getDefaultSeeding(sportType);
                if (defaultSeeding && seedingSelect) {
                    seedingSelect.value = defaultSeeding;
                }
            })
            .catch(error => {
                console.error('Error loading tournament formats:', error);
                formatSelect.innerHTML = '<option value="">Error loading formats</option>';
            });
        }

        // Get sport-specific tournament formats
        function getSportSpecificFormats(sportType) {
            const formatsByType = {
                'traditional': [
                    {
                        code: 'single_elimination',
                        name: 'Single Elimination',
                        description: 'Knockout tournament where teams/participants are eliminated after one loss. Fast-paced format with clear progression to finals.',
                        min_participants: 2,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'elimination'
                    },
                    {
                        code: 'double_elimination',
                        name: 'Double Elimination',
                        description: 'Two-bracket system with winner\'s and loser\'s brackets. Teams get a second chance after their first loss.',
                        min_participants: 3,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'elimination'
                    },
                    {
                        code: 'round_robin',
                        name: 'Round Robin',
                        description: 'Every team/participant plays every other team/participant once. Best overall record wins.',
                        min_participants: 3,
                        max_participants: 16,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'multi_stage',
                        name: 'Multi-Stage Tournament',
                        description: 'Group stage round robin followed by single elimination playoffs. Combines fairness of round robin with excitement of knockouts.',
                        min_participants: 8,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'hybrid'
                    }
                ],
                'judged': [
                    {
                        code: 'judged_rounds',
                        name: 'Judged Rounds',
                        description: 'Multiple judged rounds (preliminary, semifinal, final) with scoring criteria. Participants advance based on judge scores.',
                        min_participants: 3,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'talent_showcase',
                        name: 'Talent Showcase',
                        description: 'Multiple performance rounds with audience and judge voting. Combines technical scoring with audience appeal.',
                        min_participants: 3,
                        max_participants: 50,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'performance_competition',
                        name: 'Performance Competition',
                        description: 'Elimination-style competition with performance criteria. Participants eliminated based on performance scores.',
                        min_participants: 4,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'elimination'
                    }
                ],
                'performance': [
                    {
                        code: 'performance_showcase',
                        name: 'Performance Showcase',
                        description: 'Multi-round showcase format with audition, semifinal, and final rounds. Emphasizes artistic presentation.',
                        min_participants: 3,
                        max_participants: 50,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'artistic_judging',
                        name: 'Artistic Judging',
                        description: 'Single round with multiple judging criteria (technique, creativity, presentation). Best overall artistic score wins.',
                        min_participants: 2,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'talent_showcase',
                        name: 'Talent Showcase',
                        description: 'Showcase format with multiple performance rounds and audience voting. Great for variety shows and talent competitions.',
                        min_participants: 3,
                        max_participants: 50,
                        requires_seeding: false,
                        advancement_type: 'points'
                    }
                ],
                'academic': [
                    {
                        code: 'swiss_system',
                        name: 'Swiss System',
                        description: 'Pairing based on current standings without elimination. Participants with similar records compete against each other.',
                        min_participants: 4,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'knockout_rounds',
                        name: 'Knockout Rounds',
                        description: 'Academic elimination tournament with question pools and time limits. Single elimination format for knowledge competitions.',
                        min_participants: 4,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'elimination'
                    },
                    {
                        code: 'round_robin',
                        name: 'Academic Round Robin',
                        description: 'Every participant competes against every other participant. Best overall academic performance wins.',
                        min_participants: 3,
                        max_participants: 12,
                        requires_seeding: false,
                        advancement_type: 'points'
                    }
                ]
            };

            // Return formats for the sport type, or traditional as default
            return formatsByType[sportType] || formatsByType['traditional'];
        }

        // Get default seeding method for sport type
        function getDefaultSeeding(sportTypeCategory) {
            const defaults = {
                'traditional': 'random',
                'individual': 'ranking',
                'team': 'random',
                'academic': 'ranking',
                'judged': 'manual',
                'performance': 'manual'
            };
            return defaults[sportTypeCategory] || 'random';
        }

        // Show format information when format is selected
        function showFormatInfo() {
            const formatSelect = document.getElementById('tournament_format_id');
            const formatInfo = document.getElementById('format_info');
            const selectedOption = formatSelect.options[formatSelect.selectedIndex];

            if (selectedOption.value && selectedOption.dataset.description) {
                const description = selectedOption.dataset.description;
                const minParticipants = selectedOption.dataset.minParticipants;
                const maxParticipants = selectedOption.dataset.maxParticipants;
                const seedingRequired = selectedOption.dataset.seedingRequired;
                const advancementType = selectedOption.dataset.advancementType;

                formatInfo.querySelector('.format-description').textContent = description;

                let requirementsHtml = `<strong>Requirements:</strong> `;
                requirementsHtml += `Min ${minParticipants} participants`;
                if (maxParticipants && maxParticipants !== 'Unlimited') {
                    requirementsHtml += `, Max ${maxParticipants} participants`;
                }
                if (seedingRequired === 'true') {
                    requirementsHtml += `, Seeding required`;
                }
                if (advancementType) {
                    requirementsHtml += `, ${advancementType.charAt(0).toUpperCase() + advancementType.slice(1)} advancement`;
                }

                formatInfo.querySelector('.format-requirements').innerHTML = requirementsHtml;
                    `<strong>Requirements:</strong> ${minParticipants} - ${maxParticipants} participants`;
                formatInfo.style.display = 'block';
            } else {
                formatInfo.style.display = 'none';
            }
        }

        // Add event listener for format selection
        document.addEventListener('DOMContentLoaded', function() {
            const formatSelect = document.getElementById('tournament_format_id');
            if (formatSelect) {
                formatSelect.addEventListener('change', showFormatInfo);
            }
        });

        function submitAddSport(event) {
            event.preventDefault();

            const form = event.target;
            const modalId = 'addSportModal';

            // Validate form
            if (!validateModalForm(form)) {
                return;
            }

            // Show loading state
            showModalLoading(modalId);

            const formData = new FormData(form);
            formData.append('action', 'add_sport');

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideModalLoading(modalId);
                if (data.success) {
                    closeModal(modalId);
                    // Show success notification with department count
                    const departmentCount = data.departments_added || 0;
                    const message = departmentCount > 0
                        ? `Sport added successfully! ${departmentCount} departments automatically registered.`
                        : 'Sport added successfully!';
                    showNotification(message, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideModalLoading(modalId);
                console.error('Error:', error);
                showNotification('Error adding sport. Please try again.', 'error');
            });
        }

        // Unified Registration Function
        function submitUnifiedRegistration(event) {
            event.preventDefault();

            const form = event.target;
            const modalId = 'registerDepartmentModal';

            // Validate form
            if (!validateModalForm(form)) {
                return;
            }

            // Show loading state
            showModalLoading(modalId);

            const formData = new FormData(form);
            formData.append('action', 'register_department_unified');
            formData.append('event_id', <?php echo $event_id; ?>);

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideModalLoading(modalId);
                if (data.success) {
                    closeModal(modalId);
                    // Show success notification with details
                    const message = `Department registered successfully! Automatically added to ${data.sports_added || 0} sports.`;
                    showNotification(message, 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                hideModalLoading(modalId);
                console.error('Error:', error);
                showNotification('Error registering department. Please try again.', 'error');
            });
        }

        // Legacy function for backward compatibility
        function submitRegisterDepartment(event) {
            // Redirect to unified registration
            submitUnifiedRegistration(event);
        }

        // Show sport participations for a department
        function showSportParticipations(registrationId) {
            const listElement = document.getElementById(`sports-list-${registrationId}`);
            if (listElement.style.display === 'none') {
                // Load and show sports
                loadSportParticipations(registrationId);
                listElement.style.display = 'block';
            } else {
                listElement.style.display = 'none';
            }
        }

        // Load sport participations via AJAX
        function loadSportParticipations(registrationId) {
            const listElement = document.getElementById(`sports-list-${registrationId}`);
            listElement.innerHTML = '<div style="text-align: center; padding: 20px;"><i class="fas fa-spinner fa-spin"></i> Loading sports...</div>';

            const formData = new FormData();
            formData.append('action', 'get_sport_participations');
            formData.append('registration_id', registrationId);

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<div style="padding: 15px; background: #f8f9fa; border-radius: 6px; margin-top: 10px;">';
                    html += '<h6 style="margin: 0 0 10px 0; color: #495057;"><i class="fas fa-list"></i> Sport Participations</h6>';

                    if (data.participations && data.participations.length > 0) {
                        html += '<div style="display: grid; gap: 10px;">';
                        data.participations.forEach(participation => {
                            html += `
                                <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background: white; border-radius: 4px; border-left: 4px solid #007bff;">
                                    <div>
                                        <strong>${participation.sport_name}</strong>
                                        <div style="font-size: 0.8rem; color: #666; margin-top: 2px;">
                                            Status: <span class="status-badge status-${participation.status}">${participation.status}</span>
                                            ${participation.notes ? `• ${participation.notes}` : ''}
                                        </div>
                                    </div>
                                    <div style="font-size: 0.8rem; color: #666;">
                                        ${participation.participants_count || 0} participants
                                    </div>
                                </div>
                            `;
                        });
                        html += '</div>';
                    } else {
                        html += '<p style="color: #666; margin: 0;">No sport participations found.</p>';
                    }

                    html += '</div>';
                    listElement.innerHTML = html;
                } else {
                    listElement.innerHTML = '<div style="color: #dc3545; padding: 15px;">Error loading sports: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                listElement.innerHTML = '<div style="color: #dc3545; padding: 15px;">Error loading sports. Please try again.</div>';
            });
        }

        // Update registration status
        function updateRegistrationStatus(registrationId, newStatus) {
            if (!confirm(`Are you sure you want to change the status to "${newStatus}"?`)) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'update_registration_status');
            formData.append('registration_id', registrationId);
            formData.append('status', newStatus);

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(`Registration status updated to ${newStatus}`, 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error updating status. Please try again.', 'error');
            });
        }

        // Remove registration
        function removeRegistration(registrationId) {
            if (!confirm('Are you sure you want to remove this department registration? This will remove the department from ALL sports in this event.')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'remove_department_registration');
            formData.append('registration_id', registrationId);

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Department registration removed successfully', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error removing registration. Please try again.', 'error');
            });
        }

        function viewRegistration(registrationId) {
            // For now, redirect to a detailed view page
            window.open(`registrations.php?id=${registrationId}`, '_blank');
        }

        function editRegistration(registrationId) {
            // For now, redirect to edit page
            window.location.href = `registrations.php?edit=${registrationId}`;
        }

        function deleteRegistration(registrationId) {
            if (confirm('Are you sure you want to delete this registration? This will also remove all associated matches.')) {
                const formData = new FormData();
                formData.append('action', 'delete_registration');
                formData.append('registration_id', registrationId);

                fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error deleting registration');
                });
            }
        }

        // Live standings update functionality
        function updateStandings() {
            if (document.getElementById('standings-tab').classList.contains('active')) {
                fetch(`ajax/get-standings.php?event_id=<?php echo $event_id; ?>`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateStandingsTable(data.data.standings);
                        updateProgressSection(data.data.statistics);
                        console.log('Standings updated at:', data.data.last_updated);
                    }
                })
                .catch(error => console.error('Error refreshing standings:', error));
            }
        }

        function updateStandingsTable(standings) {
            const tbody = document.querySelector('.standings-table tbody');
            if (!tbody || !standings) return;

            tbody.innerHTML = '';

            standings.forEach((dept, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <span class="rank-badge ${dept.rank_class}">
                            ${dept.rank}
                        </span>
                    </td>
                    <td>
                        <div style="display: flex; align-items: center;">
                            <span class="department-color" style="background-color: ${dept.color_code || '#6c757d'}"></span>
                            <div>
                                <strong>${dept.department_name}</strong>
                                <br>
                                <small class="text-muted">${dept.abbreviation}</small>
                            </div>
                        </div>
                    </td>
                    <td>${dept.sports_participated}</td>
                    <td>${dept.matches_won}</td>
                    <td>${dept.total_matches}</td>
                    <td><strong>${dept.total_points}</strong></td>
                    <td>${dept.win_rate}%</td>
                `;
                tbody.appendChild(row);
            });
        }

        function updateProgressSection(stats) {
            const progressFill = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-section p');

            if (progressFill && stats) {
                progressFill.style.width = stats.progress_percentage + '%';
            }

            if (progressText && stats) {
                progressText.textContent = `${stats.progress_percentage}% Complete (${stats.total_matches_completed}/${stats.total_matches_played} matches finished)`;
            }
        }

        // Auto-refresh standings every 30 seconds
        setInterval(updateStandings, 30000);

        // Manual refresh button functionality
        function refreshStandings() {
            updateStandings();
        }

        // Export results functionality
        function exportResults() {
            const eventId = <?php echo $event_id; ?>;
            const eventName = '<?php echo addslashes($event['name']); ?>';

            // Create export data
            fetch(`ajax/get-standings.php?event_id=${eventId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    exportToCSV(data.data, eventName);
                } else {
                    alert('Error fetching data for export');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error exporting results');
            });
        }

        function exportToCSV(data, eventName) {
            const standings = data.standings;
            const stats = data.statistics;

            // Create CSV content
            let csvContent = `Event: ${eventName}\n`;
            csvContent += `Export Date: ${new Date().toLocaleString()}\n`;
            csvContent += `Total Departments: ${stats.total_departments}\n`;
            csvContent += `Matches Completed: ${stats.total_matches_completed}/${stats.total_matches_played}\n`;
            csvContent += `Event Progress: ${stats.progress_percentage}%\n\n`;

            csvContent += "Rank,Department,Abbreviation,Sports Participated,Matches Won,Total Matches,Points,Win Rate\n";

            standings.forEach(dept => {
                csvContent += `${dept.rank},"${dept.department_name}","${dept.abbreviation}",${dept.sports_participated},${dept.matches_won},${dept.total_matches},${dept.total_points},${dept.win_rate}%\n`;
            });

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${eventName.replace(/[^a-z0-9]/gi, '_')}_results_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Winner announcement functionality
        function announceWinner() {
            const eventId = <?php echo $event_id; ?>;

            if (confirm('Are you sure you want to announce the winner? This will mark the event as officially completed.')) {
                fetch('ajax/event-management.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=announce_winner&event_id=${eventId}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error announcing winner');
                });
            }
        }

        // Enhanced Modal Management Functions
        function openModal(modalId) {
            console.log('Opening modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                // Close any other open modals first
                const openModals = document.querySelectorAll('.modal.show');
                openModals.forEach(m => m.classList.remove('show'));

                // Show the requested modal
                modal.classList.add('show');
                document.body.style.overflow = 'hidden'; // Prevent background scrolling

                // Focus first input after animation
                setTimeout(() => {
                    const firstInput = modal.querySelector('input, select, textarea');
                    if (firstInput) {
                        firstInput.focus();
                    }
                }, 300);

                console.log('Modal opened successfully:', modalId);
            } else {
                console.error('Modal not found:', modalId);
            }
        }

        function closeModal(modalId) {
            console.log('Closing modal:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('show');
                document.body.style.overflow = ''; // Restore scrolling

                // Clear form if it exists
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                    // Clear error states
                    const errorElements = form.querySelectorAll('.error-message.show');
                    errorElements.forEach(el => el.classList.remove('show'));
                    const errorInputs = form.querySelectorAll('.form-control.error');
                    errorInputs.forEach(el => el.classList.remove('error'));
                }

                console.log('Modal closed successfully:', modalId);
            } else {
                console.error('Modal not found:', modalId);
            }
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal') && e.target.classList.contains('show')) {
                const modalId = e.target.id;
                if (modalId) {
                    closeModal(modalId);
                }
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    closeModal(openModal.id);
                }
            }
        });

        // Form validation helper
        function validateModalForm(form) {
            let isValid = true;
            const requiredFields = form.querySelectorAll('[required]');

            requiredFields.forEach(field => {
                const errorMsg = field.parentElement.querySelector('.error-message');

                if (!field.value.trim()) {
                    field.classList.add('error');
                    if (errorMsg) errorMsg.classList.add('show');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                    if (errorMsg) errorMsg.classList.remove('show');
                }
            });

            return isValid;
        }

        // Show loading state in modal
        function showModalLoading(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                const loading = modal.querySelector('.modal-loading');
                if (loading) loading.classList.add('show');
            }
        }

        // Hide loading state in modal
        function hideModalLoading(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                const loading = modal.querySelector('.modal-loading');
                if (loading) loading.classList.remove('show');
            }
        }

        // Notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(n => n.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Add styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 2000;
                min-width: 300px;
                max-width: 500px;
                padding: 16px;
                border-radius: 8px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.2);
                animation: slideInRight 0.3s ease;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        console.log('Modal management functions loaded successfully');
    </script>

    <style>
        /* Notification Styles */
        .notification {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .notification-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            margin-left: auto;
        }

        .notification-close:hover {
            background: rgba(255,255,255,0.2);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    </style>



    <!-- Include modal scripts AFTER our tab system -->
    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
