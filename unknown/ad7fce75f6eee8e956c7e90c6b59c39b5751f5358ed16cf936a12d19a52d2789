<?php
/**
 * Fix Tournament Formats Table Schema
 * This script will fix the missing sport_types column and ensure the table has the correct structure
 */

require_once '../config/database.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h2>Fixing Tournament Formats Table Schema</h2>";

try {
    // Check current table structure
    echo "<h3>Step 1: Checking Current Table Structure</h3>";
    $stmt = $conn->prepare("DESCRIBE tournament_formats");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<p>Current columns:</p>";
    echo "<ul>";
    $existing_columns = [];
    foreach ($columns as $column) {
        $existing_columns[] = $column['Field'];
        echo "<li><strong>{$column['Field']}</strong> - {$column['Type']}</li>";
    }
    echo "</ul>";
    
    // Check if sport_types column exists
    $has_sport_types = in_array('sport_types', $existing_columns);
    $has_min_participants = in_array('min_participants', $existing_columns);
    $has_max_participants = in_array('max_participants', $existing_columns);
    $has_created_at = in_array('created_at', $existing_columns);
    $has_updated_at = in_array('updated_at', $existing_columns);
    
    echo "<h3>Step 2: Adding Missing Columns</h3>";
    
    // Add missing columns
    if (!$has_sport_types) {
        echo "<p>Adding sport_types column...</p>";
        $conn->exec("ALTER TABLE tournament_formats ADD COLUMN sport_types VARCHAR(255) DEFAULT 'team,individual' AFTER description");
        echo "<p style='color: green;'>✓ Added sport_types column</p>";
    } else {
        echo "<p style='color: green;'>✓ sport_types column already exists</p>";
    }
    
    if (!$has_min_participants) {
        echo "<p>Adding min_participants column...</p>";
        $conn->exec("ALTER TABLE tournament_formats ADD COLUMN min_participants INT DEFAULT 2 AFTER sport_types");
        echo "<p style='color: green;'>✓ Added min_participants column</p>";
    } else {
        echo "<p style='color: green;'>✓ min_participants column already exists</p>";
    }
    
    if (!$has_max_participants) {
        echo "<p>Adding max_participants column...</p>";
        $conn->exec("ALTER TABLE tournament_formats ADD COLUMN max_participants INT NULL AFTER min_participants");
        echo "<p style='color: green;'>✓ Added max_participants column</p>";
    } else {
        echo "<p style='color: green;'>✓ max_participants column already exists</p>";
    }
    
    if (!$has_created_at) {
        echo "<p>Adding created_at column...</p>";
        $conn->exec("ALTER TABLE tournament_formats ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER max_participants");
        echo "<p style='color: green;'>✓ Added created_at column</p>";
    } else {
        echo "<p style='color: green;'>✓ created_at column already exists</p>";
    }
    
    if (!$has_updated_at) {
        echo "<p>Adding updated_at column...</p>";
        $conn->exec("ALTER TABLE tournament_formats ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
        echo "<p style='color: green;'>✓ Added updated_at column</p>";
    } else {
        echo "<p style='color: green;'>✓ updated_at column already exists</p>";
    }
    
    // Add indexes if they don't exist
    echo "<h3>Step 3: Adding Indexes</h3>";
    try {
        $conn->exec("CREATE INDEX idx_sport_types ON tournament_formats (sport_types)");
        echo "<p style='color: green;'>✓ Added sport_types index</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ sport_types index might already exist</p>";
    }
    
    try {
        $conn->exec("CREATE INDEX idx_code ON tournament_formats (code)");
        echo "<p style='color: green;'>✓ Added code index</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ code index might already exist</p>";
    }
    
    // Clear existing data and add fresh data
    echo "<h3>Step 4: Adding Tournament Format Data</h3>";
    $conn->exec("DELETE FROM tournament_formats");
    echo "<p>Cleared existing data</p>";
    
    $default_formats = [
        ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null],
        ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null],
        ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16],
        ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null],
        ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null],
        ['Knockout Rounds', 'knockout_rounds', 'Academic knockout competition with elimination rounds.', 'academic', 4, null],
        ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null],
        ['Talent Showcase', 'talent_showcase', 'Performance showcase with judged scoring.', 'judged,performance', 3, null],
        ['Performance Competition', 'performance_competition', 'Structured performance competition with multiple rounds.', 'performance', 3, null],
        ['Artistic Judging', 'artistic_judging', 'Artistic performance with panel judging.', 'performance', 3, null],
        ['Elimination Rounds', 'elimination_rounds', 'Progressive elimination rounds for individual competitors.', 'individual', 4, null],
        ['Time Trials', 'time_trials', 'Individual time-based competition format.', 'individual', 2, null]
    ];
    
    foreach ($default_formats as $format) {
        $stmt = $conn->prepare("
            INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($format);
        echo "<p style='color: green;'>✓ Added: {$format[0]} (Types: {$format[3]})</p>";
    }
    
    // Verify the final structure
    echo "<h3>Step 5: Final Verification</h3>";
    $stmt = $conn->prepare("DESCRIBE tournament_formats");
    $stmt->execute();
    $final_columns = $stmt->fetchAll();
    
    echo "<p><strong>Final table structure:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f5f5f5;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($final_columns as $column) {
        echo "<tr>";
        echo "<td><strong>{$column['Field']}</strong></td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check data count
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    
    echo "<p style='color: green; font-weight: bold; font-size: 1.2em;'>✅ SUCCESS! Tournament formats table now has {$count} records with proper schema.</p>";
    
    // Test a query
    echo "<h3>Step 6: Testing Query</h3>";
    $stmt = $conn->prepare("SELECT id, name, sport_types FROM tournament_formats WHERE sport_types LIKE '%individual%' LIMIT 3");
    $stmt->execute();
    $test_results = $stmt->fetchAll();
    
    echo "<p>Test query for 'individual' sport types:</p>";
    echo "<ul>";
    foreach ($test_results as $result) {
        echo "<li><strong>{$result['name']}</strong> (ID: {$result['id']}, Types: {$result['sport_types']})</li>";
    }
    echo "</ul>";
    
    echo "<p style='color: green; font-weight: bold; font-size: 1.2em;'>🎉 Schema fix complete! The tournament format dropdown should now work correctly.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f5f5f5; }
</style>
