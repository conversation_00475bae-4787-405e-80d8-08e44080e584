<?php
// Clean output buffer
if (ob_get_level()) {
    ob_clean();
}

require_once '../../config/database.php';
require_once '../../includes/auth.php';

// Ensure admin authentication
requireAdmin();

header('Content-Type: application/json');

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $sportId = $_GET['sport_id'] ?? 4;
    
    // Test basic query
    $stmt = $conn->prepare("SELECT * FROM sports WHERE id = ?");
    $stmt->execute([$sportId]);
    $sport = $stmt->fetch();
    
    if (!$sport) {
        throw new Exception("Sport not found");
    }
    
    // Test if tournament_manager exists
    if (file_exists('../../includes/tournament_manager.php')) {
        require_once '../../includes/tournament_manager.php';
        
        if (class_exists('TournamentManager')) {
            $tournamentManager = new TournamentManager($conn);
            $category = $tournamentManager->getSportTypeCategory($sportId);
            $formats = $tournamentManager->getAvailableFormats($category);
            
            echo json_encode([
                'success' => true,
                'sport' => $sport,
                'category' => $category,
                'formats_count' => count($formats),
                'formats' => array_slice($formats, 0, 3) // Just first 3 for testing
            ]);
        } else {
            throw new Exception("TournamentManager class not found");
        }
    } else {
        throw new Exception("tournament_manager.php file not found");
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
