<?php
/**
 * Comprehensive Tournament Schema Fix
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script performs a complete database schema fix for the tournament system:
 * 1. Creates all missing tournament tables
 * 2. Adds all missing columns to existing tables
 * 3. Sets up proper foreign key relationships
 * 4. Populates tournament formats
 * 5. Verifies the complete schema
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Comprehensive Tournament Schema Fix</h1>";
echo "<p>Performing complete database schema fix for tournament system...</p>";

try {
    $conn->beginTransaction();
    
    $changes_made = [];
    $errors = [];
    
    echo "<h2>🔍 Step 1: Create Tournament Formats Table</h2>";
    
    // Check if tournament_formats table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
    $stmt->execute();
    $formats_table_exists = $stmt->fetch();
    
    if (!$formats_table_exists) {
        echo "<p>Creating tournament_formats table...</p>";
        $sql = "CREATE TABLE tournament_formats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            code VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            sport_types VARCHAR(255) DEFAULT 'team,individual',
            min_participants INT DEFAULT 2,
            max_participants INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->exec($sql);
        $changes_made[] = "Created tournament_formats table";
        echo "<p style='color: green;'>✅ Created tournament_formats table</p>";
        
        // First add algorithm_class column if it doesn't exist
        echo "<p>Checking for algorithm_class column...</p>";
        $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'algorithm_class'");
        $stmt->execute();
        $algorithm_column_exists = $stmt->fetch();

        if (!$algorithm_column_exists) {
            echo "<p>Adding algorithm_class column...</p>";
            $conn->exec("ALTER TABLE tournament_formats ADD COLUMN algorithm_class VARCHAR(100) NULL AFTER description");
            $changes_made[] = "Added algorithm_class column to tournament_formats";
            echo "<p style='color: green;'>✅ Added algorithm_class column</p>";
        }

        // Insert basic tournament formats with algorithm classes
        echo "<p>Inserting basic tournament formats...</p>";
        $formats = [
            [7, 'Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'SingleEliminationAlgorithm', 'team,individual', 2, null],
            [8, 'Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'DoubleEliminationAlgorithm', 'team,individual', 3, null],
            [9, 'Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'RoundRobinAlgorithm', 'team,individual', 3, 16],
            [10, 'Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'SwissSystemAlgorithm', 'academic', 4, null],
            [11, 'Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'RoundRobinAlgorithm', 'judged,performance', 3, null]
        ];

        foreach ($formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (id, name, code, description, algorithm_class, sport_types, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                algorithm_class = VALUES(algorithm_class),
                description = VALUES(description),
                sport_types = VALUES(sport_types)
            ");
            $stmt->execute($format);
        }
        $changes_made[] = "Inserted " . count($formats) . " tournament formats";
        echo "<p style='color: green;'>✅ Inserted " . count($formats) . " tournament formats</p>";
        
    } else {
        echo "<p style='color: blue;'>ℹ tournament_formats table already exists</p>";
    }
    
    echo "<h2>🔍 Step 2: Create Tournament Structures Table</h2>";
    
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_structures'");
    $stmt->execute();
    $structures_table_exists = $stmt->fetch();
    
    if (!$structures_table_exists) {
        echo "<p>Creating tournament_structures table...</p>";
        $sql = "CREATE TABLE tournament_structures (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_sport_id INT NOT NULL,
            tournament_format_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
            participant_count INT DEFAULT 0,
            total_rounds INT DEFAULT 0,
            current_round INT DEFAULT 0,
            seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
            bracket_data JSON,
            advancement_rules JSON,
            scoring_config JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
            FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE RESTRICT
        )";
        $conn->exec($sql);
        $changes_made[] = "Created tournament_structures table";
        echo "<p style='color: green;'>✅ Created tournament_structures table</p>";
    } else {
        echo "<p style='color: blue;'>ℹ tournament_structures table already exists</p>";
    }
    
    echo "<h2>🔍 Step 3: Create Tournament Rounds Table</h2>";
    
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_rounds'");
    $stmt->execute();
    $rounds_table_exists = $stmt->fetch();
    
    if (!$rounds_table_exists) {
        echo "<p>Creating tournament_rounds table...</p>";
        $sql = "CREATE TABLE tournament_rounds (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tournament_structure_id INT NOT NULL,
            round_number INT NOT NULL,
            round_name VARCHAR(100) NOT NULL,
            round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
            status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
            start_date DATETIME,
            end_date DATETIME,
            matches_count INT DEFAULT 0,
            completed_matches INT DEFAULT 0,
            advancement_criteria JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
            UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number)
        )";
        $conn->exec($sql);
        $changes_made[] = "Created tournament_rounds table";
        echo "<p style='color: green;'>✅ Created tournament_rounds table</p>";
    } else {
        echo "<p style='color: blue;'>ℹ tournament_rounds table already exists</p>";
    }
    
    echo "<h2>🔍 Step 4: Create Tournament Participants Table</h2>";
    
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_participants'");
    $stmt->execute();
    $participants_table_exists = $stmt->fetch();
    
    if (!$participants_table_exists) {
        echo "<p>Creating tournament_participants table...</p>";
        $sql = "CREATE TABLE tournament_participants (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tournament_structure_id INT NOT NULL,
            registration_id INT NOT NULL,
            seed_number INT,
            group_assignment VARCHAR(10),
            current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
            points DECIMAL(10,2) DEFAULT 0,
            wins INT DEFAULT 0,
            losses INT DEFAULT 0,
            draws INT DEFAULT 0,
            performance_data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
            UNIQUE KEY unique_tournament_participant (tournament_structure_id, registration_id)
        )";
        $conn->exec($sql);
        $changes_made[] = "Created tournament_participants table";
        echo "<p style='color: green;'>✅ Created tournament_participants table</p>";
    } else {
        echo "<p style='color: blue;'>ℹ tournament_participants table already exists</p>";
    }
    
    echo "<h2>🔍 Step 5: Add Tournament Columns to Matches Table</h2>";
    
    // Check current matches table structure
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $matches_columns = $stmt->fetchAll();
    
    $existing_columns = [];
    foreach ($matches_columns as $column) {
        $existing_columns[] = $column['Field'];
    }
    
    // Define required tournament columns
    $required_columns = [
        'tournament_structure_id' => 'INT NULL',
        'tournament_round_id' => 'INT NULL', 
        'bracket_position' => 'VARCHAR(50) NULL',
        'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
    ];
    
    foreach ($required_columns as $column_name => $column_definition) {
        if (!in_array($column_name, $existing_columns)) {
            echo "<p>Adding {$column_name} column to matches table...</p>";
            $sql = "ALTER TABLE matches ADD COLUMN {$column_name} {$column_definition}";
            $conn->exec($sql);
            $changes_made[] = "Added {$column_name} column to matches table";
            echo "<p style='color: green;'>✅ Added {$column_name} column</p>";
        } else {
            echo "<p style='color: blue;'>ℹ {$column_name} column already exists</p>";
        }
    }
    
    echo "<h2>🔍 Step 6: Add Foreign Key Constraints</h2>";
    
    // Add foreign key constraints (ignore errors if they already exist)
    $foreign_keys = [
        'fk_matches_tournament_structure' => 'ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_structure FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE',
        'fk_matches_tournament_round' => 'ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_round FOREIGN KEY (tournament_round_id) REFERENCES tournament_rounds(id) ON DELETE CASCADE'
    ];
    
    foreach ($foreign_keys as $constraint_name => $sql) {
        try {
            echo "<p>Adding foreign key constraint: {$constraint_name}...</p>";
            $conn->exec($sql);
            $changes_made[] = "Added foreign key constraint: {$constraint_name}";
            echo "<p style='color: green;'>✅ Added foreign key constraint: {$constraint_name}</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ Foreign key constraint {$constraint_name} already exists or couldn't be added</p>";
        }
    }
    
    $conn->commit();
    
    echo "<h2>🎉 Schema Fix Complete!</h2>";
    
    if (!empty($changes_made)) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>✅ <strong>COMPREHENSIVE SCHEMA FIX SUCCESSFUL!</strong></h3>";
        echo "<p style='font-size: 18px;'>The following changes were made:</p>";
        echo "<ul>";
        foreach ($changes_made as $change) {
            echo "<li style='color: #155724; font-weight: bold;'>{$change}</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border: 3px solid #007bff; margin: 20px 0;'>";
        echo "<h3 style='color: #004085; margin-top: 0;'>ℹ <strong>SCHEMA ALREADY COMPLETE!</strong></h3>";
        echo "<p style='font-size: 18px;'>All required tables and columns already exist.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    $conn->rollBack();
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>SCHEMA FIX FAILED!</strong></h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>🔍 Step 7: Verify Complete Schema</h2>";

try {
    // Verify all tables exist
    $required_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants', 'matches'];
    $verification_passed = true;
    
    echo "<h3>Table Verification:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'><th>Table</th><th>Status</th><th>Row Count</th></tr>";
    
    foreach ($required_tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM {$table}");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<tr><td>{$table}</td><td style='color: #28a745;'>✅ EXISTS</td><td>{$count}</td></tr>";
        } else {
            echo "<tr><td>{$table}</td><td style='color: #dc3545;'>❌ MISSING</td><td>-</td></tr>";
            $verification_passed = false;
        }
    }
    echo "</table>";
    
    // Verify matches table has tournament columns
    echo "<h3>Matches Table Tournament Columns:</h3>";
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $matches_columns = $stmt->fetchAll();
    
    $tournament_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'><th>Column</th><th>Status</th><th>Type</th></tr>";
    
    foreach ($tournament_columns as $col) {
        $found = false;
        $type = '';
        foreach ($matches_columns as $column) {
            if ($column['Field'] == $col) {
                $found = true;
                $type = $column['Type'];
                break;
            }
        }
        
        if ($found) {
            echo "<tr><td>{$col}</td><td style='color: #28a745;'>✅ EXISTS</td><td>{$type}</td></tr>";
        } else {
            echo "<tr><td>{$col}</td><td style='color: #dc3545;'>❌ MISSING</td><td>-</td></tr>";
            $verification_passed = false;
        }
    }
    echo "</table>";
    
    if ($verification_passed) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 <strong>SCHEMA VERIFICATION PASSED!</strong></h3>";
        echo "<p style='font-size: 18px;'>All required tables and columns are now in place. Tournament creation should work perfectly!</p>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='tournament-creation-test.php' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block; margin-right: 10px;'>🧪 TEST TOURNAMENT CREATION</a>";
        echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #007bff; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE REAL TOURNAMENT</a>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>SCHEMA VERIFICATION FAILED!</strong></h3>";
        echo "<p>Some required components are still missing. Please check the errors above and try running the fix again.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error during verification: " . $e->getMessage() . "</p>";
}

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='comprehensive-schema-analysis.php'>🔍 Run Schema Analysis Again</a></li>";
echo "<li><a href='tournament-creation-test.php'>🧪 Test Tournament Creation</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation Page</a></li>";
echo "</ul>";
?>
