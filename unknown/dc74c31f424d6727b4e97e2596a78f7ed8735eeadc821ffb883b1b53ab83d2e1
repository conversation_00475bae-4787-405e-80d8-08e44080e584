<?php
/**
 * Final Tournament System Test
 * SC_IMS Sports Competition and Event Management System
 * 
 * Complete test of the tournament system including:
 * - Schema verification
 * - Auto-bracket generation for 5 teams
 * - Single Elimination tournament creation
 * - Match scheduling and bracket visualization
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🏆 Final Tournament System Test</h1>";
echo "<p>Complete test of the tournament system with auto-bracket generation for 5 teams...</p>";

$badminton_event_sport_id = 18;

echo "<h2>🔍 Step 1: System Readiness Check</h2>";

// Check all required components
$system_ready = true;
$issues = [];

// Check tables
$required_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants', 'matches'];
foreach ($required_tables as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$table]);
    if (!$stmt->fetch()) {
        $system_ready = false;
        $issues[] = "Missing table: {$table}";
    }
}

// Check matches table columns
$stmt = $conn->prepare("DESCRIBE matches");
$stmt->execute();
$matches_columns = $stmt->fetchAll();
$existing_columns = array_column($matches_columns, 'Field');

$required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
foreach ($required_columns as $col) {
    if (!in_array($col, $existing_columns)) {
        $system_ready = false;
        $issues[] = "Missing column in matches table: {$col}";
    }
}

// Check tournament format with algorithm class
$stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = 7");
$stmt->execute();
$format = $stmt->fetch();

if (!$format) {
    $system_ready = false;
    $issues[] = "Tournament format ID 7 (Single Elimination) not found";
} elseif (empty($format['algorithm_class'])) {
    $system_ready = false;
    $issues[] = "Tournament format missing algorithm_class";
}

if ($system_ready) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SYSTEM READY!</strong></h3>";
    echo "<p style='font-size: 18px;'>All required components are in place for tournament creation.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>SYSTEM NOT READY!</strong></h3>";
    echo "<p>Issues found:</p>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li style='color: #721c24; font-weight: bold;'>{$issue}</li>";
    }
    echo "</ul>";
    echo "<p><a href='comprehensive-schema-fix.php' style='background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-weight: bold;'>🔧 Fix Issues</a></p>";
    echo "</div>";
    exit;
}

echo "<h2>🔍 Step 2: Participant Analysis</h2>";

// Get participants for Badminton Mixed Doubles
$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        COALESCE(dsp.team_name, d.name) as team_name,
        edr.department_id,
        d.name as department_name,
        dsp.status,
        edr.status as registration_status
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
    ORDER BY d.name
");
$stmt->execute([$badminton_event_sport_id]);
$participants = $stmt->fetchAll();

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border: 2px solid #007bff; margin: 10px 0;'>";
echo "<h3>📊 Participant Summary</h3>";
echo "<p><strong>Total Confirmed Participants:</strong> " . count($participants) . "</p>";
echo "<p><strong>Tournament Format:</strong> {$format['name']} (Min: {$format['min_participants']})</p>";
echo "<p><strong>Algorithm Class:</strong> {$format['algorithm_class']}</p>";
echo "</div>";

if (count($participants) >= $format['min_participants']) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SUFFICIENT PARTICIPANTS!</strong></h3>";
    echo "<p>Ready for tournament creation with " . count($participants) . " teams.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>INSUFFICIENT PARTICIPANTS!</strong></h3>";
    echo "<p>Need at least {$format['min_participants']} participants, have " . count($participants) . "</p>";
    echo "</div>";
    exit;
}

echo "<h3>📋 Registered Teams:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f5f5f5;'><th>Seed</th><th>Team Name</th><th>Department</th><th>Status</th></tr>";

foreach ($participants as $index => $participant) {
    echo "<tr>";
    echo "<td>" . ($index + 1) . "</td>";
    echo "<td>{$participant['team_name']}</td>";
    echo "<td>{$participant['department_name']}</td>";
    echo "<td style='color: #28a745; font-weight: bold;'>{$participant['status']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔍 Step 3: Auto-Bracket Generation Preview</h2>";

// Calculate bracket structure for 5 teams
$participant_count = count($participants);
$rounds_needed = ceil(log($participant_count, 2));
$next_power_of_2 = pow(2, ceil(log($participant_count, 2)));
$byes_needed = $next_power_of_2 - $participant_count;

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; margin: 20px 0;'>";
echo "<h3>🧮 Bracket Mathematics</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><td><strong>Participants</strong></td><td>{$participant_count}</td></tr>";
echo "<tr><td><strong>Next Power of 2</strong></td><td>{$next_power_of_2}</td></tr>";
echo "<tr><td><strong>Byes Needed</strong></td><td>{$byes_needed}</td></tr>";
echo "<tr><td><strong>Total Rounds</strong></td><td>{$rounds_needed}</td></tr>";
echo "<tr><td><strong>Total Matches</strong></td><td>" . ($participant_count - 1) . "</td></tr>";
echo "</table>";
echo "</div>";

echo "<h3>🏗️ Predicted Bracket Structure:</h3>";

// Show predicted bracket structure
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 10px 0;'>";
echo "<h4>Round 1 (Quarterfinals):</h4>";
echo "<ul>";
echo "<li><strong>Match 1:</strong> Team 1 vs Team 2</li>";
echo "<li><strong>Match 2:</strong> Team 3 vs Team 4</li>";
echo "<li><strong>Bye:</strong> Team 5 (automatically advances to semifinals)</li>";
echo "</ul>";

echo "<h4>Round 2 (Semifinals):</h4>";
echo "<ul>";
echo "<li><strong>Match 3:</strong> Winner of Match 1 vs Winner of Match 2</li>";
echo "<li><strong>Match 4:</strong> Team 5 vs Winner of Match 3</li>";
echo "</ul>";

echo "<h4>Round 3 (Final):</h4>";
echo "<ul>";
echo "<li><strong>Match 5:</strong> Winner of Match 4 vs remaining team</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔍 Step 4: Create Tournament</h2>";

if (isset($_POST['create_tournament'])) {
    echo "<h3>🔄 Creating Tournament with Auto-Bracket Generation...</h3>";
    
    // Simulate the exact request
    $_POST['event_sport_id'] = $badminton_event_sport_id;
    $_POST['tournament_name'] = 'Badminton - Mixed Doubles Championship';
    $_POST['format_id'] = 7; // Single Elimination
    $_POST['seeding_method'] = 'random';
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border: 2px solid #007bff; margin: 10px 0;'>";
    echo "<h4>📤 Tournament Creation Parameters:</h4>";
    echo "<ul>";
    echo "<li><strong>Event Sport ID:</strong> {$_POST['event_sport_id']}</li>";
    echo "<li><strong>Tournament Name:</strong> {$_POST['tournament_name']}</li>";
    echo "<li><strong>Format ID:</strong> {$_POST['format_id']} (Single Elimination)</li>";
    echo "<li><strong>Seeding Method:</strong> {$_POST['seeding_method']}</li>";
    echo "<li><strong>Participants:</strong> {$participant_count} teams</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test the create-tournament.php logic
    ob_start();
    $success = false;
    $error_message = '';
    
    try {
        include 'ajax/create-tournament.php';
        $output = ob_get_clean();
        $success = true;
        
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 <strong>TOURNAMENT CREATED SUCCESSFULLY!</strong></h3>";
        echo "<p style='font-size: 18px;'>Auto-bracket generation completed for {$participant_count} teams!</p>";
        echo "<h4>System Response:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 200px;'>{$output}</pre>";
        echo "</div>";
        
        // Verify what was created
        echo "<h3>🔍 Tournament Verification:</h3>";
        
        // Get the created tournament
        $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE event_sport_id = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$badminton_event_sport_id]);
        $tournament = $stmt->fetch();
        
        if ($tournament) {
            echo "<h4>✅ Tournament Structure:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><td><strong>Tournament ID</strong></td><td>{$tournament['id']}</td></tr>";
            echo "<tr><td><strong>Name</strong></td><td>{$tournament['name']}</td></tr>";
            echo "<tr><td><strong>Status</strong></td><td>{$tournament['status']}</td></tr>";
            echo "<tr><td><strong>Participants</strong></td><td>{$tournament['participant_count']}</td></tr>";
            echo "<tr><td><strong>Total Rounds</strong></td><td>{$tournament['total_rounds']}</td></tr>";
            echo "<tr><td><strong>Seeding Method</strong></td><td>{$tournament['seeding_method']}</td></tr>";
            echo "</table>";
            
            // Check rounds created
            $stmt = $conn->prepare("SELECT * FROM tournament_rounds WHERE tournament_structure_id = ? ORDER BY round_number");
            $stmt->execute([$tournament['id']]);
            $rounds = $stmt->fetchAll();
            
            echo "<h4>✅ Tournament Rounds ({" . count($rounds) . "}):</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f5f5f5;'><th>Round</th><th>Name</th><th>Type</th><th>Status</th><th>Matches</th></tr>";
            foreach ($rounds as $round) {
                echo "<tr>";
                echo "<td>{$round['round_number']}</td>";
                echo "<td>{$round['round_name']}</td>";
                echo "<td>{$round['round_type']}</td>";
                echo "<td>{$round['status']}</td>";
                echo "<td>{$round['matches_count']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check matches created
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM matches WHERE tournament_structure_id = ?");
            $stmt->execute([$tournament['id']]);
            $match_count = $stmt->fetch()['count'];
            
            echo "<h4>✅ Matches Created: {$match_count}</h4>";
            
            if ($match_count > 0) {
                $stmt = $conn->prepare("
                    SELECT m.*, r1.team_name as team1_name, r2.team_name as team2_name 
                    FROM matches m
                    LEFT JOIN (
                        SELECT dsp.id, COALESCE(dsp.team_name, d.name) as team_name
                        FROM department_sport_participations dsp
                        JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
                        JOIN departments d ON edr.department_id = d.id
                    ) r1 ON m.team1_id = r1.id
                    LEFT JOIN (
                        SELECT dsp.id, COALESCE(dsp.team_name, d.name) as team_name
                        FROM department_sport_participations dsp
                        JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
                        JOIN departments d ON edr.department_id = d.id
                    ) r2 ON m.team2_id = r2.id
                    WHERE m.tournament_structure_id = ?
                    ORDER BY m.round_number, m.match_number
                ");
                $stmt->execute([$tournament['id']]);
                $matches = $stmt->fetchAll();
                
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f5f5f5;'><th>Round</th><th>Match</th><th>Team 1</th><th>Team 2</th><th>Position</th><th>Status</th></tr>";
                foreach ($matches as $match) {
                    $team1_display = $match['team1_name'] ?: 'TBD';
                    $team2_display = $match['team2_name'] ?: ($match['is_bye_match'] ? 'BYE' : 'TBD');
                    
                    echo "<tr>";
                    echo "<td>{$match['round_number']}</td>";
                    echo "<td>{$match['match_number']}</td>";
                    echo "<td>{$team1_display}</td>";
                    echo "<td>{$team2_display}</td>";
                    echo "<td>{$match['bracket_position']}</td>";
                    echo "<td>{$match['status']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
            echo "<h3 style='color: #155724; margin-top: 0;'>🏆 <strong>TOURNAMENT SYSTEM FULLY FUNCTIONAL!</strong></h3>";
            echo "<p style='font-size: 18px;'>✅ Auto-bracket generation working perfectly</p>";
            echo "<p style='font-size: 18px;'>✅ {$participant_count} teams properly seeded</p>";
            echo "<p style='font-size: 18px;'>✅ {$match_count} matches scheduled</p>";
            echo "<p style='font-size: 18px;'>✅ {$tournament['total_rounds']} rounds created</p>";
            echo "<div style='margin: 20px 0;'>";
            echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 GO TO TOURNAMENT MANAGEMENT</a>";
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        $error_message = $e->getMessage();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>TOURNAMENT CREATION FAILED!</strong></h3>";
        echo "<p><strong>Error:</strong> " . $error_message . "</p>";
        echo "</div>";
    }
}

if (!isset($_POST['create_tournament'])) {
    echo "<h3>🚀 Ready to Create Tournament</h3>";
    echo "<p>All systems are ready. Click below to create the tournament with automatic bracket generation for {$participant_count} teams.</p>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='create_tournament' value='1' style='background: #28a745; color: white; padding: 20px 40px; border: none; border-radius: 4px; font-size: 18px; font-weight: bold; cursor: pointer;'>🏆 CREATE TOURNAMENT WITH AUTO-BRACKETS</button>";
    echo "</form>";
}

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='comprehensive-schema-analysis.php'>🔍 Run Schema Analysis</a></li>";
echo "<li><a href='tournament-creation-test.php'>🧪 Run Tournament Test</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Management</a></li>";
echo "</ul>";
?>
