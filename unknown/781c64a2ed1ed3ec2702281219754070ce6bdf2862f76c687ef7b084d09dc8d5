<?php
/**
 * Quick Tournament System Check
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// Ensure admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

header('Content-Type: application/json');

try {
    // Check if tournament_formats table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'tournament_formats'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo json_encode([
            'success' => false,
            'message' => 'Tournament system not initialized. Please run tournament setup first.',
            'setup_url' => 'tournament-setup.php'
        ]);
        exit;
    }
    
    // Check if sports table has sport_type_id column
    $stmt = $conn->query("SHOW COLUMNS FROM sports LIKE 'sport_type_id'");
    $columnExists = $stmt->rowCount() > 0;
    
    if (!$columnExists) {
        echo json_encode([
            'success' => false,
            'message' => 'Sports table not updated. Please run tournament setup first.',
            'setup_url' => 'tournament-setup.php'
        ]);
        exit;
    }
    
    // Test getting sport type for sport ID 4
    $stmt = $conn->prepare("SELECT s.*, st.category FROM sports s LEFT JOIN sport_types st ON s.sport_type_id = st.id WHERE s.id = ?");
    $stmt->execute([4]);
    $sport = $stmt->fetch();
    
    if (!$sport) {
        echo json_encode([
            'success' => false,
            'message' => 'Sport ID 4 not found'
        ]);
        exit;
    }
    
    // Get available formats for this sport type
    $category = $sport['category'] ?? 'individual';
    $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE sport_type_category = ? OR sport_type_category = 'all' ORDER BY name");
    $stmt->execute([$category]);
    $formats = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'sport' => $sport,
        'category' => $category,
        'formats_count' => count($formats),
        'formats' => $formats
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
