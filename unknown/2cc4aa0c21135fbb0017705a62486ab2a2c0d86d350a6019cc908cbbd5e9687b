<!DOCTYPE html>
<html>
<head>
    <title>Final Modal Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        select { padding: 8px; margin: 5px; min-width: 200px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Final Tournament Format Dropdown Test</h1>
    
    <div class="test-section">
        <h3>Test: Add Sport Modal Simulation</h3>
        <p>This simulates the exact behavior of the Add Sport modal in manage-event.php</p>
        
        <div>
            <label>Sport:</label>
            <select id="sport_id" onchange="loadBracketTypes()">
                <option value="">Select a sport...</option>
                <option value="1" data-type="individual">Art Exhibition (Traditional Individual Sports)</option>
                <option value="2" data-type="team">Basketball (Traditional Team Sports)</option>
                <option value="3" data-type="academic">Chess (Academic Games)</option>
                <option value="4" data-type="judged">Singing Contest (Judged Competitions)</option>
                <option value="5" data-type="performance">Dance Competition (Performance Arts)</option>
                <option value="6" data-type="traditional">General Sport (Traditional)</option>
            </select>
        </div>
        
        <div>
            <label>Tournament Format:</label>
            <select id="tournament_format_id" onchange="showFormatInfo()">
                <option value="">Select a sport first...</option>
            </select>
        </div>
        
        <div id="format_info" style="display: none; margin-top: 10px; padding: 10px; background: #e9ecef; border-radius: 4px;">
            <div class="format-description"></div>
            <div class="format-details" style="margin-top: 5px; font-size: 0.9em; color: #6c757d;"></div>
        </div>
        
        <div id="test-result" class="result"></div>
    </div>

    <script>
        // Load bracket types based on selected sport (exact copy from manage-event.php)
        function loadBracketTypes() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const formatInfo = document.getElementById('format_info');
            const resultDiv = document.getElementById('test-result');

            const sportId = sportSelect.value;

            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            formatInfo.style.display = 'none';
            resultDiv.innerHTML = '<p class="info">Loading tournament formats...</p>';

            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                resultDiv.innerHTML = '<p class="error">No sport selected</p>';
                return;
            }

            // Get sport type from the selected option
            const selectedSportOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedSportOption.dataset.type || 'traditional';
            const sportName = selectedSportOption.textContent;

            resultDiv.innerHTML += `<p class="info">Selected sport: ${sportName}</p>`;
            resultDiv.innerHTML += `<p class="info">Sport type: ${sportType}</p>`;

            // Fetch tournament formats from database
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => {
                resultDiv.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                return response.text();
            })
            .then(text => {
                resultDiv.innerHTML += `<p class="info">Raw response length: ${text.length} characters</p>`;
                
                try {
                    const data = JSON.parse(text);
                    formatSelect.innerHTML = '<option value="">Select tournament format...</option>';

                    if (data.success && data.formats) {
                        resultDiv.innerHTML += `<p class="success">✓ Success! Found ${data.formats.length} formats</p>`;
                        
                        if (data.debug) {
                            resultDiv.innerHTML += `<p class="info">Debug info: Mapped types: ${data.debug.mapped_types.join(', ')}</p>`;
                        }
                        
                        data.formats.forEach(format => {
                            const option = document.createElement('option');
                            option.value = format.id;
                            option.textContent = format.name;
                            option.dataset.description = format.description;
                            option.dataset.minParticipants = format.min_participants;
                            option.dataset.maxParticipants = format.max_participants || 'Unlimited';
                            option.dataset.seedingRequired = format.requires_seeding;
                            option.dataset.advancementType = format.advancement_type;
                            formatSelect.appendChild(option);
                            
                            resultDiv.innerHTML += `<p class="success">Added format: ${format.name} (ID: ${format.id})</p>`;
                        });
                        
                        resultDiv.innerHTML += `<p class="success"><strong>✅ Tournament format dropdown is working correctly!</strong></p>`;
                    } else {
                        formatSelect.innerHTML = '<option value="">No formats available</option>';
                        resultDiv.innerHTML += `<p class="error">❌ No formats found or error: ${data.message || 'Unknown error'}</p>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML += `<p class="error">❌ JSON parse error: ${e.message}</p>`;
                    resultDiv.innerHTML += `<p class="error">Raw response: ${text.substring(0, 500)}${text.length > 500 ? '...' : ''}</p>`;
                    formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                }
            })
            .catch(error => {
                console.error('Error loading tournament formats:', error);
                resultDiv.innerHTML += `<p class="error">❌ Fetch error: ${error.message}</p>`;
                formatSelect.innerHTML = '<option value="">Error loading formats</option>';
            });
        }

        // Show format information when format is selected
        function showFormatInfo() {
            const formatSelect = document.getElementById('tournament_format_id');
            const formatInfo = document.getElementById('format_info');
            const selectedOption = formatSelect.options[formatSelect.selectedIndex];

            if (selectedOption.value && selectedOption.dataset.description) {
                const description = selectedOption.dataset.description;
                const minParticipants = selectedOption.dataset.minParticipants;
                const maxParticipants = selectedOption.dataset.maxParticipants;
                const seedingRequired = selectedOption.dataset.seedingRequired === 'true';
                const advancementType = selectedOption.dataset.advancementType;

                formatInfo.querySelector('.format-description').textContent = description;
                
                let details = `Min participants: ${minParticipants}`;
                if (maxParticipants !== 'Unlimited') {
                    details += `, Max participants: ${maxParticipants}`;
                }
                details += `, Advancement: ${advancementType}`;
                if (seedingRequired) {
                    details += `, Seeding required`;
                }
                
                formatInfo.querySelector('.format-details').textContent = details;
                formatInfo.style.display = 'block';
            } else {
                formatInfo.style.display = 'none';
            }
        }
    </script>
</body>
</html>
