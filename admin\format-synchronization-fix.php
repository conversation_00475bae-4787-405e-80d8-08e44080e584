<?php
/**
 * Format Synchronization Fix
 * SC_IMS Sports Competition and Event Management System
 * 
 * Fix tournament format inconsistencies across the system
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Format Synchronization Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Format Synchronization Fix</h1>
        <p><strong>Fix Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="warning">
            <h3>🎯 Issue Being Fixed</h3>
            <p><strong>Tournament Format Inconsistency:</strong> Different parts of the system showing different tournament formats (Round Robin vs Single Elimination) for the same sport/category.</p>
        </div>
        
        <?php
        $overall_success = true;
        $changes_made = [];
        $errors = [];
        
        try {
            // Step 1: Analyze current format inconsistencies
            echo '<div class="step">';
            echo '<h2>Step 1: Analyze Current Format Inconsistencies</h2>';
            
            // Check sport categories
            $stmt = $conn->prepare("
                SELECT sc.*, s.name as sport_name, s.bracket_format as sport_bracket_format, es.id as event_sport_id
                FROM sport_categories sc
                LEFT JOIN event_sports es ON sc.event_sport_id = es.id
                LEFT JOIN sports s ON es.sport_id = s.id
                WHERE sc.tournament_format IS NOT NULL OR s.bracket_format IS NOT NULL
                ORDER BY sc.name
            ");
            $stmt->execute();
            $categories = $stmt->fetchAll();
            
            if ($categories) {
                echo '<h3>Current Format Assignments:</h3>';
                echo '<table>';
                echo '<tr><th>Category Name</th><th>Sport Name</th><th>Category Format</th><th>Sport Format</th><th>Consistent?</th></tr>';
                
                $inconsistencies = [];
                foreach ($categories as $category) {
                    $category_format = $category['tournament_format'];
                    $sport_format = $category['sport_bracket_format'];
                    $is_consistent = ($category_format === $sport_format) || (empty($category_format) && empty($sport_format));
                    
                    echo '<tr style="background: ' . ($is_consistent ? '#d4edda' : '#f8d7da') . '">';
                    echo '<td>' . htmlspecialchars($category['name']) . '</td>';
                    echo '<td>' . htmlspecialchars($category['sport_name'] ?? 'N/A') . '</td>';
                    echo '<td>' . htmlspecialchars($category_format ?? 'None') . '</td>';
                    echo '<td>' . htmlspecialchars($sport_format ?? 'None') . '</td>';
                    echo '<td>' . ($is_consistent ? '✅ Yes' : '❌ No') . '</td>';
                    echo '</tr>';
                    
                    if (!$is_consistent) {
                        $inconsistencies[] = [
                            'category_id' => $category['id'],
                            'category_name' => $category['name'],
                            'event_sport_id' => $category['event_sport_id'],
                            'category_format' => $category_format,
                            'sport_format' => $sport_format
                        ];
                    }
                }
                echo '</table>';
                
                if (empty($inconsistencies)) {
                    echo '<div class="success">✅ No format inconsistencies found</div>';
                } else {
                    echo '<div class="warning">⚠️ Found ' . count($inconsistencies) . ' format inconsistencies</div>';
                }
            } else {
                echo '<p>No categories with tournament formats found.</p>';
            }
            
            echo '</div>';
            
            // Step 2: Define standardized format mapping
            echo '<div class="step">';
            echo '<h2>Step 2: Apply Standardized Format Mapping</h2>';
            
            // Define format standardization rules
            $format_rules = [
                // Sport name patterns -> preferred format
                'badminton' => 'single_elimination',
                'basketball' => 'single_elimination',
                'volleyball' => 'single_elimination',
                'football' => 'single_elimination',
                'chess' => 'round_robin',
                'table tennis' => 'single_elimination',
                'tennis' => 'single_elimination',
                'swimming' => 'round_robin',
                'track' => 'round_robin',
                'field' => 'round_robin'
            ];
            
            // Apply format standardization
            if (!empty($inconsistencies)) {
                echo '<h3>Fixing Format Inconsistencies:</h3>';
                echo '<table>';
                echo '<tr><th>Category</th><th>Current Format</th><th>New Format</th><th>Reason</th><th>Status</th></tr>';
                
                foreach ($inconsistencies as $inconsistency) {
                    $category_name = strtolower($inconsistency['category_name']);
                    $sport_name = strtolower($inconsistency['sport_name'] ?? '');
                    
                    // Determine the correct format
                    $new_format = 'single_elimination'; // Default
                    $reason = 'Default format';
                    
                    // Check against format rules
                    foreach ($format_rules as $pattern => $format) {
                        if (strpos($category_name, $pattern) !== false || strpos($sport_name, $pattern) !== false) {
                            $new_format = $format;
                            $reason = "Matched pattern: $pattern";
                            break;
                        }
                    }
                    
                    // Special cases
                    if (strpos($category_name, 'round') !== false || strpos($category_name, 'robin') !== false) {
                        $new_format = 'round_robin';
                        $reason = 'Contains "round" or "robin"';
                    }
                    
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($inconsistency['category_name']) . '</td>';
                    echo '<td>' . htmlspecialchars($inconsistency['category_format'] ?? 'None') . '</td>';
                    echo '<td>' . htmlspecialchars($new_format) . '</td>';
                    echo '<td>' . htmlspecialchars($reason) . '</td>';
                    
                    // Apply the fix
                    try {
                        // Update sport category
                        $stmt = $conn->prepare("UPDATE sport_categories SET tournament_format = ? WHERE id = ?");
                        $stmt->execute([$new_format, $inconsistency['category_id']]);
                        
                        // Update sport if event_sport_id exists
                        if ($inconsistency['event_sport_id']) {
                            $stmt = $conn->prepare("
                                UPDATE sports s 
                                JOIN event_sports es ON s.id = es.sport_id 
                                SET s.bracket_format = ? 
                                WHERE es.id = ?
                            ");
                            $stmt->execute([$new_format, $inconsistency['event_sport_id']]);
                        }
                        
                        echo '<td style="color: green;">✅ Fixed</td>';
                        $changes_made[] = "Fixed format for category: " . $inconsistency['category_name'] . " -> " . $new_format;
                        
                    } catch (Exception $e) {
                        echo '<td style="color: red;">❌ Failed</td>';
                        $errors[] = "Failed to fix category " . $inconsistency['category_name'] . ": " . $e->getMessage();
                        $overall_success = false;
                    }
                    
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            echo '</div>';
            
            // Step 3: Ensure format consistency across all sports
            echo '<div class="step">';
            echo '<h2>Step 3: Ensure Global Format Consistency</h2>';
            
            // Get all sports and their categories
            $stmt = $conn->prepare("
                SELECT DISTINCT s.id as sport_id, s.name as sport_name, s.bracket_format,
                       GROUP_CONCAT(DISTINCT sc.tournament_format) as category_formats
                FROM sports s
                LEFT JOIN event_sports es ON s.id = es.sport_id
                LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
                GROUP BY s.id, s.name, s.bracket_format
                HAVING s.bracket_format IS NOT NULL OR category_formats IS NOT NULL
            ");
            $stmt->execute();
            $sports = $stmt->fetchAll();
            
            if ($sports) {
                echo '<h3>Sport Format Consistency Check:</h3>';
                echo '<table>';
                echo '<tr><th>Sport</th><th>Sport Format</th><th>Category Formats</th><th>Action</th></tr>';
                
                foreach ($sports as $sport) {
                    $sport_format = $sport['bracket_format'];
                    $category_formats = array_filter(explode(',', $sport['category_formats'] ?? ''));
                    $category_formats = array_unique($category_formats);
                    
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($sport['sport_name']) . '</td>';
                    echo '<td>' . htmlspecialchars($sport_format ?? 'None') . '</td>';
                    echo '<td>' . htmlspecialchars(implode(', ', $category_formats)) . '</td>';
                    
                    // Check if formats are consistent
                    $all_formats = array_filter(array_merge([$sport_format], $category_formats));
                    $unique_formats = array_unique($all_formats);
                    
                    if (count($unique_formats) <= 1) {
                        echo '<td style="color: green;">✅ Consistent</td>';
                    } else {
                        // Fix inconsistency - use the most common format or apply rules
                        $preferred_format = 'single_elimination';
                        $sport_name_lower = strtolower($sport['sport_name']);
                        
                        foreach ($format_rules as $pattern => $format) {
                            if (strpos($sport_name_lower, $pattern) !== false) {
                                $preferred_format = $format;
                                break;
                            }
                        }
                        
                        try {
                            // Update sport format
                            $stmt = $conn->prepare("UPDATE sports SET bracket_format = ? WHERE id = ?");
                            $stmt->execute([$preferred_format, $sport['sport_id']]);
                            
                            // Update all categories for this sport
                            $stmt = $conn->prepare("
                                UPDATE sport_categories sc
                                JOIN event_sports es ON sc.event_sport_id = es.id
                                SET sc.tournament_format = ?
                                WHERE es.sport_id = ?
                            ");
                            $stmt->execute([$preferred_format, $sport['sport_id']]);
                            
                            echo '<td style="color: green;">✅ Fixed to ' . $preferred_format . '</td>';
                            $changes_made[] = "Standardized format for sport: " . $sport['sport_name'] . " -> " . $preferred_format;
                            
                        } catch (Exception $e) {
                            echo '<td style="color: red;">❌ Failed to fix</td>';
                            $errors[] = "Failed to fix sport " . $sport['sport_name'] . ": " . $e->getMessage();
                            $overall_success = false;
                        }
                    }
                    
                    echo '</tr>';
                }
                echo '</table>';
            }
            
            echo '</div>';
            
            // Step 4: Verify the fix
            echo '<div class="step">';
            echo '<h2>Step 4: Verify Format Synchronization</h2>';
            
            // Re-check for inconsistencies
            $stmt = $conn->prepare("
                SELECT sc.*, s.name as sport_name, s.bracket_format as sport_bracket_format
                FROM sport_categories sc
                LEFT JOIN event_sports es ON sc.event_sport_id = es.id
                LEFT JOIN sports s ON es.sport_id = s.id
                WHERE sc.tournament_format IS NOT NULL AND s.bracket_format IS NOT NULL
                AND sc.tournament_format != s.bracket_format
            ");
            $stmt->execute();
            $remaining_inconsistencies = $stmt->fetchAll();
            
            if (empty($remaining_inconsistencies)) {
                echo '<div class="success">✅ All format inconsistencies have been resolved</div>';
            } else {
                echo '<div class="warning">⚠️ ' . count($remaining_inconsistencies) . ' inconsistencies still remain</div>';
                $overall_success = false;
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Format synchronization error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
            $overall_success = false;
        }
        ?>
        
        <!-- Results Summary -->
        <div class="step">
            <h2>📊 Format Synchronization Results</h2>
            
            <?php if ($overall_success): ?>
                <div class="success">
                    <h3>🎉 FORMAT SYNCHRONIZATION SUCCESSFUL!</h3>
                    <p><strong>✅ Tournament format inconsistencies have been resolved</strong></p>
                    <p><strong>✅ All sports and categories now have synchronized formats</strong></p>
                    <p><strong>✅ Format display should now be consistent across the system</strong></p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h3>❌ Some Issues Occurred</h3>
                    <p>The format synchronization encountered some problems:</p>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($changes_made)): ?>
                <div class="info">
                    <h3>✅ Changes Applied:</h3>
                    <ul>
                        <?php foreach ($changes_made as $change): ?>
                            <li><?php echo htmlspecialchars($change); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Format Rules Reference -->
        <div class="step">
            <h2>📋 Format Standardization Rules Applied</h2>
            <div class="info">
                <h3>🎯 Format Assignment Rules</h3>
                <table>
                    <tr><th>Sport Type</th><th>Assigned Format</th><th>Reason</th></tr>
                    <tr><td>Badminton, Basketball, Volleyball, Football, Tennis, Table Tennis</td><td>Single Elimination</td><td>Traditional knockout sports</td></tr>
                    <tr><td>Chess, Swimming, Track & Field</td><td>Round Robin</td><td>Performance/skill-based sports</td></tr>
                    <tr><td>Categories containing "round" or "robin"</td><td>Round Robin</td><td>Explicit format indication</td></tr>
                    <tr><td>All others</td><td>Single Elimination</td><td>Default format</td></tr>
                </table>
            </div>
        </div>
        
        <!-- Next Steps -->
        <div class="step">
            <h2>🚀 Next Steps</h2>
            <p>
                <?php if ($overall_success): ?>
                    <a href="comprehensive-tournament-test.php" class="btn btn-success">🧪 Test Tournament Creation</a>
                    <a href="manage-event.php?id=1" class="btn">📋 Manage Events</a>
                    <a href="index.php" class="btn">🏠 Admin Dashboard</a>
                <?php else: ?>
                    <a href="format-synchronization-fix.php" class="btn">🔄 Re-run Synchronization</a>
                    <a href="critical-database-investigation.php" class="btn">🔍 Investigate Issues</a>
                <?php endif; ?>
            </p>
        </div>
    </div>
</body>
</html>
