<?php
/**
 * Direct Tournament Setup Script
 * Run this to initialize the tournament system
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// Ensure admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament System Setup</h2>";

try {
    // Read and execute the SQL file
    $sqlFile = __DIR__ . '/../database/tournament_schema_update.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception('Tournament schema file not found: ' . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception('Could not read tournament schema file');
    }
    
    echo "<p>✅ SQL file loaded successfully</p>";
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "<p>📝 Found " . count($statements) . " SQL statements</p>";
    
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    
    // Execute each statement
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
            try {
                $conn->exec($statement);
                $successCount++;
                echo "<p>✅ Executed statement " . ($successCount + $errorCount) . "</p>";
            } catch (PDOException $e) {
                $errorCount++;
                $error = "❌ Error in statement " . ($successCount + $errorCount) . ": " . $e->getMessage();
                $errors[] = $error;
                echo "<p>$error</p>";
                
                // Continue with other statements even if one fails
            }
        }
    }
    
    echo "<h3>Setup Results:</h3>";
    echo "<p>✅ Successful statements: $successCount</p>";
    echo "<p>❌ Failed statements: $errorCount</p>";
    
    if ($errorCount > 0) {
        echo "<h4>Errors:</h4>";
        foreach ($errors as $error) {
            echo "<p>$error</p>";
        }
    }
    
    // Test the setup
    echo "<h3>Testing Setup:</h3>";
    
    // Check tables
    $tables = ['sport_types', 'tournament_formats'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            echo "<p>✅ Table '$table': {$result['count']} records</p>";
        } catch (PDOException $e) {
            echo "<p>❌ Table '$table': Error - {$e->getMessage()}</p>";
        }
    }
    
    // Check sports table update
    try {
        $stmt = $conn->query("SHOW COLUMNS FROM sports LIKE 'sport_type_id'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Sports table updated with sport_type_id column</p>";
        } else {
            echo "<p>❌ Sports table missing sport_type_id column</p>";
        }
    } catch (PDOException $e) {
        echo "<p>❌ Error checking sports table: {$e->getMessage()}</p>";
    }
    
    // Test tournament manager
    try {
        require_once '../includes/tournament_manager.php';
        $tournamentManager = new TournamentManager($conn);
        
        // Test with a sport
        $stmt = $conn->query("SELECT id FROM sports LIMIT 1");
        $sport = $stmt->fetch();
        
        if ($sport) {
            $category = $tournamentManager->getSportTypeCategory($sport['id']);
            $formats = $tournamentManager->getAvailableFormats($category);
            echo "<p>✅ TournamentManager working: Found " . count($formats) . " formats for category '$category'</p>";
        }
        
    } catch (Exception $e) {
        echo "<p>❌ TournamentManager error: {$e->getMessage()}</p>";
    }
    
    if ($errorCount == 0) {
        echo "<h3 style='color: green;'>🎉 Tournament system setup completed successfully!</h3>";
        echo "<p><a href='manage-event.php?event_id=1'>Test the tournament system</a></p>";
    } else {
        echo "<h3 style='color: orange;'>⚠️ Setup completed with some errors</h3>";
        echo "<p>You may need to manually fix the errors above.</p>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Setup failed: " . $e->getMessage() . "</h3>";
}
?>
