<?php
/**
 * Tournament Creation Test
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Tournament Creation Test</h1>";
echo "<p>Testing complete tournament creation flow after schema fixes...</p>";

$badminton_event_sport_id = 18;

echo "<h2>🔍 Step 1: Pre-Flight Checks</h2>";

// Check schema is complete
$required_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];
$schema_ok = true;

echo "<h3>Required Tables:</h3>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr style='background: #f5f5f5;'><th>Table</th><th>Status</th></tr>";

foreach ($required_tables as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$table]);
    $exists = $stmt->fetch();
    
    if ($exists) {
        echo "<tr><td>{$table}</td><td style='color: #28a745;'>✅ EXISTS</td></tr>";
    } else {
        echo "<tr><td>{$table}</td><td style='color: #dc3545;'>❌ MISSING</td></tr>";
        $schema_ok = false;
    }
}
echo "</table>";

// Check matches table has tournament columns
echo "<h3>Matches Table Tournament Columns:</h3>";
$stmt = $conn->prepare("DESCRIBE matches");
$stmt->execute();
$matches_columns = $stmt->fetchAll();

$existing_columns = [];
foreach ($matches_columns as $column) {
    $existing_columns[] = $column['Field'];
}

$required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr style='background: #f5f5f5;'><th>Column</th><th>Status</th></tr>";

foreach ($required_columns as $col) {
    if (in_array($col, $existing_columns)) {
        echo "<tr><td>{$col}</td><td style='color: #28a745;'>✅ EXISTS</td></tr>";
    } else {
        echo "<tr><td>{$col}</td><td style='color: #dc3545;'>❌ MISSING</td></tr>";
        $schema_ok = false;
    }
}
echo "</table>";

if (!$schema_ok) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>SCHEMA INCOMPLETE!</strong></h3>";
    echo "<p>Please run the comprehensive schema fix first.</p>";
    echo "<p><a href='comprehensive-schema-fix.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Fix Schema Now</a></p>";
    echo "</div>";
    exit;
}

echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SCHEMA CHECK PASSED!</strong></h3>";
echo "<p>All required tables and columns exist.</p>";
echo "</div>";

echo "<h2>🔍 Step 2: Check Tournament Format</h2>";

// Get the correct format ID
$default_format_id = 7; // Use ID 7 which should exist from our schema fix
$stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
$stmt->execute([$default_format_id]);
$format = $stmt->fetch();

if ($format) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>FORMAT FOUND!</strong></h3>";
    echo "<p><strong>ID:</strong> {$format['id']}</p>";
    echo "<p><strong>Name:</strong> {$format['name']}</p>";
    echo "<p><strong>Code:</strong> {$format['code']}</p>";
    echo "<p><strong>Min Participants:</strong> {$format['min_participants']}</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>FORMAT NOT FOUND!</strong></h3>";
    echo "<p>Tournament format with ID {$default_format_id} does not exist.</p>";
    echo "</div>";
    exit;
}

echo "<h2>🔍 Step 3: Check Participants</h2>";

$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        COALESCE(dsp.team_name, d.name) as team_name,
        edr.department_id,
        d.name as department_name,
        dsp.status,
        edr.status as registration_status
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
");
$stmt->execute([$badminton_event_sport_id]);
$participants = $stmt->fetchAll();

echo "<p><strong>Confirmed Participants:</strong> " . count($participants) . "</p>";

if (count($participants) >= $format['min_participants']) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SUFFICIENT PARTICIPANTS!</strong></h3>";
    echo "<p>Have " . count($participants) . " participants, need minimum " . $format['min_participants'] . "</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>INSUFFICIENT PARTICIPANTS!</strong></h3>";
    echo "<p>Have " . count($participants) . " participants, need minimum " . $format['min_participants'] . "</p>";
    echo "</div>";
    exit;
}

echo "<h3>📋 Participant List:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f5f5f5;'><th>ID</th><th>Team Name</th><th>Department</th><th>Status</th></tr>";

foreach ($participants as $participant) {
    echo "<tr>";
    echo "<td>{$participant['id']}</td>";
    echo "<td>{$participant['team_name']}</td>";
    echo "<td>{$participant['department_name']}</td>";
    echo "<td>{$participant['status']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>🔍 Step 4: Test Tournament Creation</h2>";

if (isset($_POST['test_tournament_creation'])) {
    echo "<h3>🔄 Creating Tournament...</h3>";
    
    // Simulate the exact request
    $_POST['event_sport_id'] = $badminton_event_sport_id;
    $_POST['tournament_name'] = 'Badminton - Mixed Doubles Tournament';
    $_POST['format_id'] = $default_format_id;
    $_POST['seeding_method'] = 'random';
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border: 2px solid #007bff; margin: 10px 0;'>";
    echo "<h4>📤 Tournament Creation Parameters:</h4>";
    echo "<ul>";
    echo "<li><strong>event_sport_id:</strong> {$_POST['event_sport_id']}</li>";
    echo "<li><strong>tournament_name:</strong> {$_POST['tournament_name']}</li>";
    echo "<li><strong>format_id:</strong> {$_POST['format_id']}</li>";
    echo "<li><strong>seeding_method:</strong> {$_POST['seeding_method']}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test the create-tournament.php logic
    ob_start();
    $success = false;
    $error_message = '';
    
    try {
        include 'ajax/create-tournament.php';
        $output = ob_get_clean();
        $success = true;
        
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 <strong>TOURNAMENT CREATION SUCCESSFUL!</strong></h3>";
        echo "<p style='font-size: 18px;'>The tournament system is now fully functional!</p>";
        echo "<h4>Response:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px;'>{$output}</pre>";
        echo "</div>";
        
        // Check what was created
        echo "<h3>🔍 Verify Tournament Creation:</h3>";
        
        // Check tournament structure
        $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE event_sport_id = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$badminton_event_sport_id]);
        $tournament_structure = $stmt->fetch();
        
        if ($tournament_structure) {
            echo "<h4>Tournament Structure Created:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><td><strong>ID</strong></td><td>{$tournament_structure['id']}</td></tr>";
            echo "<tr><td><strong>Name</strong></td><td>{$tournament_structure['name']}</td></tr>";
            echo "<tr><td><strong>Status</strong></td><td>{$tournament_structure['status']}</td></tr>";
            echo "<tr><td><strong>Participants</strong></td><td>{$tournament_structure['participant_count']}</td></tr>";
            echo "<tr><td><strong>Rounds</strong></td><td>{$tournament_structure['total_rounds']}</td></tr>";
            echo "</table>";
            
            // Check tournament rounds
            $stmt = $conn->prepare("SELECT * FROM tournament_rounds WHERE tournament_structure_id = ?");
            $stmt->execute([$tournament_structure['id']]);
            $rounds = $stmt->fetchAll();
            
            if (!empty($rounds)) {
                echo "<h4>Tournament Rounds Created:</h4>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr style='background: #f5f5f5;'><th>Round #</th><th>Name</th><th>Type</th><th>Status</th><th>Matches</th></tr>";
                foreach ($rounds as $round) {
                    echo "<tr>";
                    echo "<td>{$round['round_number']}</td>";
                    echo "<td>{$round['round_name']}</td>";
                    echo "<td>{$round['round_type']}</td>";
                    echo "<td>{$round['status']}</td>";
                    echo "<td>{$round['matches_count']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            // Check matches created
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM matches WHERE tournament_structure_id = ?");
            $stmt->execute([$tournament_structure['id']]);
            $match_count = $stmt->fetch()['count'];
            
            echo "<h4>Matches Created: {$match_count}</h4>";
            
            if ($match_count > 0) {
                $stmt = $conn->prepare("SELECT * FROM matches WHERE tournament_structure_id = ? LIMIT 5");
                $stmt->execute([$tournament_structure['id']]);
                $matches = $stmt->fetchAll();
                
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr style='background: #f5f5f5;'><th>Match ID</th><th>Round</th><th>Team 1</th><th>Team 2</th><th>Position</th><th>Status</th></tr>";
                foreach ($matches as $match) {
                    echo "<tr>";
                    echo "<td>{$match['id']}</td>";
                    echo "<td>{$match['round_number']}</td>";
                    echo "<td>{$match['team1_id']}</td>";
                    echo "<td>{$match['team2_id']}</td>";
                    echo "<td>{$match['bracket_position']}</td>";
                    echo "<td>{$match['status']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                if ($match_count > 5) {
                    echo "<p><em>Showing first 5 of {$match_count} matches...</em></p>";
                }
            }
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        $error_message = $e->getMessage();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>TOURNAMENT CREATION FAILED!</strong></h3>";
        echo "<p><strong>Error:</strong> " . $error_message . "</p>";
        echo "</div>";
        
        // Provide specific troubleshooting
        if (strpos($error_message, 'tournament_structure_id') !== false) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 10px 0;'>";
            echo "<h4 style='color: #856404;'>🔧 <strong>SCHEMA ISSUE!</strong></h4>";
            echo "<p>The error indicates missing tournament_structure_id column. Please run the schema fix again.</p>";
            echo "<p><a href='comprehensive-schema-fix.php' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;'>🔧 Fix Schema Again</a></p>";
            echo "</div>";
        }
    }
    
    if ($success) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🏆 <strong>COMPLETE SUCCESS!</strong></h3>";
        echo "<p style='font-size: 18px;'>Tournament system is fully functional! You can now create tournaments for Badminton Mixed Doubles.</p>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE REAL TOURNAMENT NOW</a>";
        echo "</div>";
        echo "</div>";
    }
}

echo "<h3>🚀 Test Tournament Creation</h3>";
echo "<form method='POST'>";
echo "<button type='submit' name='test_tournament_creation' value='1' style='background: #007bff; color: white; padding: 20px 40px; border: none; border-radius: 4px; font-size: 18px; font-weight: bold; cursor: pointer;'>🧪 Test Tournament Creation</button>";
echo "</form>";

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='comprehensive-schema-analysis.php'>🔍 Run Schema Analysis</a></li>";
echo "<li><a href='comprehensive-schema-fix.php'>🔧 Run Schema Fix</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation Page</a></li>";
echo "</ul>";
?>
