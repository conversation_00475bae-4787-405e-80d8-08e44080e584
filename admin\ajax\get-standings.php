<?php
/**
 * AJAX Endpoint for Live Standings Updates
 * Returns real-time department rankings for an event
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $event_id = $_GET['event_id'] ?? 0;
    
    if (!$event_id) {
        throw new Exception('Event ID is required');
    }
    
    // Verify event exists
    $event = getEventById($conn, $event_id);
    if (!$event) {
        throw new Exception('Event not found');
    }
    
    // Get live standings (Updated for Unified Registration System)
    function getLiveStandings($conn, $event_id) {
        $sql = "SELECT
                    d.id,
                    d.name as department_name,
                    d.abbreviation,
                    d.color_code,
                    edr.status as registration_status,
                    COUNT(DISTINCT dsp.id) as sports_participated,
                    COUNT(DISTINCT CASE WHEN m.winner_id = r.id THEN m.id END) as matches_won,
                    COUNT(DISTINCT CASE WHEN m.status = 'completed' AND (m.team1_id = r.id OR m.team2_id = r.id) THEN m.id END) as total_matches,
                    COALESCE(SUM(CASE
                        WHEN m.winner_id = r.id THEN 3
                        WHEN m.status = 'completed' AND m.winner_id IS NULL AND (m.team1_id = r.id OR m.team2_id = r.id) THEN 1
                        ELSE 0
                    END), 0) as total_points,
                    COUNT(DISTINCT CASE WHEN m.status = 'completed' AND m.winner_id IS NULL AND (m.team1_id = r.id OR m.team2_id = r.id) THEN m.id END) as draws,
                    COUNT(DISTINCT CASE WHEN m.status = 'completed' AND m.winner_id != r.id AND m.winner_id IS NOT NULL AND (m.team1_id = r.id OR m.team2_id = r.id) THEN m.id END) as matches_lost
                FROM departments d
                INNER JOIN event_department_registrations edr ON d.id = edr.department_id AND edr.event_id = ?
                LEFT JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
                LEFT JOIN registrations r ON r.department_sport_participation_id = dsp.id
                LEFT JOIN matches m ON (m.team1_id = r.id OR m.team2_id = r.id)
                WHERE edr.status = 'approved'
                GROUP BY d.id, d.name, d.abbreviation, d.color_code, edr.status
                ORDER BY total_points DESC, matches_won DESC, department_name";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_id, $event_id]);
        return $stmt->fetchAll();
    }
    
    $standings = getLiveStandings($conn, $event_id);
    
    // Calculate additional statistics
    $total_departments = count($standings);
    $total_points_awarded = array_sum(array_column($standings, 'total_points'));
    $total_matches_played = 0;
    $total_matches_completed = 0;
    
    // Get match statistics
    $sql = "SELECT 
                COUNT(*) as total_matches,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_matches
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            WHERE es.event_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    $match_stats = $stmt->fetch();
    
    $total_matches_played = $match_stats['total_matches'] ?? 0;
    $total_matches_completed = $match_stats['completed_matches'] ?? 0;
    
    // Calculate progress percentage
    $progress_percentage = $total_matches_played > 0 ? round(($total_matches_completed / $total_matches_played) * 100, 1) : 0;
    
    // Add ranking and additional data to standings
    foreach ($standings as $index => &$dept) {
        $dept['rank'] = $index + 1;
        $dept['win_rate'] = $dept['total_matches'] > 0 ? round(($dept['matches_won'] / $dept['total_matches']) * 100, 1) : 0;
        $dept['rank_class'] = $dept['rank'] <= 3 ? "rank-{$dept['rank']}" : "rank-other";
        
        // Determine rank change (for future implementation)
        $dept['rank_change'] = 0; // Could be calculated by comparing with previous standings
        $dept['rank_trend'] = 'stable'; // 'up', 'down', 'stable'
    }
    
    // Get recent match results for context
    $sql = "SELECT m.*, 
                   s.name as sport_name,
                   d1.name as team1_name, d1.abbreviation as team1_abbr,
                   d2.name as team2_name, d2.abbreviation as team2_abbr,
                   dw.name as winner_name, dw.abbreviation as winner_abbr
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            LEFT JOIN registrations rw ON m.winner_id = rw.id
            LEFT JOIN departments dw ON rw.department_id = dw.id
            WHERE es.event_id = ? AND m.status = 'completed'
            ORDER BY m.actual_end_time DESC
            LIMIT 5";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    $recent_matches = $stmt->fetchAll();
    
    // Get upcoming matches
    $sql = "SELECT m.*, 
                   s.name as sport_name,
                   d1.name as team1_name, d1.abbreviation as team1_abbr,
                   d2.name as team2_name, d2.abbreviation as team2_abbr
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE es.event_id = ? AND m.status IN ('scheduled', 'in_progress')
            ORDER BY m.scheduled_time ASC
            LIMIT 5";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id]);
    $upcoming_matches = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'standings' => $standings,
            'statistics' => [
                'total_departments' => $total_departments,
                'total_points_awarded' => $total_points_awarded,
                'total_matches_played' => $total_matches_played,
                'total_matches_completed' => $total_matches_completed,
                'progress_percentage' => $progress_percentage
            ],
            'recent_matches' => $recent_matches,
            'upcoming_matches' => $upcoming_matches,
            'last_updated' => date('Y-m-d H:i:s')
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
