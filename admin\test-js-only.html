<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript Test</title>
    <style>
        .section-tabs {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .tab-button {
            padding: 10px 20px;
            border: 1px solid #ccc;
            background: #f8f9fa;
            cursor: pointer;
            border-radius: 5px;
        }
        .tab-button.active {
            background: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .tab-content.active {
            display: block;
        }
        .debug-controls {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <h1>JavaScript Tab Test</h1>
    
    <!-- Tab Navigation -->
    <div class="section-tabs">
        <button class="tab-button active" data-tab="standings" onclick="showTab('standings', this)">
            <span>Standings</span>
        </button>
        <button class="tab-button" data-tab="sports" onclick="showTab('sports', this)">
            <span>Sports Management</span>
        </button>
        <button class="tab-button" data-tab="registrations" onclick="showTab('registrations', this)">
            <span>Registrations</span>
        </button>
        <button class="tab-button" data-tab="matches" onclick="showTab('matches', this)">
            <span>Recent Matches</span>
        </button>
    </div>

    <!-- Debug Controls -->
    <div class="debug-controls">
        <strong>Debug Controls:</strong>
        <button onclick="window.testTabClick('standings')" class="btn btn-sm btn-secondary">Test Standings</button>
        <button onclick="window.testTabClick('sports')" class="btn btn-sm btn-secondary">Test Sports</button>
        <button onclick="window.testTabClick('registrations')" class="btn btn-sm btn-secondary">Test Registrations</button>
        <button onclick="window.testTabClick('matches')" class="btn btn-sm btn-secondary">Test Matches</button>
        <button onclick="window.testAllTabs()" class="btn btn-sm btn-primary">Test All Tabs</button>
    </div>

    <!-- Tab Contents -->
    <div id="standings-tab" class="tab-content active">
        <h2>Current Standings</h2>
        <p>Standings content goes here.</p>
    </div>

    <div id="sports-tab" class="tab-content">
        <h2>Sports Management</h2>
        <p>Sports management content goes here.</p>
    </div>

    <div id="registrations-tab" class="tab-content">
        <h2>Registrations</h2>
        <p>Registration content goes here.</p>
    </div>

    <div id="matches-tab" class="tab-content">
        <h2>Recent Matches</h2>
        <p>Recent matches content goes here.</p>
    </div>

    <script>
        // Simple Tab System (Exact copy from manage-event.php)
        console.log('Initializing Simple Tab System');
        
        function showTab(tabName, clickedButton) {
            console.log('showTab called:', tabName, clickedButton ? clickedButton.textContent.trim() : 'null');

            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
                console.log('Hiding:', content.id);
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('Showing:', targetTab.id);
            } else {
                console.error('Tab not found:', tabName + '-tab');
                return false;
            }

            // Add active class to clicked button
            if (clickedButton) {
                clickedButton.classList.add('active');
                console.log('Activated button:', clickedButton.textContent.trim());
            } else {
                // Fallback: find the button by data-tab attribute
                const button = document.querySelector(`[data-tab="${tabName}"]`);
                if (button) {
                    button.classList.add('active');
                    console.log('Found and activated button by data-tab');
                }
            }
            
            // Save to localStorage
            localStorage.setItem('activeEventTab', tabName);
            return true;
        }

        // Initialize tabs when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM ready, initializing tabs');
            
            // Restore saved tab or use default
            const savedTab = localStorage.getItem('activeEventTab');
            if (savedTab && document.getElementById(savedTab + '-tab')) {
                console.log('Restoring saved tab:', savedTab);
                const button = document.querySelector(`[data-tab="${savedTab}"]`);
                showTab(savedTab, button);
            } else {
                console.log('Using default tab: standings');
                const button = document.querySelector(`[data-tab="standings"]`);
                if (button) {
                    showTab('standings', button);
                }
            }
            
            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key >= '1' && e.key <= '4') {
                    e.preventDefault();
                    const tabMap = {
                        '1': 'standings',
                        '2': 'sports', 
                        '3': 'registrations',
                        '4': 'matches'
                    };
                    const tabName = tabMap[e.key];
                    if (tabName) {
                        const button = document.querySelector(`[data-tab="${tabName}"]`);
                        showTab(tabName, button);
                    }
                }
            });
        });

        // Test functions for debugging
        window.testTabClick = function(tabName) {
            console.log('Testing tab click for:', tabName);
            const button = document.querySelector(`[data-tab="${tabName}"]`);
            return showTab(tabName, button);
        };
        
        window.testAllTabs = function() {
            console.log('Testing all tabs...');
            const tabs = ['standings', 'sports', 'registrations', 'matches'];
            let index = 0;
            
            function testNext() {
                if (index < tabs.length) {
                    console.log(`Testing tab ${index + 1}: ${tabs[index]}`);
                    const button = document.querySelector(`[data-tab="${tabs[index]}"]`);
                    showTab(tabs[index], button);
                    index++;
                    setTimeout(testNext, 1000);
                } else {
                    console.log('All tabs tested successfully');
                }
            }
            
            testNext();
        };
    </script>
</body>
</html>
