<?php
/**
 * Debug Event Deletion Issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Event Deletion</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>Debug Event Deletion</h1>
    
    <div class="section">
        <h3>1. Database Foreign Key Constraints Analysis</h3>
        <div id="constraints-analysis"></div>
        <button onclick="analyzeConstraints()">Analyze Database Constraints</button>
    </div>
    
    <div class="section">
        <h3>2. Event Dependencies Check</h3>
        <div id="dependencies-check"></div>
        <button onclick="checkEventDependencies()">Check Event Dependencies</button>
    </div>
    
    <div class="section">
        <h3>3. Test CSRF Token Generation</h3>
        <div id="csrf-test"></div>
        <button onclick="testCSRFToken()">Test CSRF Token</button>
    </div>
    
    <div class="section">
        <h3>4. Test Event Deletion with CSRF Token</h3>
        <div id="deletion-test"></div>
        <button onclick="testEventDeletion()" class="danger">Test Event Deletion (Safe Test Event)</button>
    </div>
    
    <div class="section">
        <h3>5. Create Test Event for Deletion</h3>
        <div id="create-test"></div>
        <button onclick="createTestEvent()">Create Test Event</button>
    </div>

    <script>
        async function analyzeConstraints() {
            const resultDiv = document.getElementById('constraints-analysis');
            resultDiv.innerHTML = '<p>Analyzing database constraints...</p>';
            
            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=analyze_constraints'
                });
                
                const text = await response.text();
                const data = JSON.parse(text);
                
                if (data.success) {
                    let html = '<h4>Foreign Key Constraints:</h4>';
                    if (data.constraints && data.constraints.length > 0) {
                        html += '<table><tr><th>Table</th><th>Column</th><th>Referenced Table</th><th>Referenced Column</th><th>Delete Rule</th><th>Update Rule</th></tr>';
                        data.constraints.forEach(constraint => {
                            html += `<tr>
                                <td>${constraint.TABLE_NAME}</td>
                                <td>${constraint.COLUMN_NAME}</td>
                                <td>${constraint.REFERENCED_TABLE_NAME}</td>
                                <td>${constraint.REFERENCED_COLUMN_NAME}</td>
                                <td>${constraint.DELETE_RULE}</td>
                                <td>${constraint.UPDATE_RULE}</td>
                            </tr>`;
                        });
                        html += '</table>';
                    } else {
                        html += '<p>No foreign key constraints found.</p>';
                    }
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">Error: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function checkEventDependencies() {
            const resultDiv = document.getElementById('dependencies-check');
            resultDiv.innerHTML = '<p>Checking event dependencies...</p>';
            
            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=check_dependencies'
                });
                
                const text = await response.text();
                const data = JSON.parse(text);
                
                if (data.success) {
                    let html = '<h4>Event Dependencies:</h4>';
                    data.events.forEach(event => {
                        html += `<div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <h5>Event: ${event.name} (ID: ${event.id})</h5>
                            <ul>
                                <li>Event Sports: ${event.event_sports_count}</li>
                                <li>Registrations: ${event.registrations_count}</li>
                                <li>Tournaments: ${event.tournaments_count}</li>
                                <li>Matches: ${event.matches_count}</li>
                            </ul>
                            ${event.can_delete ? '<span style="color: green;">✅ Can be deleted</span>' : '<span style="color: red;">❌ Cannot be deleted (has dependencies)</span>'}
                        </div>`;
                    });
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">Error: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testCSRFToken() {
            const resultDiv = document.getElementById('csrf-test');
            resultDiv.innerHTML = '<p>Testing CSRF token generation...</p>';
            
            try {
                const response = await fetch('../includes/get_csrf_token.php');
                const text = await response.text();
                const data = JSON.parse(text);
                
                if (data.token) {
                    resultDiv.innerHTML = `<div class="success">✅ CSRF Token Generated Successfully</div>
                        <p><strong>Token:</strong> ${data.token}</p>
                        <p><strong>Length:</strong> ${data.token.length} characters</p>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to generate CSRF token</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function createTestEvent() {
            const resultDiv = document.getElementById('create-test');
            resultDiv.innerHTML = '<p>Creating test event...</p>';
            
            try {
                // Get CSRF token first
                const tokenResponse = await fetch('../includes/get_csrf_token.php');
                const tokenData = await tokenResponse.json();
                
                const formData = new FormData();
                formData.append('entity', 'event');
                formData.append('action', 'create');
                formData.append('name', 'TEST DELETE EVENT ' + Date.now());
                formData.append('description', 'Test event for deletion testing');
                formData.append('start_date', '2025-01-01');
                formData.append('end_date', '2025-01-02');
                formData.append('location', 'Test Location');
                formData.append('status', 'upcoming');
                formData.append('csrf_token', tokenData.token);
                
                const response = await fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                const data = JSON.parse(text);
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Test event created successfully</div>
                        <p><strong>Event ID:</strong> ${data.id}</p>
                        <p><strong>Message:</strong> ${data.message}</p>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to create test event: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testEventDeletion() {
            const resultDiv = document.getElementById('deletion-test');
            resultDiv.innerHTML = '<p>Testing event deletion...</p>';
            
            try {
                // First, create a test event
                const tokenResponse = await fetch('../includes/get_csrf_token.php');
                const tokenData = await tokenResponse.json();
                
                // Create test event
                const createFormData = new FormData();
                createFormData.append('entity', 'event');
                createFormData.append('action', 'create');
                createFormData.append('name', 'DELETE TEST EVENT ' + Date.now());
                createFormData.append('description', 'Event created for deletion testing');
                createFormData.append('start_date', '2025-01-01');
                createFormData.append('end_date', '2025-01-02');
                createFormData.append('location', 'Test Location');
                createFormData.append('status', 'upcoming');
                createFormData.append('csrf_token', tokenData.token);
                
                const createResponse = await fetch('ajax/modal-handler.php', {
                    method: 'POST',
                    body: createFormData
                });
                
                const createData = await JSON.parse(await createResponse.text());
                
                if (createData.success) {
                    resultDiv.innerHTML += `<div class="info">✅ Test event created (ID: ${createData.id})</div>`;
                    
                    // Now test deletion with CSRF token
                    const deleteFormData = new FormData();
                    deleteFormData.append('entity', 'event');
                    deleteFormData.append('action', 'delete');
                    deleteFormData.append('id', createData.id);
                    deleteFormData.append('csrf_token', tokenData.token);
                    
                    const deleteResponse = await fetch('ajax/modal-handler.php', {
                        method: 'POST',
                        body: deleteFormData
                    });
                    
                    const deleteText = await deleteResponse.text();
                    const deleteData = JSON.parse(deleteText);
                    
                    if (deleteData.success) {
                        resultDiv.innerHTML += `<div class="success">✅ Event deletion successful: ${deleteData.message}</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="error">❌ Event deletion failed: ${deleteData.message}</div>`;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to create test event: ${createData.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        // Load initial analysis
        document.addEventListener('DOMContentLoaded', function() {
            analyzeConstraints();
            checkEventDependencies();
        });
    </script>
</body>
</html>

<?php
// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'analyze_constraints':
                $sql = "
                    SELECT 
                        TABLE_NAME,
                        COLUMN_NAME,
                        REFERENCED_TABLE_NAME,
                        REFERENCED_COLUMN_NAME,
                        DELETE_RULE,
                        UPDATE_RULE
                    FROM information_schema.KEY_COLUMN_USAGE 
                    WHERE REFERENCED_TABLE_SCHEMA = DATABASE()
                    AND REFERENCED_TABLE_NAME = 'events'
                    ORDER BY TABLE_NAME, COLUMN_NAME
                ";
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo json_encode([
                    'success' => true,
                    'constraints' => $constraints
                ]);
                break;
                
            case 'check_dependencies':
                $sql = "
                    SELECT 
                        e.id,
                        e.name,
                        e.status,
                        (SELECT COUNT(*) FROM event_sports es WHERE es.event_id = e.id) as event_sports_count,
                        (SELECT COUNT(*) FROM registrations r 
                         JOIN event_sports es ON r.event_sport_id = es.id 
                         WHERE es.event_id = e.id) as registrations_count,
                        (SELECT COUNT(*) FROM tournaments t 
                         JOIN event_sports es ON t.event_sport_id = es.id 
                         WHERE es.event_id = e.id) as tournaments_count,
                        (SELECT COUNT(*) FROM matches m 
                         JOIN event_sports es ON m.event_sport_id = es.id 
                         WHERE es.event_id = e.id) as matches_count
                    FROM events e
                    ORDER BY e.id
                ";
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Determine if each event can be deleted
                foreach ($events as &$event) {
                    $event['can_delete'] = ($event['event_sports_count'] == 0 && 
                                          $event['registrations_count'] == 0 && 
                                          $event['tournaments_count'] == 0 && 
                                          $event['matches_count'] == 0);
                }
                
                echo json_encode([
                    'success' => true,
                    'events' => $events
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'Invalid action'
                ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}
?>
