/**
 * Tab System for SC_IMS Event Management
 * External JavaScript file to avoid conflicts
 */

console.log('=== TAB SYSTEM EXTERNAL FILE LOADING ===');

// Define showTab function in global scope immediately
window.showTab = function(tabName, clickedButton) {
    console.log('=== showTab called ===', tabName, clickedButton);

    try {
        // Hide all tab contents
        const allContents = document.querySelectorAll('.tab-content');
        console.log('Found tab contents:', allContents.length);
        allContents.forEach(content => {
            content.classList.remove('active');
            console.log('Hiding:', content.id);
        });

        // Remove active class from all tab buttons
        const allButtons = document.querySelectorAll('.tab-button');
        console.log('Found tab buttons:', allButtons.length);
        allButtons.forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab content
        const targetTab = document.getElementById(tabName + '-tab');
        if (targetTab) {
            targetTab.classList.add('active');
            console.log('SUCCESS: Showing tab:', targetTab.id);
        } else {
            console.error('ERROR: Tab not found:', tabName + '-tab');
            return false;
        }

        // Add active class to clicked button
        if (clickedButton) {
            clickedButton.classList.add('active');
            console.log('SUCCESS: Activated button');
        } else {
            // Fallback: find the button by data-tab attribute
            const button = document.querySelector(`[data-tab="${tabName}"]`);
            if (button) {
                button.classList.add('active');
                console.log('SUCCESS: Found and activated button by data-tab');
            }
        }
        
        // Save to localStorage
        localStorage.setItem('activeEventTab', tabName);
        console.log('SUCCESS: Tab switch completed');
        return true;
        
    } catch (error) {
        console.error('ERROR in showTab:', error);
        return false;
    }
};

// Also create a legacy alias
function showTab(tabName, clickedButton) {
    return window.showTab(tabName, clickedButton);
}

// Test functions for debugging - GLOBAL SCOPE
window.testTabClick = function(tabName) {
    console.log('=== testTabClick called ===', tabName);
    try {
        const button = document.querySelector(`[data-tab="${tabName}"]`);
        console.log('Found button:', button);
        return window.showTab(tabName, button);
    } catch (error) {
        console.error('ERROR in testTabClick:', error);
        return false;
    }
};

window.testAllTabs = function() {
    console.log('=== testAllTabs called ===');
    try {
        const tabs = ['standings', 'sports', 'registrations', 'matches'];
        let index = 0;
        
        function testNext() {
            if (index < tabs.length) {
                console.log(`Testing tab ${index + 1}: ${tabs[index]}`);
                const button = document.querySelector(`[data-tab="${tabs[index]}"]`);
                window.showTab(tabs[index], button);
                index++;
                setTimeout(testNext, 1000);
            } else {
                console.log('All tabs tested successfully');
            }
        }

        testNext();
    } catch (error) {
        console.error('ERROR in testAllTabs:', error);
    }
};

// Initialize tabs when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM ready, initializing tabs');
    
    // Restore saved tab or use default
    const savedTab = localStorage.getItem('activeEventTab');
    if (savedTab && document.getElementById(savedTab + '-tab')) {
        console.log('Restoring saved tab:', savedTab);
        const button = document.querySelector(`[data-tab="${savedTab}"]`);
        window.showTab(savedTab, button);
    } else {
        console.log('Using default tab: standings');
        const button = document.querySelector(`[data-tab="standings"]`);
        if (button) {
            window.showTab('standings', button);
        }
    }
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key >= '1' && e.key <= '4') {
            e.preventDefault();
            const tabMap = {
                '1': 'standings',
                '2': 'sports', 
                '3': 'registrations',
                '4': 'matches'
            };
            const tabName = tabMap[e.key];
            if (tabName) {
                const button = document.querySelector(`[data-tab="${tabName}"]`);
                window.showTab(tabName, button);
            }
        }
    });
});

console.log('=== TAB SYSTEM EXTERNAL FILE LOADED ===');
console.log('showTab type:', typeof window.showTab);
console.log('testTabClick type:', typeof window.testTabClick);
console.log('testAllTabs type:', typeof window.testAllTabs);
