<?php
/**
 * Verify Tournament Formats Database Setup
 */

require_once '../config/database.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Formats Verification</h2>";

try {
    // Check if table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if (!$table_exists) {
        echo "<p style='color: red;'>❌ tournament_formats table does not exist!</p>";
        echo "<p>Running database setup...</p>";
        
        // Initialize database
        $database->initializeDatabase();
        echo "<p style='color: green;'>✓ Database initialized</p>";
    } else {
        echo "<p style='color: green;'>✓ tournament_formats table exists</p>";
    }
    
    // Check table structure
    echo "<h3>Table Structure:</h3>";
    $stmt = $conn->prepare("DESCRIBE tournament_formats");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check current data
    echo "<h3>Current Data:</h3>";
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    if (empty($formats)) {
        echo "<p style='color: red;'>❌ No tournament formats found!</p>";
        echo "<p>Adding default formats...</p>";
        
        // Add default formats
        $default_formats = [
            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null],
            ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null],
            ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16],
            ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null],
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null],
            ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null]
        ];
        
        foreach ($default_formats as $format) {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute($format);
            echo "<p style='color: green;'>✓ Added: {$format[0]}</p>";
        }
        
        // Re-fetch data
        $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
        $stmt->execute();
        $formats = $stmt->fetchAll();
    }
    
    echo "<p style='color: green;'>✓ Found " . count($formats) . " tournament formats</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Types</th><th>Min Participants</th><th>Max Participants</th></tr>";
    foreach ($formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>{$format['sport_types']}</td>";
        echo "<td>{$format['min_participants']}</td>";
        echo "<td>" . ($format['max_participants'] ?: 'Unlimited') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test the query logic
    echo "<h3>Testing Query Logic:</h3>";
    
    $test_cases = [
        'traditional' => ['team', 'individual'],
        'team' => ['team'],
        'individual' => ['individual'],
        'academic' => ['academic'],
        'judged' => ['judged'],
        'performance' => ['performance']
    ];
    
    foreach ($test_cases as $sport_type => $mapped_types) {
        echo "<h4>Testing: {$sport_type} → " . implode(', ', $mapped_types) . "</h4>";
        
        // Build WHERE clause
        $where_conditions = [];
        $params = [];
        
        foreach ($mapped_types as $type) {
            $where_conditions[] = "sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?";
            $params[] = $type . ',%';
            $params[] = '%,' . $type . ',%';
            $params[] = '%,' . $type;
            $params[] = $type;
        }
        
        $where_clause = '(' . implode(') OR (', $where_conditions) . ')';
        
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_types
            FROM tournament_formats 
            WHERE {$where_clause}
            ORDER BY name
        ");
        
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        if (empty($results)) {
            echo "<p style='color: red;'>❌ No formats found</p>";
        } else {
            echo "<p style='color: green;'>✓ Found " . count($results) . " formats:</p>";
            echo "<ul>";
            foreach ($results as $result) {
                echo "<li><strong>{$result['name']}</strong> (Code: {$result['code']}, Types: {$result['sport_types']})</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
