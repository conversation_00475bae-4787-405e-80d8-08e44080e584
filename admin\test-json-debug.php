<?php
/**
 * Test JSON Response Integrity
 */

// Start output buffering to prevent any unwanted output
ob_start();

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Clean any output that might have been generated
ob_clean();

// Set JSON response header
header('Content-Type: application/json');

// Get database connection
$database = new Database();
$conn = $database->getConnection();

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'test_csrf':
                // Test CSRF token validation
                if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Invalid security token. Please refresh the page and try again.'
                    ]);
                    exit;
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'CSRF token validation successful'
                ]);
                break;
                
            case 'test_event_deletion':
                // Test event deletion logic without actually deleting
                $eventId = $_POST['event_id'] ?? 0;
                
                if (!$eventId) {
                    throw new Exception('Event ID is required');
                }
                
                // Check if event exists
                $stmt = $conn->prepare("SELECT name FROM events WHERE id = ?");
                $stmt->execute([$eventId]);
                $event = $stmt->fetch();
                
                if (!$event) {
                    throw new Exception('Event not found');
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Event deletion test successful',
                    'event_name' => $event['name'],
                    'event_id' => $eventId
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'Invalid action'
                ]);
        }
    } else {
        // Clean output buffer for HTML response
        ob_clean();
        header('Content-Type: text/html');
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>JSON Response Test</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
                .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
                button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
                button:hover { background: #0056b3; }
                pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
            </style>
        </head>
        <body>
            <h1>JSON Response Test</h1>
            
            <div class="section">
                <h3>1. Test Raw Response</h3>
                <div id="raw-result"></div>
                <button onclick="testRawResponse()">Test Raw Response</button>
            </div>

            <script>
                async function testRawResponse() {
                    const resultDiv = document.getElementById('raw-result');
                    resultDiv.innerHTML = '<p>Testing raw response...</p>';
                    
                    try {
                        const response = await fetch('ajax/modal-handler.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                            body: 'entity=invalid&action=test'
                        });
                        
                        const responseText = await response.text();
                        console.log('Raw modal-handler response:', responseText);
                        
                        resultDiv.innerHTML = `<div class="success">Raw response received:</div><pre>${responseText}</pre>`;
                        
                        try {
                            const data = JSON.parse(responseText);
                            resultDiv.innerHTML += `<div class="success">✅ Valid JSON parsed</div>`;
                        } catch (parseError) {
                            resultDiv.innerHTML += `<div class="error">❌ JSON parse error: ${parseError.message}</div>`;
                        }
                    } catch (error) {
                        resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                        console.error('Raw response test error:', error);
                    }
                }
            </script>
        </body>
        </html>
        <?php
    }
} catch (Exception $e) {
    // Clean any output that might have been generated
    ob_clean();
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Ensure clean output
ob_end_flush();
?>
