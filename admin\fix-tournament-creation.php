<?php
/**
 * Fix Tournament Creation for Badminton
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Tournament Creation for Badminton</h1>";
echo "<p>This will diagnose and fix the registration status issues preventing tournament creation...</p>";

$badminton_event_sport_id = 18;
$fixed = false;

if (isset($_POST['fix_registrations'])) {
    echo "<h2>🔄 Fixing Registration Status...</h2>";
    
    try {
        $conn->beginTransaction();
        
        // Fix unified registration system
        $stmt = $conn->prepare("
            UPDATE department_sport_participations dsp
            JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
            SET dsp.status = 'confirmed', edr.status = 'approved'
            WHERE dsp.event_sport_id = ? AND dsp.status != 'confirmed'
        ");
        $stmt->execute([$badminton_event_sport_id]);
        $unified_fixed = $stmt->rowCount();
        
        // Fix old registration system as backup
        $stmt = $conn->prepare("
            UPDATE registrations 
            SET status = 'confirmed' 
            WHERE event_sport_id = ? AND status NOT IN ('confirmed', 'approved')
        ");
        $stmt->execute([$badminton_event_sport_id]);
        $old_fixed = $stmt->rowCount();
        
        $conn->commit();
        
        echo "<p style='color: green;'>✅ Fixed {$unified_fixed} unified registrations</p>";
        echo "<p style='color: green;'>✅ Fixed {$old_fixed} old system registrations</p>";
        
        $fixed = true;
        
    } catch (Exception $e) {
        $conn->rollBack();
        echo "<p style='color: red;'>❌ Error fixing registrations: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>📊 Current Registration Status</h2>";

// Check unified system
$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        dsp.status,
        dsp.team_name,
        d.name as department_name,
        edr.status as registration_status
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ?
    ORDER BY d.name
");
$stmt->execute([$badminton_event_sport_id]);
$unified_registrations = $stmt->fetchAll();

echo "<h3>Unified Registration System:</h3>";
if (empty($unified_registrations)) {
    echo "<p style='color: orange;'>⚠ No registrations found in unified system</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>Department</th><th>Team Name</th><th>Sport Status</th><th>Registration Status</th>";
    echo "</tr>";
    
    $confirmed_count = 0;
    foreach ($unified_registrations as $reg) {
        $status_color = ($reg['status'] == 'confirmed') ? 'green' : 'red';
        if ($reg['status'] == 'confirmed') $confirmed_count++;
        
        echo "<tr>";
        echo "<td>{$reg['department_name']}</td>";
        echo "<td>" . ($reg['team_name'] ?: 'Default') . "</td>";
        echo "<td style='color: {$status_color}; font-weight: bold;'>{$reg['status']}</td>";
        echo "<td>{$reg['registration_status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><strong>Confirmed registrations:</strong> <span style='color: " . ($confirmed_count >= 2 ? 'green' : 'red') . ";'>{$confirmed_count}</span></p>";
}

// Test tournament creation query
echo "<h2>🧪 Tournament Creation Test</h2>";
$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        COALESCE(dsp.team_name, d.name) as team_name,
        edr.department_id,
        d.name as department_name,
        dsp.status
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
");
$stmt->execute([$badminton_event_sport_id]);
$tournament_participants = $stmt->fetchAll();

echo "<p><strong>Tournament Creation Query Result:</strong></p>";
if (count($tournament_participants) >= 2) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ SUCCESS! Found " . count($tournament_participants) . " participants for tournament creation!</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #d4edda;'><th>Team Name</th><th>Department</th><th>Status</th></tr>";
    foreach ($tournament_participants as $p) {
        echo "<tr><td>{$p['team_name']}</td><td>{$p['department_name']}</td><td>{$p['status']}</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>🎯 Ready to Create Tournament!</h2>";
    echo "<p><a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 18px; font-weight: bold;'>🏆 Go Create Badminton Tournament</a></p>";
    
} else {
    echo "<p style='color: red; font-size: 18px; font-weight: bold;'>❌ PROBLEM: Only found " . count($tournament_participants) . " participants (need at least 2)</p>";
    
    if (!$fixed && !empty($unified_registrations)) {
        echo "<h3>🔧 Fix Required</h3>";
        echo "<p>The registrations exist but have wrong status. Click below to fix:</p>";
        echo "<form method='POST' style='margin: 20px 0;'>";
        echo "<button type='submit' name='fix_registrations' value='1' style='background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; font-weight: bold;'>🔧 Fix Registration Status Now</button>";
        echo "</form>";
    } else if (empty($unified_registrations)) {
        echo "<p style='color: red;'>No registrations found. You need to create registrations first.</p>";
        echo "<p><a href='quick-registration-fix.php?event_sport_id={$badminton_event_sport_id}' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Create Registrations</a></p>";
    }
}

echo "<h2>📋 Technical Details</h2>";
echo "<details>";
echo "<summary>Click to see technical information</summary>";
echo "<ul>";
echo "<li><strong>Event Sport ID:</strong> {$badminton_event_sport_id}</li>";
echo "<li><strong>Required Status:</strong> dsp.status = 'confirmed' AND edr.status IN ('approved', 'pending')</li>";
echo "<li><strong>Tournament Creation URL:</strong> manage-category.php?event_id=3&sport_id=40&category_id=2</li>";
echo "<li><strong>Query Used:</strong> Unified registration system with fallback to old system</li>";
echo "</ul>";
echo "</details>";

if ($fixed) {
    echo "<script>";
    echo "setTimeout(function() {";
    echo "  if (confirm('Registration status has been fixed! Would you like to go to the tournament creation page now?')) {";
    echo "    window.location.href = 'manage-category.php?event_id=3&sport_id=40&category_id=2';";
    echo "  }";
    echo "}, 2000);";
    echo "</script>";
}
?>
