<?php
/**
 * Check Event Sports Status
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Check Event Sports Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
    </style>
</head>
<body>
    <h1>Event Sports Status Check</h1>
    
    <div class="section info">
        <h3>Event 1 Current Sports</h3>
        <?php
        $stmt = $conn->prepare("
            SELECT es.*, s.name as sport_name, s.type as sport_type, tf.name as format_name
            FROM event_sports es 
            LEFT JOIN sports s ON es.sport_id = s.id 
            LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
            WHERE es.event_id = 1
            ORDER BY es.id
        ");
        $stmt->execute();
        $event_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($event_sports)) {
            echo '<p>No sports currently added to Event 1.</p>';
        } else {
            echo '<table>';
            echo '<tr><th>ID</th><th>Sport Name</th><th>Sport Type</th><th>Tournament Format</th><th>Status</th><th>Created</th><th>Action</th></tr>';
            foreach ($event_sports as $sport) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($sport['id']) . '</td>';
                echo '<td>' . htmlspecialchars($sport['sport_name'] ?? 'Unknown') . '</td>';
                echo '<td>' . htmlspecialchars($sport['sport_type'] ?? 'Unknown') . '</td>';
                echo '<td>' . htmlspecialchars($sport['format_name'] ?? 'Unknown') . '</td>';
                echo '<td>' . htmlspecialchars($sport['status']) . '</td>';
                echo '<td>' . htmlspecialchars($sport['created_at']) . '</td>';
                echo '<td><button class="btn-danger" onclick="removeSport(' . $sport['id'] . ')">Remove</button></td>';
                echo '</tr>';
            }
            echo '</table>';
        }
        ?>
    </div>
    
    <div class="section">
        <h3>All Available Sports</h3>
        <?php
        $stmt = $conn->prepare("SELECT id, name, type FROM sports ORDER BY name");
        $stmt->execute();
        $all_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo '<table>';
        echo '<tr><th>ID</th><th>Sport Name</th><th>Type</th><th>In Event 1?</th></tr>';
        foreach ($all_sports as $sport) {
            // Check if this sport is in Event 1
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = 1 AND sport_id = ?");
            $stmt->execute([$sport['id']]);
            $in_event = $stmt->fetch();
            
            echo '<tr>';
            echo '<td>' . htmlspecialchars($sport['id']) . '</td>';
            echo '<td>' . htmlspecialchars($sport['name']) . '</td>';
            echo '<td>' . htmlspecialchars($sport['type']) . '</td>';
            echo '<td>' . ($in_event ? '✅ Yes' : '❌ No') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        ?>
    </div>
    
    <div class="section">
        <h3>Database Cleanup Actions</h3>
        <button class="btn-danger" onclick="clearAllEventSports()">Clear All Sports from Event 1</button>
        <button class="btn-success" onclick="refreshPage()">Refresh Page</button>
        <div id="action-result"></div>
    </div>

    <script>
        async function removeSport(eventSportId) {
            if (!confirm('Are you sure you want to remove this sport from Event 1?')) {
                return;
            }
            
            try {
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=remove_sport&event_sport_id=${eventSportId}&csrf_token=<?php echo generateCSRFToken(); ?>`
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        alert('Sport removed successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                } catch (e) {
                    alert('JSON parse error: ' + e.message);
                    console.log('Raw response:', text);
                }
            } catch (error) {
                alert('Network error: ' + error.message);
            }
        }
        
        async function clearAllEventSports() {
            if (!confirm('Are you sure you want to remove ALL sports from Event 1? This cannot be undone!')) {
                return;
            }
            
            const resultDiv = document.getElementById('action-result');
            resultDiv.innerHTML = '<p>Clearing all sports...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=clear_event_sports&event_id=1'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = '<p style="color: green;">All sports cleared successfully!</p>';
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        resultDiv.innerHTML = '<p style="color: red;">Error: ' + data.message + '</p>';
                    }
                } catch (e) {
                    resultDiv.innerHTML = '<p style="color: red;">JSON parse error: ' + e.message + '</p>';
                    console.log('Raw response:', text);
                }
            } catch (error) {
                resultDiv.innerHTML = '<p style="color: red;">Network error: ' + error.message + '</p>';
            }
        }
        
        function refreshPage() {
            location.reload();
        }
    </script>
</body>
</html>
