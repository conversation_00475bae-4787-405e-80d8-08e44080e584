<!DOCTYPE html>
<html>
<head>
    <title>Direct AJAX Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Direct AJAX Endpoint Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Direct fetch to AJAX endpoint</h3>
        <button onclick="testAjaxEndpoint('individual')">Test with 'individual' sport type</button>
        <button onclick="testAjaxEndpoint('team')">Test with 'team' sport type</button>
        <button onclick="testAjaxEndpoint('academic')">Test with 'academic' sport type</button>
        <button onclick="testAjaxEndpoint('traditional')">Test with 'traditional' sport type</button>
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Check if endpoint is accessible</h3>
        <button onclick="checkEndpointAccess()">Check Endpoint Access</button>
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Simulate exact modal behavior</h3>
        <select id="test-sport-select" onchange="simulateModalBehavior()">
            <option value="">Select a sport...</option>
            <option value="1" data-type="individual">Art Exhibition (Individual)</option>
            <option value="2" data-type="team">Basketball (Team)</option>
            <option value="3" data-type="academic">Chess (Academic)</option>
            <option value="4" data-type="judged">Singing Contest (Judged)</option>
        </select>
        <select id="test-format-select">
            <option value="">Select a sport first...</option>
        </select>
        <div id="test3-result"></div>
    </div>

    <script>
        function testAjaxEndpoint(sportType) {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.innerHTML = `<p class="info">Testing with sport type: ${sportType}</p>`;
            
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => {
                resultDiv.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                resultDiv.innerHTML += `<p class="info">Response headers: ${JSON.stringify([...response.headers])}</p>`;
                return response.text();
            })
            .then(text => {
                resultDiv.innerHTML += `<p class="info">Raw response:</p><pre>${text}</pre>`;
                
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML += `<p class="success">✓ Valid JSON response</p>`;
                    
                    if (data.success) {
                        resultDiv.innerHTML += `<p class="success">✓ Success: Found ${data.formats.length} formats</p>`;
                        if (data.formats.length > 0) {
                            resultDiv.innerHTML += '<ul>';
                            data.formats.forEach(format => {
                                resultDiv.innerHTML += `<li>${format.name} (ID: ${format.id})</li>`;
                            });
                            resultDiv.innerHTML += '</ul>';
                        }
                    } else {
                        resultDiv.innerHTML += `<p class="error">❌ Error: ${data.message}</p>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML += `<p class="error">❌ JSON parse error: ${e.message}</p>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML += `<p class="error">❌ Fetch error: ${error.message}</p>`;
            });
        }
        
        function checkEndpointAccess() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.innerHTML = '<p class="info">Checking endpoint accessibility...</p>';
            
            fetch('ajax/get-tournament-formats.php', {
                method: 'GET'
            })
            .then(response => {
                resultDiv.innerHTML += `<p class="info">GET request status: ${response.status}</p>`;
                return response.text();
            })
            .then(text => {
                resultDiv.innerHTML += `<p class="info">GET response:</p><pre>${text.substring(0, 500)}${text.length > 500 ? '...' : ''}</pre>`;
            })
            .catch(error => {
                resultDiv.innerHTML += `<p class="error">❌ Endpoint not accessible: ${error.message}</p>`;
            });
        }
        
        function simulateModalBehavior() {
            const sportSelect = document.getElementById('test-sport-select');
            const formatSelect = document.getElementById('test-format-select');
            const resultDiv = document.getElementById('test3-result');
            
            const sportId = sportSelect.value;
            
            resultDiv.innerHTML = '<p class="info">Simulating modal behavior...</p>';
            
            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            
            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                resultDiv.innerHTML += '<p class="error">No sport selected</p>';
                return;
            }
            
            // Get sport type from the selected option
            const selectedSportOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedSportOption.dataset.type || 'traditional';
            const sportName = selectedSportOption.textContent;
            
            resultDiv.innerHTML += `<p class="info">Selected sport: ${sportName}</p>`;
            resultDiv.innerHTML += `<p class="info">Sport type: ${sportType}</p>`;
            
            // Fetch tournament formats from database
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => {
                resultDiv.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                return response.text();
            })
            .then(text => {
                resultDiv.innerHTML += `<p class="info">Raw response:</p><pre>${text}</pre>`;
                
                try {
                    const data = JSON.parse(text);
                    formatSelect.innerHTML = '<option value="">Select tournament format...</option>';

                    if (data.success && data.formats) {
                        resultDiv.innerHTML += `<p class="success">✓ Found ${data.formats.length} formats</p>`;
                        
                        data.formats.forEach(format => {
                            const option = document.createElement('option');
                            option.value = format.id;
                            option.textContent = format.name;
                            option.dataset.description = format.description;
                            option.dataset.minParticipants = format.min_participants;
                            option.dataset.maxParticipants = format.max_participants || 'Unlimited';
                            option.dataset.seedingRequired = format.requires_seeding;
                            option.dataset.advancementType = format.advancement_type;
                            formatSelect.appendChild(option);
                            
                            resultDiv.innerHTML += `<p class="success">Added format: ${format.name} (ID: ${format.id})</p>`;
                        });
                    } else {
                        formatSelect.innerHTML = '<option value="">No formats available</option>';
                        resultDiv.innerHTML += `<p class="error">No formats found or error: ${data.message || 'Unknown error'}</p>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML += `<p class="error">JSON parse error: ${e.message}</p>`;
                    formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                }
            })
            .catch(error => {
                console.error('Error loading tournament formats:', error);
                resultDiv.innerHTML += `<p class="error">Fetch error: ${error.message}</p>`;
                formatSelect.innerHTML = '<option value="">Error loading formats</option>';
            });
        }
    </script>
</body>
</html>
