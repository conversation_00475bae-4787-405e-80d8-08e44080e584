<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .tab-button {
            padding: 10px 20px;
            margin: 5px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            cursor: pointer;
            display: inline-block;
        }
        .tab-button.active {
            background: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            margin-top: 10px;
        }
        .tab-content.active {
            display: block;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Tab Debug Test</h1>
    
    <div class="debug-info" id="debugInfo">
        Debug info will appear here...
    </div>
    
    <div class="tab-buttons">
        <button class="tab-button active" data-tab="standings" onclick="showTab('standings', this)">
            Standings
        </button>
        <button class="tab-button" data-tab="sports" onclick="showTab('sports', this)">
            Sports
        </button>
        <button class="tab-button" data-tab="registrations" onclick="showTab('registrations', this)">
            Registrations
        </button>
        <button class="tab-button" data-tab="matches" onclick="showTab('matches', this)">
            Matches
        </button>
    </div>
    
    <div id="standings-tab" class="tab-content active">
        <h2>Standings Content</h2>
        <p>This is the standings tab content.</p>
    </div>
    
    <div id="sports-tab" class="tab-content">
        <h2>Sports Content</h2>
        <p>This is the sports tab content.</p>
    </div>
    
    <div id="registrations-tab" class="tab-content">
        <h2>Registrations Content</h2>
        <p>This is the registrations tab content.</p>
    </div>
    
    <div id="matches-tab" class="tab-content">
        <h2>Matches Content</h2>
        <p>This is the matches tab content.</p>
    </div>
    
    <script>
        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}<br>`;
            console.log(message);
        }
        
        log('Script loaded');
        
        function showTab(tabName, clickedButton) {
            log(`showTab called with: ${tabName}, button: ${clickedButton ? clickedButton.textContent : 'null'}`);

            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
                log(`Hiding tab content: ${content.id}`);
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                log(`Activated tab: ${tabName}-tab`);
            } else {
                log(`ERROR: Tab not found: ${tabName}-tab`);
            }

            // Add active class to clicked button
            if (clickedButton) {
                clickedButton.classList.add('active');
                log(`Activated button: ${clickedButton.textContent}`);
            } else {
                log('WARNING: No clicked button provided');
            }
        }
        
        // Test when document is ready
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded');
            
            const tabButtons = document.querySelectorAll('.tab-button');
            log(`Found ${tabButtons.length} tab buttons`);
            
            const tabContents = document.querySelectorAll('.tab-content');
            log(`Found ${tabContents.length} tab contents`);
            
            // Test automatic tab switching
            setTimeout(() => {
                log('Testing automatic tab switching...');
                showTab('sports', document.querySelector('[data-tab="sports"]'));
            }, 2000);
            
            setTimeout(() => {
                log('Switching back to standings...');
                showTab('standings', document.querySelector('[data-tab="standings"]'));
            }, 4000);
        });
        
        // Test if onclick works
        window.testOnClick = function() {
            log('Testing onclick manually...');
            const button = document.querySelector('[data-tab="registrations"]');
            if (button) {
                button.click();
            }
        };
        
        log('Script setup complete');
    </script>
</body>
</html>
