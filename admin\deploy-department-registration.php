<?php
/**
 * Department-Centric Registration System Deployment
 * Safely deploys the new database schema for unified department registration
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$deployment_log = [];
$errors = [];

function logStep($message, $success = true) {
    global $deployment_log;
    $deployment_log[] = [
        'message' => $message,
        'success' => $success,
        'timestamp' => date('Y-m-d H:i:s')
    ];
}

function executeSQL($conn, $sql, $description) {
    global $errors;
    try {
        $conn->exec($sql);
        logStep("✅ $description");
        return true;
    } catch (Exception $e) {
        $error_msg = "❌ $description - Error: " . $e->getMessage();
        logStep($error_msg, false);
        $errors[] = $error_msg;
        return false;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['deploy'])) {
    try {
        $conn->beginTransaction();
        
        logStep("🚀 Starting Department-Centric Registration System Deployment");
        
        // 1. Create event_department_registrations table
        $sql = "CREATE TABLE IF NOT EXISTS event_department_registrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_id INT NOT NULL,
            department_id INT NOT NULL,
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('pending', 'approved', 'rejected', 'withdrawn') DEFAULT 'pending',
            contact_person VARCHAR(255),
            contact_email VARCHAR(255),
            contact_phone VARCHAR(20),
            notes TEXT,
            total_participants INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
            FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
            UNIQUE KEY unique_event_department (event_id, department_id)
        )";
        executeSQL($conn, $sql, "Created event_department_registrations table");
        
        // 2. Create department_sport_participations table
        $sql = "CREATE TABLE IF NOT EXISTS department_sport_participations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_department_registration_id INT NOT NULL,
            event_sport_id INT NOT NULL,
            team_name VARCHAR(255),
            participants JSON,
            participation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status ENUM('registered', 'confirmed', 'withdrawn') DEFAULT 'registered',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_department_registration_id) REFERENCES event_department_registrations(id) ON DELETE CASCADE,
            FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
            UNIQUE KEY unique_dept_sport_participation (event_department_registration_id, event_sport_id)
        )";
        executeSQL($conn, $sql, "Created department_sport_participations table");
        
        // 3. Create department_overall_scores table
        $sql = "CREATE TABLE IF NOT EXISTS department_overall_scores (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_department_registration_id INT NOT NULL,
            sport_id INT NOT NULL,
            position INT,
            points_earned DECIMAL(10,2) DEFAULT 0,
            bonus_points DECIMAL(10,2) DEFAULT 0,
            penalty_points DECIMAL(10,2) DEFAULT 0,
            final_score DECIMAL(10,2) GENERATED ALWAYS AS (points_earned + bonus_points - penalty_points) STORED,
            performance_data JSON,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_department_registration_id) REFERENCES event_department_registrations(id) ON DELETE CASCADE,
            FOREIGN KEY (sport_id) REFERENCES sports(id) ON DELETE CASCADE,
            UNIQUE KEY unique_dept_sport_score (event_department_registration_id, sport_id)
        )";
        executeSQL($conn, $sql, "Created department_overall_scores table");
        
        // 4. Create event_overall_standings table
        $sql = "CREATE TABLE IF NOT EXISTS event_overall_standings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_id INT NOT NULL,
            department_id INT NOT NULL,
            total_points DECIMAL(10,2) DEFAULT 0,
            sports_participated INT DEFAULT 0,
            sports_won INT DEFAULT 0,
            sports_podium INT DEFAULT 0,
            average_position DECIMAL(5,2),
            overall_rank INT,
            is_overall_winner BOOLEAN DEFAULT FALSE,
            calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
            FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
            UNIQUE KEY unique_event_dept_standing (event_id, department_id)
        )";
        executeSQL($conn, $sql, "Created event_overall_standings table");
        
        // 5. Create scoring_systems table
        $sql = "CREATE TABLE IF NOT EXISTS scoring_systems (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_id INT NOT NULL,
            name VARCHAR(255) NOT NULL DEFAULT 'Default Scoring',
            description TEXT,
            position_points JSON,
            participation_bonus DECIMAL(5,2) DEFAULT 0,
            winner_bonus DECIMAL(5,2) DEFAULT 0,
            min_sports_required INT DEFAULT 1,
            tie_breaker_method ENUM('total_wins', 'average_position', 'head_to_head', 'manual') DEFAULT 'total_wins',
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        )";
        executeSQL($conn, $sql, "Created scoring_systems table");
        
        // 6. Check if columns already exist before adding them
        $result = $conn->query("SHOW COLUMNS FROM registrations LIKE 'department_sport_participation_id'");
        if ($result->rowCount() == 0) {
            $sql = "ALTER TABLE registrations ADD COLUMN department_sport_participation_id INT NULL";
            executeSQL($conn, $sql, "Added department_sport_participation_id to registrations table");
            
            $sql = "ALTER TABLE registrations ADD FOREIGN KEY (department_sport_participation_id) REFERENCES department_sport_participations(id) ON DELETE SET NULL";
            executeSQL($conn, $sql, "Added foreign key constraint for department_sport_participation_id");
        } else {
            logStep("✅ registrations table already has department_sport_participation_id column");
        }
        
        // 7. Check if events table columns exist
        $result = $conn->query("SHOW COLUMNS FROM events LIKE 'overall_winner_department_id'");
        if ($result->rowCount() == 0) {
            $sql = "ALTER TABLE events ADD COLUMN overall_winner_department_id INT NULL";
            executeSQL($conn, $sql, "Added overall_winner_department_id to events table");
            
            $sql = "ALTER TABLE events ADD COLUMN overall_winner_announced_at TIMESTAMP NULL";
            executeSQL($conn, $sql, "Added overall_winner_announced_at to events table");
            
            $sql = "ALTER TABLE events ADD COLUMN scoring_system_id INT NULL";
            executeSQL($conn, $sql, "Added scoring_system_id to events table");
            
            $sql = "ALTER TABLE events ADD FOREIGN KEY (overall_winner_department_id) REFERENCES departments(id) ON DELETE SET NULL";
            executeSQL($conn, $sql, "Added foreign key for overall_winner_department_id");
            
            $sql = "ALTER TABLE events ADD FOREIGN KEY (scoring_system_id) REFERENCES scoring_systems(id) ON DELETE SET NULL";
            executeSQL($conn, $sql, "Added foreign key for scoring_system_id");
        } else {
            logStep("✅ events table already has overall winner columns");
        }
        
        // 8. Create indexes
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_event_dept_reg_event ON event_department_registrations(event_id)",
            "CREATE INDEX IF NOT EXISTS idx_event_dept_reg_dept ON event_department_registrations(department_id)",
            "CREATE INDEX IF NOT EXISTS idx_dept_sport_part_reg ON department_sport_participations(event_department_registration_id)",
            "CREATE INDEX IF NOT EXISTS idx_dept_sport_part_sport ON department_sport_participations(event_sport_id)",
            "CREATE INDEX IF NOT EXISTS idx_dept_overall_scores_reg ON department_overall_scores(event_department_registration_id)",
            "CREATE INDEX IF NOT EXISTS idx_dept_overall_scores_sport ON department_overall_scores(sport_id)",
            "CREATE INDEX IF NOT EXISTS idx_overall_standings_event ON event_overall_standings(event_id)",
            "CREATE INDEX IF NOT EXISTS idx_overall_standings_dept ON event_overall_standings(department_id)",
            "CREATE INDEX IF NOT EXISTS idx_overall_standings_rank ON event_overall_standings(overall_rank)"
        ];
        
        foreach ($indexes as $index_sql) {
            executeSQL($conn, $index_sql, "Created performance index");
        }
        
        // 9. Insert default scoring systems for existing events
        $sql = "INSERT IGNORE INTO scoring_systems (event_id, name, description, position_points, participation_bonus, winner_bonus)
                SELECT 
                    id as event_id,
                    'Default Scoring System' as name,
                    'Standard point-based scoring: 1st=10pts, 2nd=8pts, 3rd=6pts, 4th=4pts, 5th=2pts, participation=1pt' as description,
                    '{\"1\": 10, \"2\": 8, \"3\": 6, \"4\": 4, \"5\": 2, \"participation\": 1}' as position_points,
                    1.0 as participation_bonus,
                    2.0 as winner_bonus
                FROM events";
        executeSQL($conn, $sql, "Created default scoring systems for existing events");
        
        // 10. Update events to link with scoring systems
        $sql = "UPDATE events e 
                SET scoring_system_id = (
                    SELECT id FROM scoring_systems s 
                    WHERE s.event_id = e.id 
                    AND s.name = 'Default Scoring System' 
                    LIMIT 1
                ) 
                WHERE scoring_system_id IS NULL";
        executeSQL($conn, $sql, "Linked events with default scoring systems");
        
        $conn->commit();
        logStep("🎉 Department-Centric Registration System deployed successfully!");
        
        // Log admin activity
        logAdminActivity('DEPLOY_DEPT_REGISTRATION', 'system', null);
        
    } catch (Exception $e) {
        $conn->rollBack();
        $error_msg = "💥 Deployment failed: " . $e->getMessage();
        logStep($error_msg, false);
        $errors[] = $error_msg;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploy Department Registration System - SC_IMS</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .deployment-container { max-width: 1000px; margin: 20px auto; padding: 20px; }
        .deployment-header { text-align: center; margin-bottom: 30px; }
        .deployment-warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .deployment-log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; max-height: 500px; overflow-y: auto; }
        .log-entry { margin: 5px 0; padding: 8px; border-radius: 4px; font-family: monospace; }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }
        .deploy-button { background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; }
        .deploy-button:hover { background: #218838; }
        .deploy-button:disabled { background: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="deployment-container">
        <div class="deployment-header">
            <h1><i class="fas fa-database"></i> Deploy Department-Centric Registration System</h1>
            <p>This will upgrade your SC_IMS to support unified department registration with multi-sport participation and overall winner calculation.</p>
        </div>
        
        <?php if (empty($deployment_log)): ?>
        <div class="deployment-warning">
            <h3><i class="fas fa-exclamation-triangle"></i> Important Notice</h3>
            <ul>
                <li><strong>Backup Recommended:</strong> This deployment will modify your database schema</li>
                <li><strong>Existing Data:</strong> All current registrations will be preserved</li>
                <li><strong>New Features:</strong> Unified department registration, multi-sport participation, overall winner calculation</li>
                <li><strong>Compatibility:</strong> Existing sport-specific functionality will remain intact</li>
            </ul>
        </div>
        
        <form method="POST" style="text-align: center;">
            <button type="submit" name="deploy" class="deploy-button">
                <i class="fas fa-rocket"></i> Deploy Department Registration System
            </button>
        </form>
        <?php endif; ?>
        
        <?php if (!empty($deployment_log)): ?>
        <div class="deployment-log">
            <h3><i class="fas fa-list"></i> Deployment Log</h3>
            <?php foreach ($deployment_log as $entry): ?>
                <div class="log-entry <?php echo $entry['success'] ? 'log-success' : 'log-error'; ?>">
                    [<?php echo $entry['timestamp']; ?>] <?php echo htmlspecialchars($entry['message']); ?>
                </div>
            <?php endforeach; ?>
            
            <?php if (empty($errors)): ?>
                <div style="text-align: center; margin-top: 20px;">
                    <a href="events.php" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> Continue to Event Management
                    </a>
                </div>
            <?php else: ?>
                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="location.reload()" class="btn btn-warning">
                        <i class="fas fa-redo"></i> Retry Deployment
                    </button>
                </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
