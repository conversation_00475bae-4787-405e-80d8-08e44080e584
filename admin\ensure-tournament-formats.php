<?php
/**
 * Ensure Tournament Formats Table Has Data
 */

require_once '../config/database.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h2>Ensuring Tournament Formats Table Has Data</h2>";

try {
    // Check if table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if (!$table_exists) {
        echo "<p style='color: red;'>❌ tournament_formats table does not exist! Creating...</p>";
        
        $sql = "CREATE TABLE tournament_formats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            sport_types VARCHAR(255) DEFAULT 'team,individual',
            min_participants INT DEFAULT 2,
            max_participants INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_sport_types (sport_types),
            INDEX idx_code (code)
        )";
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ tournament_formats table created</p>";
    } else {
        echo "<p style='color: green;'>✓ tournament_formats table exists</p>";
    }
    
    // Check current data count
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    
    echo "<p>Current tournament formats count: <strong>{$count}</strong></p>";
    
    if ($count == 0) {
        echo "<p style='color: orange;'>⚠ No tournament formats found! Adding default formats...</p>";
        
        // Clear any existing data first
        $conn->exec("DELETE FROM tournament_formats");
        
        $default_formats = [
            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null],
            ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null],
            ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16],
            ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null],
            ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null],
            ['Knockout Rounds', 'knockout_rounds', 'Academic knockout competition with elimination rounds.', 'academic', 4, null],
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null],
            ['Talent Showcase', 'talent_showcase', 'Performance showcase with judged scoring.', 'judged,performance', 3, null],
            ['Performance Competition', 'performance_competition', 'Structured performance competition with multiple rounds.', 'performance', 3, null],
            ['Artistic Judging', 'artistic_judging', 'Artistic performance with panel judging.', 'performance', 3, null],
            ['Elimination Rounds', 'elimination_rounds', 'Progressive elimination rounds for individual competitors.', 'individual', 4, null],
            ['Time Trials', 'time_trials', 'Individual time-based competition format.', 'individual', 2, null]
        ];
        
        foreach ($default_formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute($format);
            echo "<p style='color: green;'>✓ Added: {$format[0]} (Types: {$format[3]})</p>";
        }
        
        // Re-check count
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
        $stmt->execute();
        $new_count = $stmt->fetch()['count'];
        echo "<p style='color: green;'><strong>✓ Total formats added: {$new_count}</strong></p>";
    } else {
        echo "<p style='color: green;'>✓ Tournament formats already exist</p>";
    }
    
    // Display all formats
    echo "<h3>All Tournament Formats:</h3>";
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY name");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'><th>ID</th><th>Name</th><th>Code</th><th>Sport Types</th><th>Min Participants</th><th>Max Participants</th></tr>";
    foreach ($formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td><strong>{$format['name']}</strong></td>";
        echo "<td>{$format['code']}</td>";
        echo "<td style='color: blue;'>{$format['sport_types']}</td>";
        echo "<td>{$format['min_participants']}</td>";
        echo "<td>" . ($format['max_participants'] ?: 'Unlimited') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test queries for each sport type
    echo "<h3>Testing Queries for Each Sport Type:</h3>";
    
    $test_types = ['traditional', 'team', 'individual', 'academic', 'judged', 'performance'];
    
    foreach ($test_types as $sport_type) {
        echo "<h4>Testing: {$sport_type}</h4>";
        
        // Map sport types to database categories
        $type_mapping = [
            'traditional' => ['team', 'individual'],
            'team' => ['team'],
            'individual' => ['individual'],
            'academic' => ['academic'],
            'judged' => ['judged'],
            'performance' => ['performance']
        ];
        
        $sport_types = $type_mapping[$sport_type] ?? ['team', 'individual'];
        
        // Build WHERE clause
        $where_conditions = [];
        $params = [];
        
        foreach ($sport_types as $type) {
            $where_conditions[] = "sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?";
            $params[] = $type . ',%';
            $params[] = '%,' . $type . ',%';
            $params[] = '%,' . $type;
            $params[] = $type;
        }
        
        $where_clause = '(' . implode(') OR (', $where_conditions) . ')';
        
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_types
            FROM tournament_formats 
            WHERE {$where_clause}
            ORDER BY name
        ");
        
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        if (empty($results)) {
            echo "<p style='color: red;'>❌ No formats found for {$sport_type}</p>";
        } else {
            echo "<p style='color: green;'>✓ Found " . count($results) . " formats for {$sport_type}:</p>";
            echo "<ul>";
            foreach ($results as $result) {
                echo "<li><strong>{$result['name']}</strong> (ID: {$result['id']}, Types: {$result['sport_types']})</li>";
            }
            echo "</ul>";
        }
    }
    
    echo "<h3>✅ Setup Complete!</h3>";
    echo "<p style='color: green; font-weight: bold;'>The tournament formats table is now properly set up. You can test the Add Sport modal.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background: #f5f5f5; }
</style>
