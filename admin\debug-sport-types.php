<?php
/**
 * Debug Sport Types and Categories
 */

require_once '../config/database.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h2>Sport Types and Categories Debug</h2>";

try {
    // Check sport_types table
    echo "<h3>1. Sport Types Table</h3>";
    $stmt = $conn->prepare("SELECT * FROM sport_types ORDER BY id");
    $stmt->execute();
    $sport_types = $stmt->fetchAll();
    
    if (empty($sport_types)) {
        echo "<p style='color: red;'>❌ No sport types found!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Category</th><th>Description</th></tr>";
        foreach ($sport_types as $type) {
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['name']}</td>";
            echo "<td>{$type['category']}</td>";
            echo "<td>" . htmlspecialchars($type['description'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check sports table with sport types
    echo "<h3>2. Sports with Sport Types</h3>";
    $stmt = $conn->prepare("
        SELECT s.id, s.name, s.type as old_type, s.sport_type_id,
               st.name as sport_type_name, st.category as sport_type_category
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        ORDER BY s.id
    ");
    $stmt->execute();
    $sports = $stmt->fetchAll();
    
    if (empty($sports)) {
        echo "<p style='color: red;'>❌ No sports found!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Sport Name</th><th>Old Type</th><th>Sport Type ID</th><th>Sport Type Name</th><th>Category</th></tr>";
        foreach ($sports as $sport) {
            echo "<tr>";
            echo "<td>{$sport['id']}</td>";
            echo "<td>{$sport['name']}</td>";
            echo "<td>" . ($sport['old_type'] ?? 'NULL') . "</td>";
            echo "<td>" . ($sport['sport_type_id'] ?? 'NULL') . "</td>";
            echo "<td>" . ($sport['sport_type_name'] ?? 'NULL') . "</td>";
            echo "<td>" . ($sport['sport_type_category'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test getAvailableSports function
    echo "<h3>3. Testing getAvailableSports Function</h3>";
    require_once '../includes/functions.php';
    
    // Test with event ID 1 (or create a test event)
    $test_event_id = 1;
    
    // Check if event exists
    $stmt = $conn->prepare("SELECT id, name FROM events WHERE id = ?");
    $stmt->execute([$test_event_id]);
    $event = $stmt->fetch();
    
    if (!$event) {
        echo "<p style='color: orange;'>⚠ Event ID {$test_event_id} not found, creating test event...</p>";
        $stmt = $conn->prepare("INSERT INTO events (name, description, start_date, end_date, status) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['Test Event', 'Test event for debugging', date('Y-m-d'), date('Y-m-d', strtotime('+1 day')), 'upcoming']);
        $test_event_id = $conn->lastInsertId();
        echo "<p style='color: green;'>✓ Created test event with ID: {$test_event_id}</p>";
    } else {
        echo "<p style='color: green;'>✓ Using event: {$event['name']} (ID: {$test_event_id})</p>";
    }
    
    $available_sports = getAvailableSports($conn, $test_event_id);
    
    echo "<h4>Available Sports for Event {$test_event_id}:</h4>";
    if (empty($available_sports)) {
        echo "<p style='color: red;'>❌ No available sports found!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Sport Type Name</th><th>Category</th><th>Data Attribute Value</th></tr>";
        foreach ($available_sports as $sport) {
            $data_type = $sport['sport_type_category'] ?? $sport['type'];
            echo "<tr>";
            echo "<td>{$sport['id']}</td>";
            echo "<td>{$sport['name']}</td>";
            echo "<td>" . ($sport['sport_type_name'] ?? 'NULL') . "</td>";
            echo "<td>" . ($sport['sport_type_category'] ?? 'NULL') . "</td>";
            echo "<td style='font-weight: bold; color: blue;'>{$data_type}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test tournament format mapping
    echo "<h3>4. Testing Tournament Format Mapping</h3>";
    
    $type_mapping = [
        'traditional' => ['team', 'individual'],
        'team' => ['team'],
        'individual' => ['individual'],
        'academic' => ['academic'],
        'judged' => ['judged'],
        'performance' => ['performance']
    ];
    
    foreach ($available_sports as $sport) {
        $sport_type = $sport['sport_type_category'] ?? $sport['type'] ?? 'traditional';
        $mapped_types = $type_mapping[$sport_type] ?? ['team', 'individual'];
        
        echo "<h4>Sport: {$sport['name']} (Type: {$sport_type})</h4>";
        echo "<p>Mapped to tournament types: " . implode(', ', $mapped_types) . "</p>";
        
        // Test the query
        $where_conditions = [];
        $params = [];
        
        foreach ($mapped_types as $type) {
            $where_conditions[] = "sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?";
            $params[] = $type . ',%';
            $params[] = '%,' . $type . ',%';
            $params[] = '%,' . $type;
            $params[] = $type;
        }
        
        $where_clause = '(' . implode(') OR (', $where_conditions) . ')';
        
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_types
            FROM tournament_formats 
            WHERE {$where_clause}
            ORDER BY name
        ");
        
        $stmt->execute($params);
        $formats = $stmt->fetchAll();
        
        if (empty($formats)) {
            echo "<p style='color: red;'>❌ No formats found</p>";
        } else {
            echo "<p style='color: green;'>✓ Found " . count($formats) . " formats:</p>";
            echo "<ul>";
            foreach ($formats as $format) {
                echo "<li>{$format['name']} (Code: {$format['code']}, Types: {$format['sport_types']})</li>";
            }
            echo "</ul>";
        }
        
        echo "<hr>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
