<?php
/**
 * Test Tournament Creation After Schema Fix
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Tournament Creation - Fixed</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Tournament Creation After Schema Fix</h1>
        
        <?php
        try {
            echo '<div class="step">';
            echo '<h2>🔍 Step 1: Verify Schema Fix</h2>';
            
            // Test tournament_structure_id column access
            try {
                $stmt = $conn->prepare("SELECT tournament_structure_id FROM matches LIMIT 1");
                $stmt->execute();
                echo '<div class="success">✅ tournament_structure_id column is accessible</div>';
            } catch (Exception $e) {
                echo '<div class="error">❌ tournament_structure_id column still not accessible: ' . htmlspecialchars($e->getMessage()) . '</div>';
                echo '<p><a href="comprehensive-tournament-schema-fix.php" class="btn btn-warning">🔧 Run Schema Fix Again</a></p>';
                exit;
            }
            
            // Test insert with tournament columns
            try {
                $stmt = $conn->prepare("
                    INSERT INTO matches 
                    (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([999, 999, 999, 1, 1, 'TEST', 0, 'scheduled']);
                
                // Clean up test record
                $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = 999 AND team1_id = 999");
                $stmt->execute();
                
                echo '<div class="success">✅ Insert with tournament columns successful</div>';
            } catch (Exception $e) {
                echo '<div class="error">❌ Insert with tournament columns failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                exit;
            }
            
            echo '</div>';
            
            echo '<div class="step">';
            echo '<h2>🏗️ Step 2: Test Tournament Manager</h2>';
            
            // Include tournament manager
            require_once '../includes/tournament_manager.php';
            
            // Get a test event sport
            $stmt = $conn->prepare("
                SELECT es.*, s.name as sport_name 
                FROM event_sports es 
                JOIN sports s ON es.sport_id = s.id 
                WHERE es.event_id = 1 
                LIMIT 1
            ");
            $stmt->execute();
            $event_sport = $stmt->fetch();
            
            if (!$event_sport) {
                echo '<div class="warning">⚠️ No event sports found for testing. Please add a sport to Event 1 first.</div>';
                echo '<p><a href="manage-event.php?id=1" class="btn btn-warning">📋 Manage Event 1</a></p>';
            } else {
                echo '<div class="info">ℹ️ Testing with Event Sport: ' . htmlspecialchars($event_sport['sport_name']) . ' (ID: ' . $event_sport['id'] . ')</div>';
                
                // Check for tournament formats
                $stmt = $conn->prepare("SELECT * FROM tournament_formats LIMIT 1");
                $stmt->execute();
                $format = $stmt->fetch();
                
                if (!$format) {
                    echo '<div class="warning">⚠️ No tournament formats found. Creating basic format...</div>';

                    // First check if algorithm_class column exists in tournament_formats
                    $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'algorithm_class'");
                    $stmt->execute();
                    $has_algorithm_class = $stmt->fetch();

                    if (!$has_algorithm_class) {
                        echo '<div class="warning">⚠️ Adding algorithm_class column to tournament_formats...</div>';
                        $conn->exec("ALTER TABLE tournament_formats ADD COLUMN algorithm_class VARCHAR(100) NULL");
                        echo '<div class="success">✅ Added algorithm_class column</div>';
                    }

                    // Create a basic tournament format with algorithm class
                    $stmt = $conn->prepare("
                        INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants, algorithm_class)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        'Single Elimination',
                        'single_elimination',
                        'Basic single elimination tournament',
                        'team,individual',
                        2,
                        32,
                        'SingleEliminationAlgorithm'
                    ]);

                    $format_id = $conn->lastInsertId();
                    echo '<div class="success">✅ Created basic tournament format (ID: ' . $format_id . ')</div>';
                } else {
                    $format_id = $format['id'];
                    echo '<div class="info">ℹ️ Using existing tournament format: ' . htmlspecialchars($format['name']) . ' (ID: ' . $format_id . ')</div>';

                    // Check if this format has algorithm_class
                    if (empty($format['algorithm_class'])) {
                        echo '<div class="warning">⚠️ Tournament format missing algorithm_class. Updating...</div>';
                        $stmt = $conn->prepare("UPDATE tournament_formats SET algorithm_class = ? WHERE id = ?");
                        $stmt->execute(['SingleEliminationAlgorithm', $format_id]);
                        echo '<div class="success">✅ Updated tournament format with algorithm_class</div>';
                    }
                }
                
                // Check for registrations
                $stmt = $conn->prepare("
                    SELECT COUNT(*) as count 
                    FROM registrations r
                    JOIN event_department_registrations edr ON r.event_department_registration_id = edr.id
                    WHERE edr.event_id = ? AND r.event_sport_id = ?
                ");
                $stmt->execute([1, $event_sport['id']]);
                $registration_count = $stmt->fetchColumn();
                
                echo '<div class="info">ℹ️ Found ' . $registration_count . ' registrations for this event sport</div>';
                
                if ($registration_count < 2) {
                    echo '<div class="warning">⚠️ Need at least 2 registrations to test tournament creation. Creating test registrations...</div>';
                    
                    // Create test registrations if needed
                    // First, ensure we have departments
                    $stmt = $conn->prepare("SELECT id FROM departments LIMIT 2");
                    $stmt->execute();
                    $departments = $stmt->fetchAll();
                    
                    if (count($departments) < 2) {
                        echo '<div class="warning">⚠️ Need at least 2 departments. Creating test departments...</div>';
                        
                        for ($i = 1; $i <= 2; $i++) {
                            $stmt = $conn->prepare("
                                INSERT IGNORE INTO departments (name, contact_person, contact_email, contact_phone) 
                                VALUES (?, ?, ?, ?)
                            ");
                            $stmt->execute([
                                "Test Department $i",
                                "Test Contact $i",
                                "test$<EMAIL>",
                                "123-456-789$i"
                            ]);
                        }
                        
                        // Re-fetch departments
                        $stmt = $conn->prepare("SELECT id FROM departments LIMIT 2");
                        $stmt->execute();
                        $departments = $stmt->fetchAll();
                    }
                    
                    // Create event department registrations and sport registrations
                    foreach ($departments as $dept) {
                        // Check if event department registration exists
                        $stmt = $conn->prepare("
                            SELECT id FROM event_department_registrations 
                            WHERE event_id = ? AND department_id = ?
                        ");
                        $stmt->execute([1, $dept['id']]);
                        $edr_id = $stmt->fetchColumn();
                        
                        if (!$edr_id) {
                            // Create event department registration
                            $stmt = $conn->prepare("
                                INSERT INTO event_department_registrations (event_id, department_id, registration_date, status) 
                                VALUES (?, ?, NOW(), 'approved')
                            ");
                            $stmt->execute([1, $dept['id']]);
                            $edr_id = $conn->lastInsertId();
                        }
                        
                        // Check if sport registration exists
                        $stmt = $conn->prepare("
                            SELECT id FROM registrations 
                            WHERE event_department_registration_id = ? AND event_sport_id = ?
                        ");
                        $stmt->execute([$edr_id, $event_sport['id']]);
                        $reg_exists = $stmt->fetchColumn();
                        
                        if (!$reg_exists) {
                            // Create sport registration
                            $stmt = $conn->prepare("
                                INSERT INTO registrations (event_department_registration_id, event_sport_id, team_name, registration_date, status) 
                                VALUES (?, ?, ?, NOW(), 'approved')
                            ");
                            $stmt->execute([$edr_id, $event_sport['id'], "Test Team " . $dept['id']]);
                        }
                    }
                    
                    echo '<div class="success">✅ Created test registrations</div>';
                }
                
                echo '</div>';
                
                echo '<div class="step">';
                echo '<h2>🚀 Step 3: Create Test Tournament</h2>';
                
                try {
                    $tournamentManager = new TournamentManager($conn);
                    
                    $config = [
                        'seeding_method' => 'random',
                        'scoring_config' => [
                            'points_win' => 3,
                            'points_draw' => 1,
                            'points_loss' => 0
                        ]
                    ];
                    
                    $tournament_name = "Test Tournament " . date('Y-m-d H:i:s');
                    
                    echo '<p>Creating tournament: ' . htmlspecialchars($tournament_name) . '</p>';
                    
                    $tournamentId = $tournamentManager->createTournament(
                        $event_sport['id'], 
                        $format_id, 
                        $tournament_name, 
                        $config
                    );
                    
                    echo '<div class="success">🎉 <strong>SUCCESS!</strong> Tournament created successfully with ID: ' . $tournamentId . '</div>';
                    
                    // Show tournament details
                    $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE id = ?");
                    $stmt->execute([$tournamentId]);
                    $tournament = $stmt->fetch();
                    
                    if ($tournament) {
                        echo '<h3>Tournament Details:</h3>';
                        echo '<table>';
                        echo '<tr><th>Property</th><th>Value</th></tr>';
                        echo '<tr><td>ID</td><td>' . $tournament['id'] . '</td></tr>';
                        echo '<tr><td>Name</td><td>' . htmlspecialchars($tournament['name']) . '</td></tr>';
                        echo '<tr><td>Status</td><td>' . htmlspecialchars($tournament['status']) . '</td></tr>';
                        echo '<tr><td>Participants</td><td>' . $tournament['participant_count'] . '</td></tr>';
                        echo '<tr><td>Total Rounds</td><td>' . $tournament['total_rounds'] . '</td></tr>';
                        echo '<tr><td>Current Round</td><td>' . $tournament['current_round'] . '</td></tr>';
                        echo '</table>';
                    }
                    
                    // Show created matches
                    $stmt = $conn->prepare("
                        SELECT m.*, r1.team_name as team1_name, r2.team_name as team2_name
                        FROM matches m
                        LEFT JOIN registrations r1 ON m.team1_id = r1.id
                        LEFT JOIN registrations r2 ON m.team2_id = r2.id
                        WHERE m.tournament_structure_id = ?
                        ORDER BY m.round_number, m.bracket_position
                    ");
                    $stmt->execute([$tournamentId]);
                    $matches = $stmt->fetchAll();
                    
                    if ($matches) {
                        echo '<h3>Created Matches:</h3>';
                        echo '<table>';
                        echo '<tr><th>Round</th><th>Position</th><th>Team 1</th><th>Team 2</th><th>Status</th><th>Bye Match</th></tr>';
                        foreach ($matches as $match) {
                            echo '<tr>';
                            echo '<td>' . $match['round_number'] . '</td>';
                            echo '<td>' . htmlspecialchars($match['bracket_position']) . '</td>';
                            echo '<td>' . htmlspecialchars($match['team1_name'] ?? 'BYE') . '</td>';
                            echo '<td>' . htmlspecialchars($match['team2_name'] ?? 'BYE') . '</td>';
                            echo '<td>' . htmlspecialchars($match['status']) . '</td>';
                            echo '<td>' . ($match['is_bye_match'] ? 'Yes' : 'No') . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="error">❌ <strong>Tournament creation failed:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
                    
                    // Provide debugging information
                    echo '<h3>Debug Information:</h3>';
                    echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '<p><strong>File:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
                    echo '<p><strong>Line:</strong> ' . $e->getLine() . '</p>';
                    
                    if (strpos($e->getMessage(), 'tournament_structure_id') !== false) {
                        echo '<div class="warning">⚠️ This appears to be a schema issue. The tournament_structure_id column may still be missing or inaccessible.</div>';
                        echo '<p><a href="comprehensive-tournament-schema-fix.php" class="btn btn-warning">🔧 Run Schema Fix Again</a></p>';
                    }
                }
                
                echo '</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ <strong>Test failed:</strong> ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
        
        <!-- Navigation -->
        <div class="step">
            <h2>🧭 Navigation</h2>
            <p>
                <a href="check-matches-table.php" class="btn">🔍 Check Schema</a>
                <a href="comprehensive-tournament-schema-fix.php" class="btn btn-warning">🔧 Schema Fix</a>
                <a href="manage-event.php?id=1" class="btn btn-success">📋 Manage Events</a>
                <a href="index.php" class="btn">🏠 Admin Dashboard</a>
            </p>
        </div>
    </div>
</body>
</html>
