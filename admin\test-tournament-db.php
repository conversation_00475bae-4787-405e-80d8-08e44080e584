<?php
/**
 * Test Tournament Database Tables
 * Quick test to check if tournament tables exist and have data
 */

require_once '../config/database.php';
require_once '../includes/auth.php';

// Ensure admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Database Test</h2>";

// Test 1: Check if tables exist
$tables = ['sport_types', 'tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];

echo "<h3>1. Table Existence Check:</h3>";
foreach ($tables as $table) {
    try {
        $stmt = $conn->query("SHOW TABLES LIKE '{$table}'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '{$table}' exists<br>";
        } else {
            echo "❌ Table '{$table}' does NOT exist<br>";
        }
    } catch (PDOException $e) {
        echo "❌ Error checking table '{$table}': " . $e->getMessage() . "<br>";
    }
}

// Test 2: Check sport_types data
echo "<h3>2. Sport Types Data:</h3>";
try {
    $stmt = $conn->query("SELECT * FROM sport_types");
    $sportTypes = $stmt->fetchAll();
    if (count($sportTypes) > 0) {
        echo "✅ Found " . count($sportTypes) . " sport types:<br>";
        foreach ($sportTypes as $type) {
            echo "- {$type['name']} ({$type['category']})<br>";
        }
    } else {
        echo "❌ No sport types found<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error querying sport_types: " . $e->getMessage() . "<br>";
}

// Test 3: Check tournament_formats data
echo "<h3>3. Tournament Formats Data:</h3>";
try {
    $stmt = $conn->query("SELECT * FROM tournament_formats");
    $formats = $stmt->fetchAll();
    if (count($formats) > 0) {
        echo "✅ Found " . count($formats) . " tournament formats:<br>";
        foreach ($formats as $format) {
            echo "- {$format['name']} (Category: {$format['sport_type_category']})<br>";
        }
    } else {
        echo "❌ No tournament formats found<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error querying tournament_formats: " . $e->getMessage() . "<br>";
}

// Test 4: Check sports table for sport_type_id
echo "<h3>4. Sports Table Check:</h3>";
try {
    $stmt = $conn->query("SELECT s.id, s.name, s.sport_type_id, st.category 
                          FROM sports s 
                          LEFT JOIN sport_types st ON s.sport_type_id = st.id 
                          LIMIT 5");
    $sports = $stmt->fetchAll();
    if (count($sports) > 0) {
        echo "✅ Found sports with sport_type_id:<br>";
        foreach ($sports as $sport) {
            echo "- {$sport['name']} (ID: {$sport['id']}, Type ID: {$sport['sport_type_id']}, Category: {$sport['category']})<br>";
        }
    } else {
        echo "❌ No sports found<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error querying sports: " . $e->getMessage() . "<br>";
}

// Test 5: Test the TournamentManager class
echo "<h3>5. TournamentManager Test:</h3>";
try {
    require_once '../includes/tournament_manager.php';
    $tournamentManager = new TournamentManager($conn);
    
    // Test with sport ID 4 (Chess)
    $sportTypeCategory = $tournamentManager->getSportTypeCategory(4);
    echo "✅ Sport ID 4 category: {$sportTypeCategory}<br>";
    
    $formats = $tournamentManager->getAvailableFormats($sportTypeCategory);
    echo "✅ Available formats for '{$sportTypeCategory}': " . count($formats) . " formats<br>";
    foreach ($formats as $format) {
        echo "- {$format['name']}<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing TournamentManager: " . $e->getMessage() . "<br>";
}

echo "<h3>6. Direct AJAX Test:</h3>";
echo "<button onclick='testAjax()'>Test AJAX Call</button>";
echo "<div id='ajax-result'></div>";

?>

<script>
function testAjax() {
    const resultDiv = document.getElementById('ajax-result');
    resultDiv.innerHTML = 'Testing...';
    
    fetch('../ajax/tournament-management.php?action=get_bracket_types&sport_id=4')
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text(); // Get as text first to see what we're getting
        })
        .then(text => {
            console.log('Raw response:', text);
            resultDiv.innerHTML = '<pre>' + text + '</pre>';
            
            // Try to parse as JSON
            try {
                const data = JSON.parse(text);
                console.log('Parsed JSON:', data);
                resultDiv.innerHTML += '<br><strong>Parsed JSON:</strong><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                console.error('JSON parse error:', e);
                resultDiv.innerHTML += '<br><strong>JSON Parse Error:</strong> ' + e.message;
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            resultDiv.innerHTML = '<strong>Error:</strong> ' + error.message;
        });
}
</script>
