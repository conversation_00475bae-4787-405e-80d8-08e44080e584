<?php
/**
 * Debug Tournament Format Issue
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Debug Tournament Format Issue</h1>";
echo "<p>Investigating the 'Invalid tournament format' error...</p>";

$badminton_event_sport_id = 18;

echo "<h2>1. Check Tournament Formats Table</h2>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$formats = $stmt->fetchAll();

if (empty($formats)) {
    echo "<p style='color: red;'>❌ <strong>PROBLEM FOUND!</strong> No tournament formats exist in database!</p>";
    echo "<p>This is why you're getting 'Invalid tournament format' error.</p>";
    
    echo "<h3>🔧 Creating Tournament Formats...</h3>";
    
    // Create tournament_formats table if it doesn't exist
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS tournament_formats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        sport_types VARCHAR(255) DEFAULT 'team,individual',
        min_participants INT DEFAULT 2,
        max_participants INT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->exec($create_table_sql);
    echo "<p style='color: green;'>✅ Tournament formats table created</p>";
    
    // Insert basic tournament formats
    $default_formats = [
        ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null],
        ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null],
        ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16],
        ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null],
        ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null],
        ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null]
    ];
    
    foreach ($default_formats as $format) {
        $stmt = $conn->prepare("
            INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($format);
        echo "<p style='color: green;'>✓ Added: {$format[0]}</p>";
    }
    
    // Re-fetch formats
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
} else {
    echo "<p style='color: green;'>✅ Found " . count($formats) . " tournament formats</p>";
}

echo "<h3>Available Tournament Formats:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f5f5f5;'>";
echo "<th>ID</th><th>Name</th><th>Code</th><th>Sport Types</th><th>Min Participants</th><th>Max Participants</th>";
echo "</tr>";

foreach ($formats as $format) {
    echo "<tr>";
    echo "<td>{$format['id']}</td>";
    echo "<td>{$format['name']}</td>";
    echo "<td>{$format['code']}</td>";
    echo "<td>" . ($format['sport_types'] ?? 'N/A') . "</td>";
    echo "<td>{$format['min_participants']}</td>";
    echo "<td>" . ($format['max_participants'] ?: 'Unlimited') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>2. Check What Format ID is Being Sent</h2>";
echo "<p>Let's simulate the tournament creation request to see what's happening...</p>";

// Check what sport type Badminton is
$stmt = $conn->prepare("
    SELECT s.name, s.type, s.category
    FROM event_sports es
    JOIN sports s ON es.sport_id = s.id
    WHERE es.id = ?
");
$stmt->execute([$badminton_event_sport_id]);
$sport_info = $stmt->fetch();

if ($sport_info) {
    echo "<p><strong>Badminton Sport Info:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Name:</strong> {$sport_info['name']}</li>";
    echo "<li><strong>Type:</strong> " . ($sport_info['type'] ?? 'N/A') . "</li>";
    echo "<li><strong>Category:</strong> " . ($sport_info['category'] ?? 'N/A') . "</li>";
    echo "</ul>";
    
    // Find suitable formats for this sport type
    $sport_type = $sport_info['type'] ?? 'team';
    echo "<h3>Suitable Formats for {$sport_type} sports:</h3>";
    
    $suitable_formats = [];
    foreach ($formats as $format) {
        $format_types = explode(',', $format['sport_types'] ?? 'team,individual');
        if (in_array($sport_type, $format_types) || in_array('all', $format_types)) {
            $suitable_formats[] = $format;
            echo "<p style='color: green;'>✓ <strong>{$format['name']}</strong> (ID: {$format['id']}) - {$format['description']}</p>";
        }
    }
    
    if (empty($suitable_formats)) {
        echo "<p style='color: red;'>❌ No suitable formats found for sport type: {$sport_type}</p>";
    }
}

echo "<h2>3. Test Tournament Creation with Default Format</h2>";
if (!empty($formats)) {
    $default_format = $formats[0]; // Use first available format
    echo "<p>Testing with format: <strong>{$default_format['name']}</strong> (ID: {$default_format['id']})</p>";
    
    // Check participants count
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM event_department_registrations edr
        JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
    ");
    $stmt->execute([$badminton_event_sport_id]);
    $participant_count = $stmt->fetch()['count'];
    
    echo "<p><strong>Participants:</strong> {$participant_count}</p>";
    echo "<p><strong>Format Min Participants:</strong> {$default_format['min_participants']}</p>";
    
    if ($participant_count >= $default_format['min_participants']) {
        echo "<p style='color: green; font-size: 18px;'>✅ <strong>READY!</strong> Tournament creation should work with format ID {$default_format['id']}</p>";
        
        echo "<h3>🎯 Solution</h3>";
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px;'>";
        echo "<p><strong>The tournament formats are now available!</strong></p>";
        echo "<p>Try creating the tournament again. It should work now.</p>";
        echo "<p><a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px; font-weight: bold;'>🏆 Create Badminton Tournament Now</a></p>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ Not enough participants ({$participant_count}) for minimum required ({$default_format['min_participants']})</p>";
    }
}

echo "<h2>4. Debug Tournament Creation Form</h2>";
echo "<p>Let's check what the tournament creation form is sending...</p>";

// Check if there's a tournament creation form on the manage-category page
echo "<script>";
echo "console.log('Tournament Formats Available:');";
foreach ($formats as $format) {
    echo "console.log('ID: {$format['id']}, Name: {$format['name']}, Code: {$format['code']}');";
}
echo "</script>";

echo "<h3>📋 Summary</h3>";
if (empty($formats)) {
    echo "<p style='color: red; font-size: 18px;'>❌ <strong>MAIN ISSUE:</strong> No tournament formats in database</p>";
} else {
    echo "<p style='color: green; font-size: 18px;'>✅ <strong>FIXED:</strong> Tournament formats are now available</p>";
    echo "<p>Default format ID to use: <strong>{$formats[0]['id']}</strong> ({$formats[0]['name']})</p>";
}

echo "<h3>🔧 Next Steps</h3>";
echo "<ol>";
echo "<li>Tournament formats have been created/verified</li>";
echo "<li>Go back to the tournament creation page</li>";
echo "<li>Try creating the tournament again</li>";
echo "<li>If it still fails, check browser console for JavaScript errors</li>";
echo "</ol>";
?>
