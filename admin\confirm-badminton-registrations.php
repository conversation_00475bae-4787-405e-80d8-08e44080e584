<?php
/**
 * Confirm Badminton Registrations for Tournament Creation
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>Confirm Badminton Registrations</h1>";
echo "<p>This will confirm/approve your Badminton registrations so tournaments can be created...</p>";

// Find the correct Badminton event sport (Event ID: 3, Sport ID: 40, Event Sport ID: 18)
$badminton_event_sport_id = 18;

echo "<h2>🏸 Badminton Registration Status Check</h2>";

// Check current registrations
$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        dsp.status,
        d.name as department_name,
        edr.status as registration_status,
        dsp.team_name
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ?
    ORDER BY d.name
");
$stmt->execute([$badminton_event_sport_id]);
$registrations = $stmt->fetchAll();

if (empty($registrations)) {
    echo "<p style='color: red;'>❌ No registrations found for Badminton (Event Sport ID: {$badminton_event_sport_id})</p>";
    echo "<p><a href='quick-registration-fix.php?event_sport_id={$badminton_event_sport_id}' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Create Registrations for Badminton</a></p>";
    exit;
}

echo "<h3>Current Registrations (" . count($registrations) . " found):</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f5f5f5;'>";
echo "<th>ID</th><th>Department</th><th>Team Name</th><th>Sport Status</th><th>Registration Status</th>";
echo "</tr>";

$needs_confirmation = 0;
$already_confirmed = 0;

foreach ($registrations as $reg) {
    $status_color = '';
    if ($reg['status'] == 'confirmed') {
        $status_color = 'color: green; font-weight: bold;';
        $already_confirmed++;
    } else {
        $status_color = 'color: orange; font-weight: bold;';
        $needs_confirmation++;
    }
    
    echo "<tr>";
    echo "<td>{$reg['id']}</td>";
    echo "<td>{$reg['department_name']}</td>";
    echo "<td>" . ($reg['team_name'] ?: 'Default') . "</td>";
    echo "<td style='{$status_color}'>{$reg['status']}</td>";
    echo "<td>{$reg['registration_status']}</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Summary:</h3>";
echo "<ul>";
echo "<li><strong>Total Registrations:</strong> " . count($registrations) . "</li>";
echo "<li><strong>Already Confirmed:</strong> <span style='color: green;'>{$already_confirmed}</span></li>";
echo "<li><strong>Need Confirmation:</strong> <span style='color: orange;'>{$needs_confirmation}</span></li>";
echo "</ul>";

// Check if we have enough confirmed registrations for tournament
if ($already_confirmed >= 2) {
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ You already have {$already_confirmed} confirmed registrations - tournaments can be created!</p>";
    echo "<p><a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Go to Badminton Category Management</a></p>";
} else if (count($registrations) >= 2) {
    echo "<p style='color: orange; font-size: 16px;'>⚠ You have enough registrations but they need to be confirmed first.</p>";
    
    if (isset($_POST['confirm_registrations'])) {
        echo "<h3>🔄 Confirming Registrations...</h3>";
        
        try {
            $conn->beginTransaction();
            
            $confirmed_count = 0;
            foreach ($registrations as $reg) {
                if ($reg['status'] != 'confirmed') {
                    $stmt = $conn->prepare("UPDATE department_sport_participations SET status = 'confirmed' WHERE id = ?");
                    $stmt->execute([$reg['id']]);
                    echo "<p style='color: green;'>✓ Confirmed registration for {$reg['department_name']}</p>";
                    $confirmed_count++;
                }
            }
            
            $conn->commit();
            
            echo "<h3>🎉 Success!</h3>";
            echo "<p style='color: green; font-size: 18px; font-weight: bold;'>✅ Confirmed {$confirmed_count} registrations!</p>";
            echo "<p><strong>You can now create tournaments for Badminton!</strong></p>";
            echo "<p><a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Go to Badminton Category Management</a></p>";
            
        } catch (Exception $e) {
            $conn->rollBack();
            echo "<p style='color: red;'>❌ Error confirming registrations: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<form method='POST' style='margin: 20px 0;'>";
        echo "<button type='submit' name='confirm_registrations' value='1' style='background: #007bff; color: white; padding: 15px 30px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;'>✅ Confirm All Registrations</button>";
        echo "</form>";
        echo "<p><em>This will change the status of all registrations from 'registered' to 'confirmed' so tournaments can be created.</em></p>";
    }
} else {
    echo "<p style='color: red; font-size: 16px;'>❌ Not enough registrations. You need at least 2 registrations to create tournaments.</p>";
    echo "<p><a href='quick-registration-fix.php?event_sport_id={$badminton_event_sport_id}' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Create More Registrations</a></p>";
}

echo "<hr>";
echo "<h3>🔍 Troubleshooting Info:</h3>";
echo "<ul>";
echo "<li><strong>Badminton Event Sport ID:</strong> {$badminton_event_sport_id}</li>";
echo "<li><strong>Correct URL Parameters:</strong> event_id=3&sport_id=40&category_id=2</li>";
echo "<li><strong>Tournament Creation Requirements:</strong> At least 2 confirmed registrations</li>";
echo "<li><strong>Current Issue:</strong> Registrations have status 'registered' but need 'confirmed'</li>";
echo "</ul>";

echo "<h3>📋 Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Click 'Confirm All Registrations'</strong> above to approve the registrations</li>";
echo "<li><strong>Go to Badminton Category Management</strong> using the correct URL</li>";
echo "<li><strong>Try creating the tournament</strong> - it should work now!</li>";
echo "</ol>";
?>
