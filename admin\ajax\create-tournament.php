<?php
/**
 * AJAX Tournament Creation Endpoint
 * SC_IMS Sports Competition and Event Management System
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/tournament_manager.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Validate request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception("Invalid request method");
    }

    // Get and validate input parameters
    $eventSportId = $_POST['event_sport_id'] ?? '';
    $tournamentName = $_POST['tournament_name'] ?? '';
    $formatId = $_POST['format_id'] ?? 1; // Default to single elimination
    $seedingMethod = $_POST['seeding_method'] ?? 'random';

    if (empty($eventSportId)) {
        throw new Exception("Event sport ID is required");
    }

    if (empty($tournamentName)) {
        throw new Exception("Tournament name is required");
    }

    // Get event sport details
    $stmt = $conn->prepare("
        SELECT es.*, s.name as sport_name, e.name as event_name
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        JOIN events e ON es.event_id = e.id
        WHERE es.id = ?
    ");
    $stmt->execute([$eventSportId]);
    $eventSport = $stmt->fetch();

    if (!$eventSport) {
        throw new Exception("Event sport not found");
    }

    // Check if tournament already exists
    $stmt = $conn->prepare("SELECT id FROM tournament_structures WHERE event_sport_id = ?");
    $stmt->execute([$eventSportId]);
    if ($stmt->fetch()) {
        throw new Exception("Tournament already exists for this category");
    }

    // Get participants from unified registration system
    $participants = [];
    try {
        // Try unified registration system first
        $stmt = $conn->prepare("
            SELECT 
                dsp.id,
                COALESCE(dsp.team_name, d.name) as team_name,
                edr.department_id,
                d.name as department_name,
                dsp.status
            FROM event_department_registrations edr
            JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            JOIN departments d ON edr.department_id = d.id
            WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
        ");
        $stmt->execute([$eventSportId]);
        $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // Fallback to old registration system
        $stmt = $conn->prepare("
            SELECT 
                r.id,
                COALESCE(r.team_name, d.name) as team_name,
                r.department_id,
                d.name as department_name,
                r.status
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ? AND r.status IN ('approved', 'confirmed')
        ");
        $stmt->execute([$eventSportId]);
        $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    if (count($participants) < 2) {
        throw new Exception("At least 2 participants are required to create a tournament");
    }

    // Get tournament format
    $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
    $stmt->execute([$formatId]);
    $format = $stmt->fetch();

    if (!$format) {
        throw new Exception("Invalid tournament format");
    }

    if (count($participants) < $format['min_participants']) {
        throw new Exception("Not enough participants for this tournament format. Minimum required: " . $format['min_participants']);
    }

    // Create tournament manager instance
    $tournamentManager = new TournamentManager($conn);

    // Configuration for tournament creation
    $config = [
        'seeding_method' => $seedingMethod,
        'scoring_config' => [
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0
        ]
    ];

    // Create the tournament
    $tournamentId = $tournamentManager->createTournament($eventSportId, $formatId, $tournamentName, $config);

    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'create_tournament',
        "Created tournament '{$tournamentName}' for {$eventSport['sport_name']} with " . count($participants) . " participants");

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Tournament created successfully',
        'tournament_id' => $tournamentId,
        'tournament_name' => $tournamentName,
        'participants_count' => count($participants),
        'format_name' => $format['name']
    ]);

} catch (Exception $e) {
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
