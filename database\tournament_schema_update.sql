-- Tournament Management System Database Schema Enhancement
-- SC_IMS Sports Competition and Event Management System
-- Enhanced schema for dynamic bracket selection and comprehensive tournament algorithms

-- =====================================================
-- 1. SPORT TYPES AND BRACKET TYPES MAPPING TABLE
-- =====================================================

-- Create sport_types table for better categorization
CREATE TABLE IF NOT EXISTS sport_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    category ENUM('individual', 'team', 'academic', 'judged') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert predefined sport types
INSERT INTO sport_types (name, description, category) VALUES
('Individual Sports', 'Sports where participants compete individually', 'individual'),
('Team Sports', 'Sports where teams compete against each other', 'team'),
('Academic Games', 'Knowledge-based competitions and academic contests', 'academic'),
('Judged Competitions', 'Competitions evaluated by judges based on criteria', 'judged');

-- =====================================================
-- 2. TOURNAMENT FORMATS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS tournament_formats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    sport_type_category ENUM('individual', 'team', 'academic', 'judged', 'all') NOT NULL,
    min_participants INT DEFAULT 2,
    max_participants INT DEFAULT NULL,
    requires_seeding BOOLEAN DEFAULT FALSE,
    supports_byes BOOLEAN DEFAULT TRUE,
    advancement_type ENUM('elimination', 'points', 'ranking', 'hybrid') NOT NULL,
    rounds_formula VARCHAR(255), -- Mathematical formula for calculating rounds
    matches_formula VARCHAR(255), -- Mathematical formula for calculating total matches
    algorithm_class VARCHAR(100), -- PHP class name for tournament algorithm
    configuration JSON, -- Additional format-specific configuration
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert comprehensive tournament formats
INSERT INTO tournament_formats (name, code, description, sport_type_category, min_participants, max_participants, requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula, algorithm_class, configuration) VALUES

-- Individual Sports Formats
('Elimination Rounds', 'elimination_rounds', 'Progressive elimination rounds for individual competitions', 'individual', 4, NULL, TRUE, TRUE, 'elimination', 'ceil(log2(n))', 'n-1', 'EliminationRoundsAlgorithm', '{"allow_wildcards": true, "consolation_rounds": false}'),

('Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria', 'judged', 3, NULL, FALSE, FALSE, 'points', '3', 'n*3', 'JudgedRoundsAlgorithm', '{"rounds": ["preliminary", "semifinal", "final"], "criteria_based": true}'),

('Best Performance', 'best_performance', 'Single round with best performance wins', 'individual', 2, NULL, FALSE, FALSE, 'ranking', '1', 'n', 'BestPerformanceAlgorithm', '{"performance_metrics": ["time", "score", "accuracy"]}'),

('Talent Showcase', 'talent_showcase', 'Showcase format with multiple performance rounds', 'judged', 3, 50, FALSE, FALSE, 'points', 'variable', 'n*rounds', 'TalentShowcaseAlgorithm', '{"showcase_rounds": 3, "audience_voting": true}'),

-- Team Sports Formats
('Single Elimination', 'single_elimination', 'Traditional knockout tournament format', 'team', 2, NULL, TRUE, TRUE, 'elimination', 'ceil(log2(n))', 'n-1', 'SingleEliminationAlgorithm', '{"bracket_seeding": true, "third_place_match": false}'),

('Double Elimination', 'double_elimination', 'Two-bracket system with winners and losers brackets', 'team', 3, NULL, TRUE, TRUE, 'elimination', 'ceil(log2(n))*2-1', '2*n-2', 'DoubleEliminationAlgorithm', '{"winners_bracket": true, "losers_bracket": true, "grand_final_advantage": true}'),

('Round Robin', 'round_robin', 'Every team plays every other team once', 'team', 3, 16, FALSE, FALSE, 'points', '1', 'n*(n-1)/2', 'RoundRobinAlgorithm', '{"points_win": 3, "points_draw": 1, "points_loss": 0}'),

('Multi-Stage', 'multi_stage', 'Group stage followed by knockout rounds', 'team', 8, NULL, TRUE, TRUE, 'hybrid', 'variable', 'variable', 'MultiStageAlgorithm', '{"group_stage": true, "knockout_stage": true, "groups_count": 4}'),

-- Academic Games Formats
('Swiss System', 'swiss_system', 'Pairing based on current standings without elimination', 'academic', 4, NULL, FALSE, TRUE, 'points', 'ceil(log2(n))', 'n*rounds/2', 'SwissSystemAlgorithm', '{"pairing_algorithm": "swiss", "avoid_rematches": true}'),

('Knockout Rounds', 'knockout_rounds', 'Academic elimination tournament', 'academic', 4, NULL, TRUE, TRUE, 'elimination', 'ceil(log2(n))', 'n-1', 'KnockoutRoundsAlgorithm', '{"question_pools": true, "time_limits": true}'),

-- Universal Formats
('League Format', 'league_format', 'Season-long competition with regular matches', 'all', 4, NULL, FALSE, FALSE, 'points', 'variable', 'variable', 'LeagueFormatAlgorithm', '{"season_length": "flexible", "home_away": true}');

-- =====================================================
-- 3. TOURNAMENT STRUCTURES TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS tournament_structures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_sport_id INT NOT NULL,
    tournament_format_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
    participant_count INT DEFAULT 0,
    total_rounds INT DEFAULT 0,
    current_round INT DEFAULT 0,
    seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
    bracket_data JSON, -- Complete bracket structure
    advancement_rules JSON, -- Rules for advancing between rounds
    scoring_config JSON, -- Scoring configuration
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
    FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE RESTRICT
);

-- =====================================================
-- 4. TOURNAMENT ROUNDS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS tournament_rounds (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_structure_id INT NOT NULL,
    round_number INT NOT NULL,
    round_name VARCHAR(100) NOT NULL,
    round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
    start_date DATETIME,
    end_date DATETIME,
    matches_count INT DEFAULT 0,
    completed_matches INT DEFAULT 0,
    advancement_criteria JSON, -- Criteria for advancing from this round
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number)
);

-- =====================================================
-- 5. TOURNAMENT PARTICIPANTS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS tournament_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_structure_id INT NOT NULL,
    registration_id INT NOT NULL,
    seed_number INT,
    group_assignment VARCHAR(10), -- For group-stage tournaments
    current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
    points DECIMAL(10,2) DEFAULT 0,
    wins INT DEFAULT 0,
    losses INT DEFAULT 0,
    draws INT DEFAULT 0,
    performance_data JSON, -- Additional performance metrics
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tournament_participant (tournament_structure_id, registration_id)
);

-- =====================================================
-- 6. UPDATE EXISTING TABLES
-- =====================================================

-- Add tournament_structure_id to matches table
ALTER TABLE matches 
ADD COLUMN tournament_structure_id INT,
ADD COLUMN tournament_round_id INT,
ADD COLUMN bracket_position VARCHAR(50),
ADD COLUMN is_bye_match BOOLEAN DEFAULT FALSE,
ADD FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
ADD FOREIGN KEY (tournament_round_id) REFERENCES tournament_rounds(id) ON DELETE CASCADE;

-- Update sports table to reference sport_types
ALTER TABLE sports 
ADD COLUMN sport_type_id INT,
ADD FOREIGN KEY (sport_type_id) REFERENCES sport_types(id) ON DELETE SET NULL;

-- Update existing sports to have proper sport_type_id
UPDATE sports SET sport_type_id = (
    CASE 
        WHEN type = 'traditional' THEN (SELECT id FROM sport_types WHERE category = 'team' LIMIT 1)
        WHEN type = 'judged' THEN (SELECT id FROM sport_types WHERE category = 'judged' LIMIT 1)
        WHEN type = 'academic' THEN (SELECT id FROM sport_types WHERE category = 'academic' LIMIT 1)
        ELSE (SELECT id FROM sport_types WHERE category = 'individual' LIMIT 1)
    END
);

-- =====================================================
-- 7. INDEXES FOR PERFORMANCE
-- =====================================================

CREATE INDEX idx_tournament_structures_event_sport ON tournament_structures(event_sport_id);
CREATE INDEX idx_tournament_structures_status ON tournament_structures(status);
CREATE INDEX idx_tournament_rounds_tournament ON tournament_rounds(tournament_structure_id);
CREATE INDEX idx_tournament_participants_tournament ON tournament_participants(tournament_structure_id);
CREATE INDEX idx_tournament_participants_status ON tournament_participants(current_status);
CREATE INDEX idx_matches_tournament_structure ON matches(tournament_structure_id);
CREATE INDEX idx_matches_tournament_round ON matches(tournament_round_id);
