<?php
/**
 * Check Event Dependencies AJAX Handler
 */

// Start output buffering to prevent any unwanted output
ob_start();

require_once '../auth.php';
require_once '../../config/database.php';

// Require admin authentication
requireAdmin();

// Clean any output that might have been generated
ob_clean();

// Set JSON response header
header('Content-Type: application/json');

// Get database connection
$database = new Database();
$conn = $database->getConnection();

try {
    $event_id = $_POST['event_id'] ?? 0;
    
    if (!$event_id) {
        throw new Exception('Event ID is required');
    }
    
    // Check if event exists
    $stmt = $conn->prepare("SELECT name FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    
    if (!$event) {
        throw new Exception('Event not found');
    }
    
    // Check dependencies
    $sql = "
        SELECT 
            (SELECT COUNT(*) FROM event_sports es WHERE es.event_id = ?) as event_sports_count,
            (SELECT COUNT(*) FROM registrations r 
             JOIN event_sports es ON r.event_sport_id = es.id 
             WHERE es.event_id = ?) as registrations_count,
            (SELECT COUNT(*) FROM tournaments t 
             JOIN event_sports es ON t.event_sport_id = es.id 
             WHERE es.event_id = ?) as tournaments_count,
            (SELECT COUNT(*) FROM matches m 
             JOIN event_sports es ON m.event_sport_id = es.id 
             WHERE es.event_id = ?) as matches_count
    ";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_id, $event_id, $event_id, $event_id]);
    $dependencies = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Determine if event can be deleted
    $dependencies['can_delete'] = ($dependencies['event_sports_count'] == 0 && 
                                  $dependencies['registrations_count'] == 0 && 
                                  $dependencies['tournaments_count'] == 0 && 
                                  $dependencies['matches_count'] == 0);
    
    echo json_encode([
        'success' => true,
        'event_name' => $event['name'],
        'dependencies' => $dependencies
    ]);
    
} catch (Exception $e) {
    // Clean any output that might have been generated
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Ensure clean output
ob_end_flush();
?>
