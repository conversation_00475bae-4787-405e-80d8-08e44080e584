<?php
/**
 * Test Tournament Creation
 * Quick test to verify tournament creation functionality
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tournament Creation | SC_IMS Admin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tournament Creation Test</h1>
        <p>This page tests the tournament creation functionality for the category management system.</p>
        
        <h3>Available Event Sports:</h3>
        <?php
        $stmt = $conn->prepare("
            SELECT es.id, es.event_id, es.sport_id,
                   e.name as event_name, s.name as sport_name
            FROM event_sports es
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            ORDER BY e.name, s.name
            LIMIT 10
        ");
        $stmt->execute();
        $eventSports = $stmt->fetchAll();
        ?>
        
        <div style="margin: 1rem 0;">
            <?php foreach ($eventSports as $es): ?>
                <div style="margin: 0.5rem 0; padding: 0.5rem; border: 1px solid #ddd; border-radius: 4px;">
                    <strong>ID: <?php echo $es['id']; ?></strong> -
                    <?php echo $es['event_name']; ?> >
                    <?php echo $es['sport_name']; ?>

                    <button class="btn" onclick="testCreateTournament(<?php echo $es['id']; ?>, '<?php echo addslashes($es['event_name'] . ' - ' . $es['sport_name']); ?>')">
                        Test Create Tournament
                    </button>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div style="margin-top: 2rem;">
            <a href="manage-category.php?event_id=1&sport_id=1&category_id=2" class="btn">← Back to Category Management</a>
        </div>
    </div>

    <script>
        function testCreateTournament(eventSportId, tournamentName) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = 'Creating tournament...';
            
            // Prepare form data
            const formData = new FormData();
            formData.append('event_sport_id', eventSportId);
            formData.append('tournament_name', tournamentName + ' Tournament');
            formData.append('format_id', '1'); // Default to single elimination
            formData.append('seeding_method', 'random');
            
            // Make AJAX request
            fetch('ajax/create-tournament.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = 'SUCCESS!\n\n' +
                          'Tournament: ' + data.tournament_name + '\n' +
                          'Tournament ID: ' + data.tournament_id + '\n' +
                          'Participants: ' + data.participants_count + '\n' +
                          'Format: ' + data.format_name;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = 'ERROR!\n\n' + data.message;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.className = 'result error';
                resultDiv.textContent = 'NETWORK ERROR!\n\n' + error.message;
            });
        }
    </script>
</body>
</html>
