<?php
/**
 * Verify Tournament Fix
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>✅ Verify Tournament Fix</h1>";
echo "<p>Testing the exact request that will be sent from the frontend...</p>";

$badminton_event_sport_id = 18;

echo "<h2>🔍 Step 1: Check Format ID 7 Exists</h2>";

$stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = 7");
$stmt->execute();
$format_7 = $stmt->fetch();

if ($format_7) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SUCCESS!</strong> Format ID 7 Found</h3>";
    echo "<p><strong>Name:</strong> {$format_7['name']}</p>";
    echo "<p><strong>Code:</strong> {$format_7['code']}</p>";
    echo "<p><strong>Min Participants:</strong> {$format_7['min_participants']}</p>";
    echo "<p><strong>Description:</strong> {$format_7['description']}</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>ERROR!</strong> Format ID 7 Not Found</h3>";
    echo "</div>";
    exit;
}

echo "<h2>🔍 Step 2: Check Participants</h2>";

$stmt = $conn->prepare("
    SELECT 
        dsp.id,
        COALESCE(dsp.team_name, d.name) as team_name,
        edr.department_id,
        d.name as department_name,
        dsp.status,
        edr.status as registration_status
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    JOIN departments d ON edr.department_id = d.id
    WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
");
$stmt->execute([$badminton_event_sport_id]);
$participants = $stmt->fetchAll();

echo "<p><strong>Confirmed Participants:</strong> " . count($participants) . "</p>";

if (count($participants) >= $format_7['min_participants']) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SUFFICIENT PARTICIPANTS!</strong></h3>";
    echo "<p>Have " . count($participants) . " participants, need minimum " . $format_7['min_participants'] . "</p>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 10px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>⚠ <strong>INSUFFICIENT PARTICIPANTS!</strong></h3>";
    echo "<p>Have " . count($participants) . " participants, need minimum " . $format_7['min_participants'] . "</p>";
    echo "</div>";
}

echo "<h3>📋 Participant Details</h3>";
if (!empty($participants)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Team Name</th><th>Department</th><th>Status</th><th>Registration Status</th>";
    echo "</tr>";
    
    foreach ($participants as $participant) {
        echo "<tr>";
        echo "<td>{$participant['id']}</td>";
        echo "<td>{$participant['team_name']}</td>";
        echo "<td>{$participant['department_name']}</td>";
        echo "<td>{$participant['status']}</td>";
        echo "<td>{$participant['registration_status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p><em>No confirmed participants found.</em></p>";
}

echo "<h2>🧪 Step 3: Simulate Exact Frontend Request</h2>";

if (isset($_POST['simulate_request'])) {
    echo "<h3>🔄 Processing Simulated Request...</h3>";
    
    // Simulate the exact request from the frontend
    $_POST['event_sport_id'] = $badminton_event_sport_id;
    $_POST['tournament_name'] = 'Badminton - Mixed Doubles Tournament';
    $_POST['format_id'] = 7; // The corrected format ID
    $_POST['seeding_method'] = 'random';
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border: 2px solid #007bff; margin: 10px 0;'>";
    echo "<h4>📤 Request Parameters:</h4>";
    echo "<ul>";
    echo "<li><strong>event_sport_id:</strong> {$_POST['event_sport_id']}</li>";
    echo "<li><strong>tournament_name:</strong> {$_POST['tournament_name']}</li>";
    echo "<li><strong>format_id:</strong> {$_POST['format_id']}</li>";
    echo "<li><strong>seeding_method:</strong> {$_POST['seeding_method']}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Include the actual create-tournament.php logic
    ob_start();
    try {
        include 'ajax/create-tournament.php';
        $output = ob_get_clean();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 <strong>TOURNAMENT CREATION SUCCESSFUL!</strong></h3>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;'>{$output}</pre>";
        echo "</div>";
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>TOURNAMENT CREATION FAILED!</strong></h3>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

echo "<h3>🚀 Test Tournament Creation</h3>";
echo "<form method='POST'>";
echo "<button type='submit' name='simulate_request' value='1' style='background: #28a745; color: white; padding: 20px 40px; border: none; border-radius: 4px; font-size: 18px; font-weight: bold; cursor: pointer;'>🧪 Simulate Tournament Creation Request</button>";
echo "</form>";

echo "<h2>📊 Summary</h2>";

$all_good = true;
$issues = [];

if (!$format_7) {
    $all_good = false;
    $issues[] = "Format ID 7 does not exist";
}

if (count($participants) < ($format_7['min_participants'] ?? 2)) {
    $all_good = false;
    $issues[] = "Insufficient participants (" . count($participants) . " found, need " . ($format_7['min_participants'] ?? 2) . ")";
}

if ($all_good) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 <strong>ALL CHECKS PASSED!</strong></h3>";
    echo "<p style='font-size: 18px;'>Tournament creation should work perfectly now!</p>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE TOURNAMENT NOW</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>ISSUES FOUND:</strong></h3>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li style='color: #721c24; font-weight: bold;'>{$issue}</li>";
    }
    echo "</ul>";
    echo "</div>";
}

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation Page</a></li>";
echo "<li><a href='instant-badminton-fix.php'>🔧 Fix Registration Status</a></li>";
echo "<li><a href='test-tournament-request.php'>🧪 Advanced Testing</a></li>";
echo "</ul>";
?>
