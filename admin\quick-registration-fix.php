<?php
/**
 * Quick Registration Fix for Tournament Testing
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$event_sport_id = $_GET['event_sport_id'] ?? '';

if (empty($event_sport_id)) {
    echo "<p style='color: red;'>❌ Event Sport ID is required!</p>";
    echo "<p>Usage: quick-registration-fix.php?event_sport_id=X</p>";
    exit;
}

echo "<h1>Quick Registration Fix</h1>";
echo "<p>Creating registrations for Event Sport ID: {$event_sport_id}</p>";

try {
    // Get event sport details
    $stmt = $conn->prepare("
        SELECT es.*, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE es.id = ?
    ");
    $stmt->execute([$event_sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event Sport not found!</p>";
        exit;
    }
    
    echo "<p><strong>Event:</strong> {$event_sport['event_name']}</p>";
    echo "<p><strong>Sport:</strong> {$event_sport['sport_name']}</p>";
    
    // Check existing registrations
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM registrations
        WHERE event_sport_id = ? AND status IN ('confirmed', 'approved')
    ");
    $stmt->execute([$event_sport_id]);
    $existing_count = $stmt->fetch()['count'];
    
    echo "<p>Existing confirmed/approved registrations: {$existing_count}</p>";
    
    if ($existing_count >= 2) {
        echo "<p style='color: green;'>✅ Already have sufficient registrations!</p>";
        
        // Get category for redirect
        $stmt = $conn->prepare("
            SELECT id, event_sport_id
            FROM sport_categories
            WHERE event_sport_id = ?
            LIMIT 1
        ");
        $stmt->execute([$event_sport_id]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo "<p><a href='manage-category.php?event_id={$event_sport['event_id']}&sport_id={$event_sport['sport_id']}&category_id={$category['id']}' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px;'>Go Create Tournament Now!</a></p>";
        }
        exit;
    }
    
    // Get available departments
    $stmt = $conn->prepare("SELECT id, name FROM departments ORDER BY name LIMIT 6");
    $stmt->execute();
    $departments = $stmt->fetchAll();
    
    if (empty($departments)) {
        echo "<p style='color: red;'>❌ No departments found!</p>";
        exit;
    }
    
    $conn->beginTransaction();
    
    $created_count = 0;
    $needed = 4 - $existing_count; // Create enough to have at least 4 total
    
    foreach ($departments as $dept) {
        if ($created_count >= $needed) break;
        
        // Check if department already registered
        $stmt = $conn->prepare("SELECT id FROM registrations WHERE event_sport_id = ? AND department_id = ?");
        $stmt->execute([$event_sport_id, $dept['id']]);
        if ($stmt->fetch()) {
            echo "<p>⚠ {$dept['name']} already registered</p>";
            continue;
        }
        
        // Create registration
        $team_name = $dept['name'] . " " . $event_sport['sport_name'] . " Team";
        $participants = [
            "Player 1 from " . $dept['name'],
            "Player 2 from " . $dept['name']
        ];
        
        $stmt = $conn->prepare("
            INSERT INTO registrations (event_sport_id, department_id, team_name, participants, status, registration_date)
            VALUES (?, ?, ?, ?, 'confirmed', NOW())
        ");
        $stmt->execute([
            $event_sport_id,
            $dept['id'],
            $team_name,
            json_encode($participants)
        ]);
        
        echo "<p style='color: green;'>✓ Created registration for {$dept['name']}</p>";
        $created_count++;
    }
    
    $conn->commit();
    
    echo "<h2>🎉 Summary:</h2>";
    echo "<p style='color: green; font-size: 18px; font-weight: bold;'>Created {$created_count} new registrations!</p>";
    echo "<p>Total registrations now: " . ($existing_count + $created_count) . "</p>";
    
    if (($existing_count + $created_count) >= 2) {
        echo "<p style='color: green;'>✅ You now have enough registrations to create a tournament!</p>";
        
        // Get category for redirect
        $stmt = $conn->prepare("
            SELECT id
            FROM sport_categories
            WHERE event_sport_id = ?
            LIMIT 1
        ");
        $stmt->execute([$event_sport_id]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo "<p><a href='manage-category.php?event_id={$event_sport['event_id']}&sport_id={$event_sport['sport_id']}&category_id={$category['id']}' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px;'>Go Create Tournament Now!</a></p>";
        }
    }
    
} catch (Exception $e) {
    if (isset($conn)) {
        $conn->rollBack();
    }
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
