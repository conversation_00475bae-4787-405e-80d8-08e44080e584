-- Manual Tournament Schema Fix
-- SC_IMS Sports Competition and Event Management System
-- 
-- Run these SQL commands manually in phpMyAdmin or your MySQL client
-- if the automated schema fix scripts are not working.

-- =====================================================
-- 1. ADD TOURNAMENT COLUMNS TO MATCHES TABLE
-- =====================================================

-- Add tournament_structure_id column
ALTER TABLE matches ADD COLUMN tournament_structure_id INT NULL;

-- Add tournament_round_id column  
ALTER TABLE matches ADD COLUMN tournament_round_id INT NULL;

-- Add bracket_position column
ALTER TABLE matches ADD COLUMN bracket_position VARCHAR(50) NULL;

-- Add is_bye_match column
ALTER TABLE matches ADD COLUMN is_bye_match BOOLEAN DEFAULT FALSE;

-- =====================================================
-- 2. CREATE TOURNAMENT TABLES (if they don't exist)
-- =====================================================

-- Create tournament_formats table
CREATE TABLE IF NOT EXISTS tournament_formats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    algorithm_class VARCHAR(100) NULL,
    sport_types VARCHAR(255) DEFAULT 'team,individual',
    min_participants INT DEFAULT 2,
    max_participants INT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create tournament_structures table
CREATE TABLE IF NOT EXISTS tournament_structures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_sport_id INT NOT NULL,
    tournament_format_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
    participant_count INT DEFAULT 0,
    total_rounds INT DEFAULT 0,
    current_round INT DEFAULT 0,
    seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
    bracket_data JSON,
    advancement_rules JSON,
    scoring_config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
    FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE RESTRICT
);

-- Create tournament_rounds table
CREATE TABLE IF NOT EXISTS tournament_rounds (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_structure_id INT NOT NULL,
    round_number INT NOT NULL,
    round_name VARCHAR(100) NOT NULL,
    round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
    status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
    start_date DATETIME,
    end_date DATETIME,
    matches_count INT DEFAULT 0,
    completed_matches INT DEFAULT 0,
    advancement_criteria JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number)
);

-- Create tournament_participants table
CREATE TABLE IF NOT EXISTS tournament_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_structure_id INT NOT NULL,
    registration_id INT NOT NULL,
    seed_number INT,
    group_assignment VARCHAR(10),
    current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
    points DECIMAL(10,2) DEFAULT 0,
    wins INT DEFAULT 0,
    losses INT DEFAULT 0,
    draws INT DEFAULT 0,
    performance_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tournament_participant (tournament_structure_id, registration_id)
);

-- =====================================================
-- 3. INSERT TOURNAMENT FORMATS WITH ALGORITHM CLASSES
-- =====================================================

-- Insert or update tournament formats
INSERT INTO tournament_formats (id, name, code, description, algorithm_class, sport_types, min_participants, max_participants)
VALUES 
(7, 'Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'SingleEliminationAlgorithm', 'team,individual', 2, NULL),
(8, 'Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'DoubleEliminationAlgorithm', 'team,individual', 3, NULL),
(9, 'Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'RoundRobinAlgorithm', 'team,individual', 3, 16),
(10, 'Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'SwissSystemAlgorithm', 'academic', 4, NULL),
(11, 'Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'RoundRobinAlgorithm', 'judged,performance', 3, NULL)
ON DUPLICATE KEY UPDATE 
algorithm_class = VALUES(algorithm_class),
description = VALUES(description),
sport_types = VALUES(sport_types);

-- =====================================================
-- 4. ADD FOREIGN KEY CONSTRAINTS (optional)
-- =====================================================

-- Add foreign key constraint for tournament_structure_id (ignore if already exists)
-- ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_structure 
-- FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE;

-- Add foreign key constraint for tournament_round_id (ignore if already exists)  
-- ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_round 
-- FOREIGN KEY (tournament_round_id) REFERENCES tournament_rounds(id) ON DELETE CASCADE;

-- =====================================================
-- 5. VERIFICATION QUERIES
-- =====================================================

-- Check matches table structure
-- DESCRIBE matches;

-- Check tournament tables exist
-- SHOW TABLES LIKE 'tournament_%';

-- Check tournament formats
-- SELECT id, name, code, algorithm_class FROM tournament_formats;

-- Test tournament_structure_id column access
-- SELECT tournament_structure_id FROM matches LIMIT 1;
