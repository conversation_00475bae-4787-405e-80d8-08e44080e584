<?php
/**
 * Validate Tournament System Integrity
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Tournament System Validation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .step { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Tournament System Validation</h1>
        <p>This tool validates the complete tournament system integrity.</p>
        
        <?php
        $validation_results = [];
        $errors = [];
        
        try {
            // Step 1: Check Required Tables
            echo '<div class="step">';
            echo '<h2>📋 Step 1: Validate Required Tables</h2>';
            
            $required_tables = [
                'tournament_formats' => 'Tournament format definitions',
                'tournament_structures' => 'Tournament instances',
                'tournament_rounds' => 'Tournament round management',
                'tournament_participants' => 'Tournament participant tracking',
                'matches' => 'Match records with tournament support'
            ];
            
            $stmt = $conn->prepare("SHOW TABLES");
            $stmt->execute();
            $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($required_tables as $table => $description) {
                if (in_array($table, $existing_tables)) {
                    echo '<div class="success">✅ ' . $table . ' - ' . $description . '</div>';
                    $validation_results[$table] = true;
                } else {
                    echo '<div class="error">❌ ' . $table . ' - Missing: ' . $description . '</div>';
                    $validation_results[$table] = false;
                    $errors[] = "Missing table: $table";
                }
            }
            
            echo '</div>';
            
            // Step 2: Check Required Columns
            echo '<div class="step">';
            echo '<h2>🔧 Step 2: Validate Required Columns</h2>';
            
            // Check matches table columns
            if ($validation_results['matches']) {
                $stmt = $conn->prepare("DESCRIBE matches");
                $stmt->execute();
                $matches_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
                
                $required_matches_columns = [
                    'tournament_structure_id' => 'Links matches to tournaments',
                    'tournament_round_id' => 'Links matches to tournament rounds',
                    'bracket_position' => 'Position in tournament bracket',
                    'is_bye_match' => 'Indicates bye matches'
                ];
                
                echo '<h3>Matches Table Columns:</h3>';
                foreach ($required_matches_columns as $column => $description) {
                    if (in_array($column, $matches_columns)) {
                        echo '<div class="success">✅ ' . $column . ' - ' . $description . '</div>';
                    } else {
                        echo '<div class="error">❌ ' . $column . ' - Missing: ' . $description . '</div>';
                        $errors[] = "Missing column: matches.$column";
                    }
                }
            }
            
            // Check tournament_formats table columns
            if ($validation_results['tournament_formats']) {
                $stmt = $conn->prepare("DESCRIBE tournament_formats");
                $stmt->execute();
                $formats_columns = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'Field');
                
                $required_formats_columns = [
                    'algorithm_class' => 'Tournament algorithm implementation class'
                ];
                
                echo '<h3>Tournament Formats Table Columns:</h3>';
                foreach ($required_formats_columns as $column => $description) {
                    if (in_array($column, $formats_columns)) {
                        echo '<div class="success">✅ ' . $column . ' - ' . $description . '</div>';
                    } else {
                        echo '<div class="error">❌ ' . $column . ' - Missing: ' . $description . '</div>';
                        $errors[] = "Missing column: tournament_formats.$column";
                    }
                }
            }
            
            echo '</div>';
            
            // Step 3: Check Foreign Key Relationships
            echo '<div class="step">';
            echo '<h2>🔗 Step 3: Validate Foreign Key Relationships</h2>';
            
            $stmt = $conn->prepare("
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
                AND TABLE_NAME IN ('matches', 'tournament_structures', 'tournament_rounds', 'tournament_participants')
                ORDER BY TABLE_NAME, COLUMN_NAME
            ");
            $stmt->execute();
            $foreign_keys = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($foreign_keys)) {
                echo '<div class="warning">⚠️ No foreign key relationships found for tournament tables</div>';
            } else {
                echo '<table>';
                echo '<tr><th>Table</th><th>Column</th><th>References</th><th>Constraint</th></tr>';
                foreach ($foreign_keys as $fk) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($fk['TABLE_NAME']) . '</td>';
                    echo '<td>' . htmlspecialchars($fk['COLUMN_NAME']) . '</td>';
                    echo '<td>' . htmlspecialchars($fk['REFERENCED_TABLE_NAME']) . '.' . htmlspecialchars($fk['REFERENCED_COLUMN_NAME']) . '</td>';
                    echo '<td>' . htmlspecialchars($fk['CONSTRAINT_NAME']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                echo '<div class="success">✅ Foreign key relationships are configured</div>';
            }
            
            echo '</div>';
            
            // Step 4: Test Tournament Algorithm Classes
            echo '<div class="step">';
            echo '<h2>🧪 Step 4: Validate Tournament Algorithm Classes</h2>';
            
            require_once '../includes/tournament_algorithms.php';
            
            $algorithm_classes = [
                'SingleEliminationAlgorithm',
                'DoubleEliminationAlgorithm', 
                'RoundRobinAlgorithm',
                'SwissSystemAlgorithm'
            ];
            
            foreach ($algorithm_classes as $class) {
                if (class_exists($class)) {
                    echo '<div class="success">✅ ' . $class . ' - Available</div>';
                } else {
                    echo '<div class="error">❌ ' . $class . ' - Missing</div>';
                    $errors[] = "Missing algorithm class: $class";
                }
            }
            
            echo '</div>';
            
            // Step 5: Test Tournament Format Data
            echo '<div class="step">';
            echo '<h2>📊 Step 5: Validate Tournament Format Data</h2>';
            
            if ($validation_results['tournament_formats']) {
                $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_formats");
                $stmt->execute();
                $format_count = $stmt->fetchColumn();
                
                if ($format_count > 0) {
                    echo '<div class="success">✅ Found ' . $format_count . ' tournament format(s)</div>';
                    
                    // Check for formats with algorithm classes
                    $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_formats WHERE algorithm_class IS NOT NULL AND algorithm_class != ''");
                    $stmt->execute();
                    $formats_with_algorithms = $stmt->fetchColumn();
                    
                    if ($formats_with_algorithms > 0) {
                        echo '<div class="success">✅ ' . $formats_with_algorithms . ' format(s) have algorithm classes assigned</div>';
                    } else {
                        echo '<div class="warning">⚠️ No tournament formats have algorithm classes assigned</div>';
                    }
                    
                    // Show sample formats
                    $stmt = $conn->prepare("SELECT name, code, algorithm_class FROM tournament_formats LIMIT 5");
                    $stmt->execute();
                    $sample_formats = $stmt->fetchAll();
                    
                    echo '<h3>Sample Tournament Formats:</h3>';
                    echo '<table>';
                    echo '<tr><th>Name</th><th>Code</th><th>Algorithm Class</th></tr>';
                    foreach ($sample_formats as $format) {
                        echo '<tr>';
                        echo '<td>' . htmlspecialchars($format['name']) . '</td>';
                        echo '<td>' . htmlspecialchars($format['code']) . '</td>';
                        echo '<td>' . htmlspecialchars($format['algorithm_class'] ?? 'Not Set') . '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                } else {
                    echo '<div class="warning">⚠️ No tournament formats found</div>';
                }
            }
            
            echo '</div>';
            
            // Step 6: Test Basic Tournament Creation
            echo '<div class="step">';
            echo '<h2>🚀 Step 6: Test Basic Tournament Creation Capability</h2>';
            
            try {
                // Test if we can create a tournament structure record
                $stmt = $conn->prepare("
                    INSERT INTO tournament_structures 
                    (event_sport_id, tournament_format_id, name, participant_count, seeding_method, scoring_config) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([999, 1, 'Test Tournament', 4, 'random', '{}']);
                $test_tournament_id = $conn->lastInsertId();
                
                echo '<div class="success">✅ Can create tournament structure records</div>';
                
                // Test if we can create matches with tournament columns
                $stmt = $conn->prepare("
                    INSERT INTO matches 
                    (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([999, 999, 999, $test_tournament_id, 1, 'TEST', 0, 'scheduled']);
                
                echo '<div class="success">✅ Can create matches with tournament columns</div>';
                
                // Clean up test records
                $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = 999");
                $stmt->execute();
                $stmt = $conn->prepare("DELETE FROM tournament_structures WHERE id = ?");
                $stmt->execute([$test_tournament_id]);
                
                echo '<div class="info">ℹ️ Test records cleaned up</div>';
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Tournament creation test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
                $errors[] = "Tournament creation test failed: " . $e->getMessage();
            }
            
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Validation error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            $errors[] = $e->getMessage();
        }
        ?>
        
        <!-- Summary -->
        <div class="step">
            <h2>📊 Validation Summary</h2>
            
            <?php if (empty($errors)): ?>
                <div class="success">
                    <h3>🎉 All Validations Passed!</h3>
                    <p>The tournament system is properly configured and ready for use.</p>
                </div>
            <?php else: ?>
                <div class="error">
                    <h3>❌ Validation Issues Found:</h3>
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Actions -->
        <div class="step">
            <h2>🛠️ Available Actions</h2>
            <p>
                <?php if (!empty($errors)): ?>
                    <a href="comprehensive-tournament-schema-fix.php" class="btn btn-warning">🔧 Fix Schema Issues</a>
                <?php endif; ?>
                <a href="test-tournament-creation-fixed.php" class="btn btn-success">🧪 Test Tournament Creation</a>
                <a href="check-matches-table.php" class="btn">🔍 Check Matches Table</a>
                <a href="manage-event.php?id=1" class="btn">📋 Manage Events</a>
                <a href="index.php" class="btn">🏠 Admin Dashboard</a>
            </p>
        </div>
    </div>
</body>
</html>
