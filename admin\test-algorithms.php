<?php
/**
 * Test Tournament Algorithms
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/tournament_algorithms.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

echo "<h1>Tournament Algorithm Test</h1>";

try {
    // Test algorithm classes
    $algorithms_to_test = [
        'SingleEliminationAlgorithm',
        'DoubleEliminationAlgorithm',
        'RoundRobinAlgorithm',
        'SwissSystemAlgorithm'
    ];
    
    echo "<h3>Testing Algorithm Classes:</h3>";
    
    foreach ($algorithms_to_test as $algorithm_class) {
        echo "<h4>Testing {$algorithm_class}:</h4>";
        
        try {
            if (!class_exists($algorithm_class)) {
                echo "<p style='color: red;'>❌ Class {$algorithm_class} does not exist</p>";
                continue;
            }
            
            echo "<p style='color: green;'>✓ Class {$algorithm_class} exists</p>";
            
            // Try to instantiate
            $algorithm = new $algorithm_class($conn, 1, []);
            echo "<p style='color: green;'>✓ Class {$algorithm_class} can be instantiated</p>";
            
            // Test with sample participants
            $sample_participants = [
                ['id' => 1, 'team_name' => 'Team A', 'department_id' => 1],
                ['id' => 2, 'team_name' => 'Team B', 'department_id' => 2],
                ['id' => 3, 'team_name' => 'Team C', 'department_id' => 3],
                ['id' => 4, 'team_name' => 'Team D', 'department_id' => 4]
            ];
            
            // Test bracket generation
            $bracket = $algorithm->generateBracket($sample_participants, ['seeding_method' => 'random']);
            echo "<p style='color: green;'>✓ Bracket generation successful</p>";
            
            // Test round calculation
            $rounds = $algorithm->calculateRounds(count($sample_participants));
            echo "<p style='color: green;'>✓ Round calculation: {$rounds} rounds</p>";
            
            // Test match calculation
            $matches = $algorithm->calculateMatches(count($sample_participants));
            echo "<p style='color: green;'>✓ Match calculation: {$matches} matches</p>";
            
            echo "<p><strong>Bracket structure:</strong></p>";
            echo "<pre>" . htmlspecialchars(json_encode($bracket, JSON_PRETTY_PRINT)) . "</pre>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error testing {$algorithm_class}: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "<hr>";
    }
    
    echo "<h3>Algorithm Test Complete!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
