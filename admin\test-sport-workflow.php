<?php
/**
 * Test script for sport addition/deletion workflow
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get test data
$test_event_id = 1; // Assuming event ID 1 exists
$test_sport_id = 1; // Assuming sport ID 1 exists

// Get event and sport info
$stmt = $conn->prepare("SELECT name FROM events WHERE id = ?");
$stmt->execute([$test_event_id]);
$event = $stmt->fetch();

$stmt = $conn->prepare("SELECT name FROM sports WHERE id = ?");
$stmt->execute([$test_sport_id]);
$sport = $stmt->fetch();

if (!$event || !$sport) {
    die("Test event or sport not found. Please ensure event ID {$test_event_id} and sport ID {$test_sport_id} exist.");
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sport Workflow Test - <?php echo APP_NAME; ?></title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        .test-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-button.danger:hover {
            background: #c82333;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .test-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info-box {
            background: #e3f2fd;
            color: #1565c0;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="admin-main">
        <div class="test-container">
            <h1><i class="fas fa-vial"></i> Sport Management Workflow Test</h1>
            
            <div class="info-box">
                <strong>Test Configuration:</strong><br>
                Event: <?php echo htmlspecialchars($event['name']); ?> (ID: <?php echo $test_event_id; ?>)<br>
                Sport: <?php echo htmlspecialchars($sport['name']); ?> (ID: <?php echo $test_sport_id; ?>)
            </div>

            <div class="test-section">
                <h3>1. Check Current Status</h3>
                <button class="test-button" onclick="checkCurrentStatus()">Check Status</button>
                <div id="status-result" class="test-result" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>2. Add Sport to Event</h3>
                <button class="test-button" onclick="testAddSport()">Add Sport</button>
                <div id="add-result" class="test-result" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>3. Remove Sport from Event</h3>
                <button class="test-button danger" onclick="testRemoveSport()">Remove Sport</button>
                <div id="remove-result" class="test-result" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>4. Re-add Sport to Event</h3>
                <button class="test-button" onclick="testReAddSport()">Re-add Sport</button>
                <div id="readd-result" class="test-result" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>5. Complete Workflow Test</h3>
                <button class="test-button" onclick="runCompleteWorkflow()">Run Complete Workflow</button>
                <div id="workflow-result" class="test-result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let currentEventSportId = null;

        function showResult(elementId, message, isSuccess) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = 'test-result ' + (isSuccess ? 'success' : 'error');
            element.style.display = 'block';
        }

        function checkCurrentStatus() {
            fetch('ajax/event-management.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=check_sport_status&event_id=<?php echo $test_event_id; ?>&sport_id=<?php echo $test_sport_id; ?>'
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.exists) {
                        currentEventSportId = data.event_sport_id;
                        showResult('status-result', `Sport is currently added to event (event_sport_id: ${data.event_sport_id})`, true);
                    } else {
                        currentEventSportId = null;
                        showResult('status-result', 'Sport is not currently added to event', true);
                    }
                } catch (e) {
                    showResult('status-result', `Error parsing response: ${e.message}\nRaw response: ${text}`, false);
                }
            })
            .catch(error => {
                showResult('status-result', `Network error: ${error.message}`, false);
            });
        }

        function testAddSport() {
            const formData = new FormData();
            formData.append('action', 'add_sport');
            formData.append('event_id', '<?php echo $test_event_id; ?>');
            formData.append('sport_id', '<?php echo $test_sport_id; ?>');
            formData.append('tournament_format_id', '1'); // Assuming format ID 1 exists
            formData.append('seeding_method', 'random');
            formData.append('max_teams', '8');
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        currentEventSportId = data.id;
                        showResult('add-result', `Success: ${data.message}\nEvent Sport ID: ${data.id}`, true);
                    } else {
                        showResult('add-result', `Error: ${data.message}`, false);
                    }
                } catch (e) {
                    showResult('add-result', `Error parsing response: ${e.message}\nRaw response: ${text}`, false);
                }
            })
            .catch(error => {
                showResult('add-result', `Network error: ${error.message}`, false);
            });
        }

        function testRemoveSport() {
            if (!currentEventSportId) {
                showResult('remove-result', 'No event_sport_id available. Please add sport first or check status.', false);
                return;
            }

            const formData = new FormData();
            formData.append('action', 'remove_sport');
            formData.append('event_sport_id', currentEventSportId);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        currentEventSportId = null;
                        showResult('remove-result', `Success: ${data.message}`, true);
                    } else {
                        showResult('remove-result', `Error: ${data.message}`, false);
                    }
                } catch (e) {
                    showResult('remove-result', `Error parsing response: ${e.message}\nRaw response: ${text}`, false);
                }
            })
            .catch(error => {
                showResult('remove-result', `Network error: ${error.message}`, false);
            });
        }

        function testReAddSport() {
            testAddSport();
            // Copy result to re-add section
            setTimeout(() => {
                const addResult = document.getElementById('add-result');
                const readdResult = document.getElementById('readd-result');
                readdResult.textContent = addResult.textContent;
                readdResult.className = addResult.className;
                readdResult.style.display = 'block';
            }, 100);
        }

        async function runCompleteWorkflow() {
            const workflowResult = document.getElementById('workflow-result');
            workflowResult.style.display = 'block';
            workflowResult.className = 'test-result';
            workflowResult.textContent = 'Running complete workflow...\n';

            try {
                // Step 1: Check status
                workflowResult.textContent += 'Step 1: Checking current status...\n';
                await new Promise(resolve => {
                    checkCurrentStatus();
                    setTimeout(resolve, 1000);
                });

                // Step 2: Add sport
                workflowResult.textContent += 'Step 2: Adding sport...\n';
                await new Promise(resolve => {
                    testAddSport();
                    setTimeout(resolve, 1000);
                });

                // Step 3: Remove sport
                workflowResult.textContent += 'Step 3: Removing sport...\n';
                await new Promise(resolve => {
                    testRemoveSport();
                    setTimeout(resolve, 1000);
                });

                // Step 4: Re-add sport
                workflowResult.textContent += 'Step 4: Re-adding sport...\n';
                await new Promise(resolve => {
                    testReAddSport();
                    setTimeout(resolve, 1000);
                });

                workflowResult.textContent += '\nComplete workflow test finished! Check individual results above.';
                workflowResult.className = 'test-result success';

            } catch (error) {
                workflowResult.textContent += `\nWorkflow failed: ${error.message}`;
                workflowResult.className = 'test-result error';
            }
        }

        // Auto-check status on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkCurrentStatus();
        });
    </script>

    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
