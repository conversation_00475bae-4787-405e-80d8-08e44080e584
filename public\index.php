<?php
/**
 * Public Dashboard for SC_IMS
 * Read-only public interface for viewing live scores, matches, and rankings
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

// Get live matches
$live_matches = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name, s.type as sport_type,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.color_code as team1_color,
                   d2.name as team2_dept_name, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            WHERE m.status = 'ongoing'
            ORDER BY m.actual_start_time DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $live_matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting live matches: " . $e->getMessage());
}

// Get recent results
$recent_results = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.color_code as team1_color,
                   d2.name as team2_dept_name, d2.color_code as team2_color,
                   w.team_name as winner_name, wd.name as winner_dept_name
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            LEFT JOIN registrations w ON m.winner_id = w.id
            LEFT JOIN departments wd ON w.department_id = wd.id
            WHERE m.status = 'completed'
            ORDER BY m.actual_end_time DESC
            LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $recent_results = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting recent results: " . $e->getMessage());
}

// Get upcoming matches
$upcoming_matches = [];
try {
    $sql = "SELECT m.*, 
                   es.event_id, e.name as event_name,
                   s.name as sport_name,
                   t1.team_name as team1_name, t1.department_id as team1_dept_id,
                   t2.team_name as team2_name, t2.department_id as team2_dept_id,
                   d1.name as team1_dept_name, d1.color_code as team1_color,
                   d2.name as team2_dept_name, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations t1 ON m.team1_id = t1.id
            LEFT JOIN registrations t2 ON m.team2_id = t2.id
            JOIN departments d1 ON t1.department_id = d1.id
            LEFT JOIN departments d2 ON t2.department_id = d2.id
            WHERE m.status = 'scheduled'
            ORDER BY m.scheduled_time ASC
            LIMIT 10";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $upcoming_matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting upcoming matches: " . $e->getMessage());
}

// Get current rankings
$rankings = [];
try {
    $sql = "SELECT r.*, d.name as department_name, d.color_code, e.name as event_name
            FROM rankings r
            JOIN departments d ON r.department_id = d.id
            JOIN events e ON r.event_id = e.id
            WHERE e.status IN ('ongoing', 'completed')
            ORDER BY e.start_date DESC, r.rank_position ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $rankings = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting rankings: " . $e->getMessage());
}

// Get active events
$active_events = [];
try {
    $sql = "SELECT * FROM events WHERE status IN ('ongoing', 'upcoming') ORDER BY start_date ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $active_events = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error getting active events: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sports Competition Dashboard - Live Scores & Rankings</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --success-color: #22c55e;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 8px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            line-height: 1.6;
            color: var(--text-primary);
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: var(--text-primary);
            padding: 1rem 0;
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid var(--border-color);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .logo-text h1 {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .logo-text p {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .header-status {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .live-indicator {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: pulse 2s infinite;
            box-shadow: var(--shadow-md);
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: blink 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .page-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 16px 20px;
            font-weight: 600;
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-header i {
            font-size: 1.25rem;
        }

        .card-body {
            padding: 20px;
        }

        .match-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 16px;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .match-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .match-card.live {
            border-color: var(--danger-color);
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            animation: liveGlow 3s ease-in-out infinite;
        }

        .match-card.live::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--danger-color), #dc2626);
            animation: liveBar 2s ease-in-out infinite;
        }

        @keyframes liveGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.3); }
            50% { box-shadow: 0 0 30px rgba(239, 68, 68, 0.5); }
        }

        @keyframes liveBar {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .match-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .match-status.live {
            background: var(--danger-color);
            color: white;
        }

        .match-status.completed {
            background: var(--success-color);
            color: white;
        }

        .match-status.scheduled {
            background: var(--warning-color);
            color: white;
        }

        .match-teams {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 16px 0;
        }

        .team {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .team-color {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: var(--shadow-sm);
        }

        .team-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
        }

        .team-dept {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .vs {
            font-weight: 800;
            color: var(--text-secondary);
            font-size: 1.25rem;
            margin: 0 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
        }

        .match-score {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid var(--border-color);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .ranking-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .ranking-item:last-child {
            border-bottom: none;
        }

        .ranking-item:hover {
            background: var(--light-color);
            margin: 0 -20px;
            padding-left: 20px;
            padding-right: 20px;
        }

        .rank-position {
            font-weight: 800;
            font-size: 1.5rem;
            margin-right: 20px;
            min-width: 40px;
            text-align: center;
        }

        .rank-position.first {
            color: #fbbf24;
            text-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
        }

        .rank-position.second {
            color: #9ca3af;
            text-shadow: 0 0 10px rgba(156, 163, 175, 0.5);
        }

        .rank-position.third {
            color: #d97706;
            text-shadow: 0 0 10px rgba(217, 119, 6, 0.5);
        }

        .rank-details {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .rank-dept-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .rank-points {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 1.125rem;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 1.25rem;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            text-align: center;
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Page Header Styles */
        .page-header {
            text-align: center;
            padding: 32px 24px;
            margin-bottom: 32px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
        }

        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin: 0;
            font-weight: 400;
        }

        /* Empty State Styles */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 3rem;
            color: var(--border-color);
            margin-bottom: 16px;
        }

        .empty-state h3 {
            font-size: 1.25rem;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            font-weight: 600;
        }

        .empty-state p {
            margin: 0;
            font-size: 0.875rem;
        }

        /* Enhanced Team Display */
        .team-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .team-dept {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        /* Match Score Display */
        .match-score {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 12px 0;
        }

        .match-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid var(--border-color);
        }

        .match-info span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Enhanced Ranking Styles */
        .rank-details {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .rank-dept-name {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .rank-points {
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Logo Enhancement */
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .logo-text h1 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .logo-text p {
            margin: 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .header-status {
            display: flex;
            align-items: center;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .container {
                padding: 20px;
            }

            .page-title {
                font-size: 2rem;
            }
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .header-content {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }

            .logo {
                justify-content: center;
            }

            .page-header {
                padding: 24px 16px;
                margin-bottom: 24px;
            }

            .page-title {
                font-size: 1.75rem;
            }

            .page-subtitle {
                font-size: 1rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 12px;
            }

            .match-teams {
                flex-direction: column;
                gap: 12px;
                text-align: center;
            }

            .vs {
                transform: rotate(90deg);
                margin: 8px 0;
            }

            .team {
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .card-body {
                padding: 16px;
            }

            .match-card {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="logo-text">
                    <h1>Sports Dashboard</h1>
                    <p>Live Competition Results & Rankings</p>
                </div>
            </div>
            <div class="header-status">
                <?php if (!empty($live_matches)): ?>
                    <div class="live-indicator">
                        <div class="live-dot"></div>
                        LIVE MATCHES
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </header>

    <div class="container">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Sports Competition Dashboard</h1>
            <p class="page-subtitle">Real-time scores, match results, and department rankings</p>
        </div>

        <!-- Statistics Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value"><?php echo count($live_matches); ?></div>
                <div class="stat-label">Live Matches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo count($recent_results); ?></div>
                <div class="stat-label">Recent Results</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo count($upcoming_matches); ?></div>
                <div class="stat-label">Upcoming Matches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value"><?php echo count($active_events); ?></div>
                <div class="stat-label">Active Events</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="main-content">
                <!-- Live Matches Section -->
                <?php if (!empty($live_matches)): ?>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-broadcast-tower"></i>
                        Live Matches
                    </div>
                    <div class="card-body">
                        <?php foreach ($live_matches as $match): ?>
                            <div class="match-card live">
                                <div class="match-header">
                                    <strong><?php echo htmlspecialchars($match['event_name']); ?> - <?php echo htmlspecialchars($match['sport_name']); ?></strong>
                                    <span class="match-status live">LIVE</span>
                                </div>
                                <div class="match-teams">
                                    <div class="team">
                                        <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team1_color']); ?>"></div>
                                        <div>
                                            <div class="team-name"><?php echo htmlspecialchars($match['team1_dept_name']); ?></div>
                                            <div class="team-dept"><?php echo htmlspecialchars($match['team1_name'] ?? 'Team 1'); ?></div>
                                        </div>
                                    </div>
                                    <span class="vs">VS</span>
                                    <div class="team">
                                        <?php if ($match['team2_color']): ?>
                                            <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team2_color']); ?>"></div>
                                        <?php endif; ?>
                                        <div>
                                            <div class="team-name"><?php echo htmlspecialchars($match['team2_dept_name'] ?? 'TBD'); ?></div>
                                            <div class="team-dept"><?php echo htmlspecialchars($match['team2_name'] ?? 'Team 2'); ?></div>
                                        </div>
                                    </div>
                                </div>
                                <?php if ($match['team1_score'] !== null || $match['team2_score'] !== null): ?>
                                    <div class="match-score">
                                        <span><?php echo $match['team1_score'] ?? '0'; ?></span>
                                        <span>-</span>
                                        <span><?php echo $match['team2_score'] ?? '0'; ?></span>
                                    </div>
                                <?php endif; ?>
                                <div class="match-info">
                                    <?php if ($match['venue']): ?>
                                        <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($match['venue']); ?></span>
                                    <?php endif; ?>
                                    <span><i class="fas fa-clock"></i> <?php echo date('H:i', strtotime($match['actual_start_time'])); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-broadcast-tower"></i>
                        Live Matches
                    </div>
                    <div class="card-body">
                        <div class="empty-state">
                            <i class="fas fa-tv"></i>
                            <h3>No Live Matches</h3>
                            <p>Check back later for live match updates</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Results Section -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line"></i>
                        Recent Results
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_results)): ?>
                            <div class="empty-state">
                                <i class="fas fa-trophy"></i>
                                <h3>No Recent Results</h3>
                                <p>Match results will appear here once games are completed</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recent_results as $match): ?>
                                <div class="match-card">
                                    <div class="match-header">
                                        <strong><?php echo htmlspecialchars($match['event_name']); ?> - <?php echo htmlspecialchars($match['sport_name']); ?></strong>
                                        <span class="match-status completed">COMPLETED</span>
                                    </div>
                                    <div class="match-teams">
                                        <div class="team">
                                            <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team1_color']); ?>"></div>
                                            <div>
                                                <div class="team-name"><?php echo htmlspecialchars($match['team1_dept_name']); ?></div>
                                                <div class="team-dept"><?php echo htmlspecialchars($match['team1_name'] ?? 'Team 1'); ?></div>
                                            </div>
                                        </div>
                                        <span class="vs">VS</span>
                                        <div class="team">
                                            <?php if ($match['team2_color']): ?>
                                                <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team2_color']); ?>"></div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="team-name"><?php echo htmlspecialchars($match['team2_dept_name'] ?? 'TBD'); ?></div>
                                                <div class="team-dept"><?php echo htmlspecialchars($match['team2_name'] ?? 'Team 2'); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if ($match['team1_score'] !== null || $match['team2_score'] !== null): ?>
                                        <div class="match-score">
                                            <span><?php echo $match['team1_score'] ?? '0'; ?></span>
                                            <span>-</span>
                                            <span><?php echo $match['team2_score'] ?? '0'; ?></span>
                                        </div>
                                    <?php endif; ?>
                                    <div class="match-info">
                                        <?php if ($match['winner_name']): ?>
                                            <span style="color: var(--success-color); font-weight: 600;">
                                                <i class="fas fa-trophy"></i> Winner: <?php echo htmlspecialchars($match['winner_dept_name']); ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if ($match['actual_end_time']): ?>
                                            <span><i class="fas fa-clock"></i> <?php echo date('M j, H:i', strtotime($match['actual_end_time'])); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="sidebar">
                <!-- Current Rankings -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-medal"></i>
                        Current Rankings
                    </div>
                    <div class="card-body">
                        <?php if (empty($rankings)): ?>
                            <div class="empty-state">
                                <i class="fas fa-trophy"></i>
                                <h3>No Rankings Yet</h3>
                                <p>Rankings will appear as events progress</p>
                            </div>
                        <?php else: ?>
                            <?php
                            $current_event = '';
                            foreach ($rankings as $ranking):
                                if ($current_event !== $ranking['event_name']):
                                    if ($current_event !== '') echo '</div>';
                                    $current_event = $ranking['event_name'];
                                    echo '<h4 style="margin: 20px 0 12px 0; color: var(--text-primary); font-weight: 600; font-size: 1rem; border-bottom: 2px solid var(--primary-color); padding-bottom: 4px;">' . htmlspecialchars($current_event) . '</h4>';
                                    echo '<div>';
                                endif;
                            ?>
                                <div class="ranking-item">
                                    <div class="rank-position <?php
                                        if ($ranking['rank_position'] == 1) echo 'first';
                                        elseif ($ranking['rank_position'] == 2) echo 'second';
                                        elseif ($ranking['rank_position'] == 3) echo 'third';
                                    ?>"><?php echo $ranking['rank_position']; ?></div>
                                    <div class="rank-details">
                                        <div class="team-color" style="background-color: <?php echo htmlspecialchars($ranking['color_code']); ?>"></div>
                                        <div style="flex: 1;">
                                            <div class="rank-dept-name"><?php echo htmlspecialchars($ranking['department_name']); ?></div>
                                            <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                                <span class="rank-points"><?php echo $ranking['total_points']; ?> pts</span> |
                                                <?php echo $ranking['wins']; ?>W-<?php echo $ranking['losses']; ?>L
                                                <?php if ($ranking['draws'] > 0): ?>-<?php echo $ranking['draws']; ?>D<?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php
                            endforeach;
                            if ($current_event !== '') echo '</div>';
                            ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Upcoming Matches -->
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-calendar-alt"></i>
                        Upcoming Matches
                    </div>
                    <div class="card-body">
                        <?php if (empty($upcoming_matches)): ?>
                            <div class="empty-state">
                                <i class="fas fa-calendar-check"></i>
                                <h3>No Upcoming Matches</h3>
                                <p>Check back later for scheduled games</p>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_slice($upcoming_matches, 0, 5) as $match): ?>
                                <div class="match-card">
                                    <div class="match-header">
                                        <strong><?php echo htmlspecialchars($match['event_name']); ?> - <?php echo htmlspecialchars($match['sport_name']); ?></strong>
                                        <span class="match-status scheduled">SCHEDULED</span>
                                    </div>
                                    <div class="match-teams">
                                        <div class="team">
                                            <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team1_color']); ?>"></div>
                                            <div>
                                                <div class="team-name"><?php echo htmlspecialchars($match['team1_dept_name']); ?></div>
                                                <div class="team-dept"><?php echo htmlspecialchars($match['team1_name'] ?? 'Team 1'); ?></div>
                                            </div>
                                        </div>
                                        <span class="vs">VS</span>
                                        <div class="team">
                                            <?php if ($match['team2_color']): ?>
                                                <div class="team-color" style="background-color: <?php echo htmlspecialchars($match['team2_color']); ?>"></div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="team-name"><?php echo htmlspecialchars($match['team2_dept_name'] ?? 'TBD'); ?></div>
                                                <div class="team-dept"><?php echo htmlspecialchars($match['team2_name'] ?? 'Team 2'); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="match-info">
                                        <?php if ($match['venue']): ?>
                                            <span><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($match['venue']); ?></span>
                                        <?php endif; ?>
                                        <?php if ($match['scheduled_time']): ?>
                                            <span><i class="fas fa-clock"></i> <?php echo date('M j, g:i A', strtotime($match['scheduled_time'])); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Auto-refresh and Real-time Updates -->
    <script>
        // Auto-refresh page every 30 seconds for live updates
        let refreshInterval;
        let isPageVisible = true;

        // Check if page is visible to user
        document.addEventListener('visibilitychange', function() {
            isPageVisible = !document.hidden;
            if (isPageVisible) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        function startAutoRefresh() {
            // Clear any existing interval
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }

            // Only refresh if there are live matches
            const liveMatches = document.querySelectorAll('.match-card.live');
            if (liveMatches.length > 0) {
                refreshInterval = setInterval(function() {
                    if (isPageVisible) {
                        // Smooth refresh without losing scroll position
                        const scrollPosition = window.pageYOffset;
                        fetch(window.location.href)
                            .then(response => response.text())
                            .then(html => {
                                const parser = new DOMParser();
                                const newDoc = parser.parseFromString(html, 'text/html');

                                // Update live matches section
                                const currentLiveSection = document.querySelector('.main-content');
                                const newLiveSection = newDoc.querySelector('.main-content');
                                if (currentLiveSection && newLiveSection) {
                                    currentLiveSection.innerHTML = newLiveSection.innerHTML;
                                }

                                // Update rankings
                                const currentRankings = document.querySelector('.sidebar');
                                const newRankings = newDoc.querySelector('.sidebar');
                                if (currentRankings && newRankings) {
                                    currentRankings.innerHTML = newRankings.innerHTML;
                                }

                                // Update stats
                                const currentStats = document.querySelector('.stats-grid');
                                const newStats = newDoc.querySelector('.stats-grid');
                                if (currentStats && newStats) {
                                    currentStats.innerHTML = newStats.innerHTML;
                                }

                                // Restore scroll position
                                window.scrollTo(0, scrollPosition);

                                // Show update notification
                                showUpdateNotification();
                            })
                            .catch(error => {
                                console.log('Auto-refresh failed:', error);
                            });
                    }
                }, 30000); // Refresh every 30 seconds
            }
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }

        function showUpdateNotification() {
            // Create and show a subtle update notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--success-color);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 0.875rem;
                font-weight: 600;
                z-index: 1000;
                opacity: 0;
                transform: translateY(-20px);
                transition: all 0.3s ease;
                box-shadow: var(--shadow-lg);
            `;
            notification.innerHTML = '<i class="fas fa-sync-alt"></i> Updated';
            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateY(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Start auto-refresh when page loads
        document.addEventListener('DOMContentLoaded', function() {
            startAutoRefresh();

            // Add smooth scrolling for better UX
            document.documentElement.style.scrollBehavior = 'smooth';
        });

        // Handle page unload
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });
    </script>

    <!-- Font Awesome for Icons -->
    <script src="https://kit.fontawesome.com/your-kit-id.js" crossorigin="anonymous"></script>
    <!-- Fallback to CDN if kit not available -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</body>
</html>
