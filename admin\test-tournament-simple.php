<?php
/**
 * Simple Tournament Creation Test
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/tournament_manager.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    die("Database connection failed");
}

echo "<h1>Simple Tournament Creation Test</h1>";

try {
    // Get first event sport with registrations
    $stmt = $conn->prepare("
        SELECT es.id, es.event_id, es.sport_id, e.name as event_name, s.name as sport_name,
               COUNT(r.id) as registration_count
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN registrations r ON es.id = r.event_sport_id AND r.status IN ('confirmed', 'approved')
        GROUP BY es.id
        HAVING registration_count >= 2
        ORDER BY registration_count DESC
        LIMIT 1
    ");
    $stmt->execute();
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ No event sport found with sufficient registrations</p>";
        echo "<p><a href='create-test-registrations.php'>Create Test Registrations</a></p>";
        exit;
    }
    
    echo "<h3>Testing with:</h3>";
    echo "<p><strong>Event:</strong> {$event_sport['event_name']}</p>";
    echo "<p><strong>Sport:</strong> {$event_sport['sport_name']}</p>";
    echo "<p><strong>Registrations:</strong> {$event_sport['registration_count']}</p>";
    
    // Get tournament format
    $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE code = 'single_elimination' LIMIT 1");
    $stmt->execute();
    $format = $stmt->fetch();
    
    if (!$format) {
        echo "<p style='color: red;'>❌ Single elimination format not found</p>";
        exit;
    }
    
    echo "<p><strong>Format:</strong> {$format['name']} (Algorithm: {$format['algorithm_class']})</p>";
    
    // Check if tournament already exists
    $stmt = $conn->prepare("SELECT id FROM tournament_structures WHERE event_sport_id = ?");
    $stmt->execute([$event_sport['id']]);
    $existing_tournament = $stmt->fetch();
    
    if ($existing_tournament) {
        echo "<p style='color: orange;'>⚠ Tournament already exists (ID: {$existing_tournament['id']})</p>";
        echo "<p>Deleting existing tournament for fresh test...</p>";
        
        // Delete existing tournament
        $stmt = $conn->prepare("DELETE FROM tournament_structures WHERE id = ?");
        $stmt->execute([$existing_tournament['id']]);
        echo "<p style='color: green;'>✓ Existing tournament deleted</p>";
    }
    
    // Create tournament manager
    $tournamentManager = new TournamentManager($conn);
    
    echo "<h3>Creating Tournament...</h3>";
    
    $config = [
        'seeding_method' => 'random',
        'scoring_config' => [
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0
        ]
    ];
    
    $tournamentName = $event_sport['event_name'] . " - " . $event_sport['sport_name'] . " Tournament";
    
    $tournamentId = $tournamentManager->createTournament(
        $event_sport['id'],
        $format['id'],
        $tournamentName,
        $config
    );
    
    echo "<p style='color: green; font-weight: bold;'>✓ Tournament created successfully!</p>";
    echo "<p><strong>Tournament ID:</strong> {$tournamentId}</p>";
    echo "<p><strong>Tournament Name:</strong> {$tournamentName}</p>";
    
    // Verify tournament was created
    $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE id = ?");
    $stmt->execute([$tournamentId]);
    $tournament = $stmt->fetch();
    
    if ($tournament) {
        echo "<h3>Tournament Details:</h3>";
        echo "<ul>";
        echo "<li><strong>Status:</strong> {$tournament['status']}</li>";
        echo "<li><strong>Participants:</strong> {$tournament['participant_count']}</li>";
        echo "<li><strong>Total Rounds:</strong> {$tournament['total_rounds']}</li>";
        echo "<li><strong>Current Round:</strong> {$tournament['current_round']}</li>";
        echo "<li><strong>Seeding Method:</strong> {$tournament['seeding_method']}</li>";
        echo "</ul>";
        
        // Check tournament participants
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_participants WHERE tournament_structure_id = ?");
        $stmt->execute([$tournamentId]);
        $participant_count = $stmt->fetch()['count'];
        echo "<p><strong>Tournament Participants Created:</strong> {$participant_count}</p>";
        
        // Check tournament rounds
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_rounds WHERE tournament_structure_id = ?");
        $stmt->execute([$tournamentId]);
        $rounds_count = $stmt->fetch()['count'];
        echo "<p><strong>Tournament Rounds Created:</strong> {$rounds_count}</p>";
        
        // Check tournament matches
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_matches WHERE tournament_structure_id = ?");
        $stmt->execute([$tournamentId]);
        $matches_count = $stmt->fetch()['count'];
        echo "<p><strong>Tournament Matches Created:</strong> {$matches_count}</p>";
    }
    
    echo "<h3>✅ Tournament Creation Test Successful!</h3>";
    echo "<p><a href='manage-category.php?event_id={$event_sport['event_id']}&sport_id={$event_sport['sport_id']}&category_id=2'>View Tournament</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
