<?php
/**
 * Test Tournament Creation Now
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Test Tournament Creation Now</h1>";
echo "<p>Testing tournament creation immediately after schema fix...</p>";

$badminton_event_sport_id = 18;

echo "<h2>🔍 Step 1: Quick Schema Verification</h2>";

// Quick check for tournament_structure_id column
try {
    $stmt = $conn->prepare("SELECT tournament_structure_id FROM matches LIMIT 1");
    $stmt->execute();
    echo "<p style='color: green; font-weight: bold;'>✅ tournament_structure_id column exists and is accessible</p>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>SCHEMA STILL BROKEN!</strong></h3>";
    echo "<p>tournament_structure_id column is still missing or inaccessible.</p>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><a href='direct-matches-table-fix.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Run Schema Fix Again</a></p>";
    echo "</div>";
    exit;
}

echo "<h2>🔍 Step 2: Check Tournament Format</h2>";

$stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = 7");
$stmt->execute();
$format = $stmt->fetch();

if ($format && !empty($format['algorithm_class'])) {
    echo "<p style='color: green; font-weight: bold;'>✅ Tournament format ready: {$format['name']} ({$format['algorithm_class']})</p>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>FORMAT ISSUE!</strong></h3>";
    echo "<p>Tournament format ID 7 missing or lacks algorithm_class.</p>";
    echo "<p><a href='comprehensive-schema-fix.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Fix Tournament Formats</a></p>";
    echo "</div>";
    exit;
}

echo "<h2>🔍 Step 3: Test Tournament Creation</h2>";

if (isset($_POST['test_now'])) {
    echo "<h3>🔄 Creating Tournament...</h3>";
    
    // Set up the request parameters
    $_POST['event_sport_id'] = $badminton_event_sport_id;
    $_POST['tournament_name'] = 'Badminton - Mixed Doubles Test Tournament';
    $_POST['format_id'] = 7;
    $_POST['seeding_method'] = 'random';
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border: 2px solid #007bff; margin: 10px 0;'>";
    echo "<h4>📤 Test Parameters:</h4>";
    echo "<ul>";
    echo "<li><strong>event_sport_id:</strong> {$_POST['event_sport_id']}</li>";
    echo "<li><strong>tournament_name:</strong> {$_POST['tournament_name']}</li>";
    echo "<li><strong>format_id:</strong> {$_POST['format_id']}</li>";
    echo "<li><strong>seeding_method:</strong> {$_POST['seeding_method']}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Capture output from tournament creation
    ob_start();
    $success = false;
    $error_message = '';
    
    try {
        include 'ajax/create-tournament.php';
        $output = ob_get_clean();
        $success = true;
        
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 <strong>TOURNAMENT CREATION SUCCESSFUL!</strong></h3>";
        echo "<p style='font-size: 18px;'>The schema fix worked! Tournament system is now functional.</p>";
        echo "<h4>Response:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px;'>{$output}</pre>";
        echo "</div>";
        
        // Verify what was created
        echo "<h3>🔍 Verification:</h3>";
        
        $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE event_sport_id = ? ORDER BY created_at DESC LIMIT 1");
        $stmt->execute([$badminton_event_sport_id]);
        $tournament = $stmt->fetch();
        
        if ($tournament) {
            echo "<h4>✅ Tournament Created:</h4>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><td><strong>ID</strong></td><td>{$tournament['id']}</td></tr>";
            echo "<tr><td><strong>Name</strong></td><td>{$tournament['name']}</td></tr>";
            echo "<tr><td><strong>Status</strong></td><td>{$tournament['status']}</td></tr>";
            echo "<tr><td><strong>Participants</strong></td><td>{$tournament['participant_count']}</td></tr>";
            echo "<tr><td><strong>Rounds</strong></td><td>{$tournament['total_rounds']}</td></tr>";
            echo "</table>";
            
            // Check matches with tournament_structure_id
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM matches WHERE tournament_structure_id = ?");
            $stmt->execute([$tournament['id']]);
            $match_count = $stmt->fetch()['count'];
            
            echo "<h4>✅ Matches Created: {$match_count}</h4>";
            
            if ($match_count > 0) {
                echo "<p style='color: green; font-weight: bold;'>✅ tournament_structure_id column is working correctly!</p>";
                
                $stmt = $conn->prepare("SELECT * FROM matches WHERE tournament_structure_id = ? LIMIT 3");
                $stmt->execute([$tournament['id']]);
                $matches = $stmt->fetchAll();
                
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr style='background: #f5f5f5;'><th>Match ID</th><th>Tournament ID</th><th>Round</th><th>Position</th><th>Bye</th></tr>";
                foreach ($matches as $match) {
                    echo "<tr>";
                    echo "<td>{$match['id']}</td>";
                    echo "<td>{$match['tournament_structure_id']}</td>";
                    echo "<td>{$match['round_number']}</td>";
                    echo "<td>{$match['bracket_position']}</td>";
                    echo "<td>" . ($match['is_bye_match'] ? 'Yes' : 'No') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
            echo "<h3 style='color: #155724; margin-top: 0;'>🏆 <strong>SUCCESS! TOURNAMENT SYSTEM WORKING!</strong></h3>";
            echo "<p style='font-size: 18px;'>✅ Schema fix successful</p>";
            echo "<p style='font-size: 18px;'>✅ Tournament creation working</p>";
            echo "<p style='font-size: 18px;'>✅ Auto-bracket generation functional</p>";
            echo "<p style='font-size: 18px;'>✅ Database columns properly added</p>";
            echo "<div style='margin: 20px 0;'>";
            echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE REAL TOURNAMENT NOW</a>";
            echo "</div>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        $error_message = $e->getMessage();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>TOURNAMENT CREATION STILL FAILING!</strong></h3>";
        echo "<p><strong>Error:</strong> " . $error_message . "</p>";
        
        if (strpos($error_message, 'tournament_structure_id') !== false) {
            echo "<p style='color: #721c24; font-weight: bold;'>The tournament_structure_id column is still missing or inaccessible.</p>";
            echo "<p><a href='direct-matches-table-fix.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Run Schema Fix Again</a></p>";
        } else {
            echo "<p>This appears to be a different error. Please check the details above.</p>";
        }
        echo "</div>";
    }
}

if (!isset($_POST['test_now'])) {
    echo "<h3>🚀 Ready to Test</h3>";
    echo "<p>Click below to test tournament creation with the fixed schema.</p>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='test_now' value='1' style='background: #007bff; color: white; padding: 20px 40px; border: none; border-radius: 4px; font-size: 18px; font-weight: bold; cursor: pointer;'>🧪 TEST TOURNAMENT CREATION NOW</button>";
    echo "</form>";
}

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='direct-matches-table-fix.php'>🔧 Run Schema Fix Again</a></li>";
echo "<li><a href='comprehensive-schema-analysis.php'>🔍 Analyze Schema</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation</a></li>";
echo "</ul>";
?>
