<?php
/**
 * Fix Matches Table Schema - Add Missing Tournament Columns
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Matches Table Schema</h1>";
echo "<p>Adding missing tournament_structure_id and related columns to matches table...</p>";

try {
    $conn->beginTransaction();
    
    echo "<h2>🔍 Step 1: Check Current Matches Table Structure</h2>";
    
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<h3>Current Columns:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f5f5f5;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $has_tournament_structure_id = false;
    $has_tournament_round_id = false;
    $has_bracket_position = false;
    $has_is_bye_match = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
        
        if ($column['Field'] == 'tournament_structure_id') $has_tournament_structure_id = true;
        if ($column['Field'] == 'tournament_round_id') $has_tournament_round_id = true;
        if ($column['Field'] == 'bracket_position') $has_bracket_position = true;
        if ($column['Field'] == 'is_bye_match') $has_is_bye_match = true;
    }
    echo "</table>";
    
    echo "<h2>🔧 Step 2: Add Missing Columns</h2>";
    
    $changes_made = [];
    
    // Add tournament_structure_id column
    if (!$has_tournament_structure_id) {
        echo "<p>Adding tournament_structure_id column...</p>";
        $conn->exec("ALTER TABLE matches ADD COLUMN tournament_structure_id INT NULL");
        $changes_made[] = "Added tournament_structure_id column";
        echo "<p style='color: green;'>✅ Added tournament_structure_id column</p>";
    } else {
        echo "<p style='color: blue;'>ℹ tournament_structure_id column already exists</p>";
    }
    
    // Add tournament_round_id column
    if (!$has_tournament_round_id) {
        echo "<p>Adding tournament_round_id column...</p>";
        $conn->exec("ALTER TABLE matches ADD COLUMN tournament_round_id INT NULL");
        $changes_made[] = "Added tournament_round_id column";
        echo "<p style='color: green;'>✅ Added tournament_round_id column</p>";
    } else {
        echo "<p style='color: blue;'>ℹ tournament_round_id column already exists</p>";
    }
    
    // Add bracket_position column
    if (!$has_bracket_position) {
        echo "<p>Adding bracket_position column...</p>";
        $conn->exec("ALTER TABLE matches ADD COLUMN bracket_position VARCHAR(50) NULL");
        $changes_made[] = "Added bracket_position column";
        echo "<p style='color: green;'>✅ Added bracket_position column</p>";
    } else {
        echo "<p style='color: blue;'>ℹ bracket_position column already exists</p>";
    }
    
    // Add is_bye_match column
    if (!$has_is_bye_match) {
        echo "<p>Adding is_bye_match column...</p>";
        $conn->exec("ALTER TABLE matches ADD COLUMN is_bye_match BOOLEAN DEFAULT FALSE");
        $changes_made[] = "Added is_bye_match column";
        echo "<p style='color: green;'>✅ Added is_bye_match column</p>";
    } else {
        echo "<p style='color: blue;'>ℹ is_bye_match column already exists</p>";
    }
    
    echo "<h2>🔍 Step 3: Ensure Tournament Tables Exist</h2>";
    
    // Check if tournament_structures table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_structures'");
    $stmt->execute();
    $tournament_structures_exists = $stmt->fetch();
    
    if (!$tournament_structures_exists) {
        echo "<p>Creating tournament_structures table...</p>";
        $sql = "CREATE TABLE tournament_structures (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_sport_id INT NOT NULL,
            tournament_format_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            status ENUM('setup', 'registration', 'seeding', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
            participant_count INT DEFAULT 0,
            total_rounds INT DEFAULT 0,
            current_round INT DEFAULT 0,
            seeding_method ENUM('random', 'ranking', 'manual', 'hybrid') DEFAULT 'random',
            bracket_data JSON,
            advancement_rules JSON,
            scoring_config JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
            FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE RESTRICT
        )";
        $conn->exec($sql);
        $changes_made[] = "Created tournament_structures table";
        echo "<p style='color: green;'>✅ Created tournament_structures table</p>";
    } else {
        echo "<p style='color: blue;'>ℹ tournament_structures table already exists</p>";
    }
    
    // Check if tournament_rounds table exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_rounds'");
    $stmt->execute();
    $tournament_rounds_exists = $stmt->fetch();
    
    if (!$tournament_rounds_exists) {
        echo "<p>Creating tournament_rounds table...</p>";
        $sql = "CREATE TABLE tournament_rounds (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tournament_structure_id INT NOT NULL,
            round_number INT NOT NULL,
            round_name VARCHAR(100) NOT NULL,
            round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
            status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
            start_date DATETIME,
            end_date DATETIME,
            matches_count INT DEFAULT 0,
            completed_matches INT DEFAULT 0,
            advancement_criteria JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
            UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number)
        )";
        $conn->exec($sql);
        $changes_made[] = "Created tournament_rounds table";
        echo "<p style='color: green;'>✅ Created tournament_rounds table</p>";
    } else {
        echo "<p style='color: blue;'>ℹ tournament_rounds table already exists</p>";
    }
    
    $conn->commit();
    
    echo "<h2>🎉 Schema Fix Complete!</h2>";
    
    if (!empty($changes_made)) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>SCHEMA SUCCESSFULLY UPDATED!</strong></h3>";
        echo "<p style='font-size: 18px;'>The following changes were made:</p>";
        echo "<ul>";
        foreach ($changes_made as $change) {
            echo "<li style='color: #155724; font-weight: bold;'>{$change}</li>";
        }
        echo "</ul>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 TRY TOURNAMENT CREATION NOW</a>";
        echo "</div>";
        echo "</div>";
    } else {
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border: 3px solid #007bff; margin: 20px 0;'>";
        echo "<h3 style='color: #004085; margin-top: 0;'>ℹ <strong>SCHEMA ALREADY UP TO DATE!</strong></h3>";
        echo "<p style='font-size: 18px;'>All required columns and tables already exist.</p>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #007bff; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 TRY TOURNAMENT CREATION NOW</a>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "<h3>📋 Updated Matches Table Structure</h3>";
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $updated_columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($updated_columns as $column) {
        $highlight = in_array($column['Field'], ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match']) 
                    ? 'background: #d4edda; font-weight: bold;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><em>Tournament-related columns are highlighted.</em></p>";
    
} catch (Exception $e) {
    $conn->rollBack();
    echo "<p style='color: red; font-size: 18px;'>❌ <strong>ERROR:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Please check your database permissions and try again.</p>";
}

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation Page</a></li>";
echo "<li><a href='final-tournament-solution-test.php'>🧪 Test Tournament Creation</a></li>";
echo "<li><a href='verify-tournament-fix.php'>✅ Run Verification Tests</a></li>";
echo "</ul>";
?>
