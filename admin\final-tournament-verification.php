<?php
/**
 * Final Tournament Creation Verification
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>✅ Final Tournament Creation Verification</h1>";
echo "<p>Comprehensive check to ensure Badminton tournament creation will work...</p>";

$badminton_event_sport_id = 18;
$all_checks_passed = true;

echo "<h2>🔍 Verification Checklist</h2>";

// Check 1: Registration Status
echo "<h3>1. ✅ Registration Status Check</h3>";
$stmt = $conn->prepare("
    SELECT COUNT(*) as count
    FROM event_department_registrations edr
    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
    WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
");
$stmt->execute([$badminton_event_sport_id]);
$confirmed_participants = $stmt->fetch()['count'];

if ($confirmed_participants >= 2) {
    echo "<p style='color: green;'>✅ <strong>PASS:</strong> Found {$confirmed_participants} confirmed participants</p>";
} else {
    echo "<p style='color: red;'>❌ <strong>FAIL:</strong> Only {$confirmed_participants} confirmed participants (need at least 2)</p>";
    $all_checks_passed = false;
}

// Check 2: Tournament Formats Table
echo "<h3>2. ✅ Tournament Formats Table Check</h3>";
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
$stmt->execute();
$format_count = $stmt->fetch()['count'];

if ($format_count > 0) {
    echo "<p style='color: green;'>✅ <strong>PASS:</strong> Found {$format_count} tournament formats</p>";
} else {
    echo "<p style='color: red;'>❌ <strong>FAIL:</strong> No tournament formats found</p>";
    $all_checks_passed = false;
}

// Check 3: Format ID 1 Exists
echo "<h3>3. ✅ Default Format ID Check</h3>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = 1");
$stmt->execute();
$format_1 = $stmt->fetch();

if ($format_1) {
    echo "<p style='color: green;'>✅ <strong>PASS:</strong> Format ID 1 exists ({$format_1['name']})</p>";
} else {
    // Get first available format
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id LIMIT 1");
    $stmt->execute();
    $first_format = $stmt->fetch();
    
    if ($first_format) {
        echo "<p style='color: orange;'>⚠ <strong>WARNING:</strong> Format ID 1 doesn't exist, but Format ID {$first_format['id']} is available ({$first_format['name']})</p>";
        echo "<p style='color: blue;'>ℹ <strong>NOTE:</strong> Frontend needs to use Format ID {$first_format['id']} instead of 1</p>";
    } else {
        echo "<p style='color: red;'>❌ <strong>FAIL:</strong> No tournament formats available</p>";
        $all_checks_passed = false;
    }
}

// Check 4: Database Connection
echo "<h3>4. ✅ Database Connection Check</h3>";
if ($conn) {
    echo "<p style='color: green;'>✅ <strong>PASS:</strong> Database connection successful</p>";
} else {
    echo "<p style='color: red;'>❌ <strong>FAIL:</strong> Database connection failed</p>";
    $all_checks_passed = false;
}

// Check 5: Tournament Creation Files
echo "<h3>5. ✅ Tournament Creation Files Check</h3>";
$required_files = [
    'ajax/create-tournament.php',
    '../includes/tournament_manager.php',
    'manage-category.php'
];

$files_ok = true;
foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✅ {$file} exists</p>";
    } else {
        echo "<p style='color: red;'>❌ {$file} missing</p>";
        $files_ok = false;
        $all_checks_passed = false;
    }
}

if ($files_ok) {
    echo "<p style='color: green;'>✅ <strong>PASS:</strong> All required files exist</p>";
}

// Check 6: Simulate Tournament Creation Request
echo "<h3>6. ✅ Tournament Creation Simulation</h3>";

if ($all_checks_passed) {
    echo "<p>Simulating the exact request that will be sent...</p>";
    
    // Get the format to use
    $format_to_use = $format_1 ? $format_1 : $first_format;
    
    if ($format_to_use) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
        echo "<h4>Simulated Request Parameters:</h4>";
        echo "<ul>";
        echo "<li><strong>event_sport_id:</strong> {$badminton_event_sport_id}</li>";
        echo "<li><strong>tournament_name:</strong> 'Badminton - Mixed Doubles Tournament'</li>";
        echo "<li><strong>format_id:</strong> {$format_to_use['id']}</li>";
        echo "<li><strong>seeding_method:</strong> 'random'</li>";
        echo "</ul>";
        echo "</div>";
        
        // Validate format requirements
        if ($confirmed_participants >= $format_to_use['min_participants']) {
            echo "<p style='color: green;'>✅ <strong>PASS:</strong> Participants ({$confirmed_participants}) meet format minimum ({$format_to_use['min_participants']})</p>";
        } else {
            echo "<p style='color: red;'>❌ <strong>FAIL:</strong> Not enough participants for format requirements</p>";
            $all_checks_passed = false;
        }
    }
} else {
    echo "<p style='color: red;'>❌ <strong>SKIPPED:</strong> Previous checks failed</p>";
}

// Final Result
echo "<h2>🎯 Final Verification Result</h2>";

if ($all_checks_passed) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 2px solid #28a745;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 <strong>ALL CHECKS PASSED!</strong></h3>";
    echo "<p style='font-size: 18px;'><strong>Your Badminton tournament creation should work perfectly now!</strong></p>";
    
    echo "<h4>✅ Summary:</h4>";
    echo "<ul>";
    echo "<li>✅ {$confirmed_participants} confirmed participants ready</li>";
    echo "<li>✅ {$format_count} tournament formats available</li>";
    echo "<li>✅ Default format ready for use</li>";
    echo "<li>✅ All required files exist</li>";
    echo "<li>✅ Database connection working</li>";
    echo "</ul>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 18px; font-weight: bold; display: inline-block; margin-right: 10px;'>🏆 Create Tournament Now</a>";
    echo "<a href='instant-badminton-fix.php' style='background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px; display: inline-block;'>🔧 Re-run Registration Fix</a>";
    echo "</div>";
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 2px solid #dc3545;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>ISSUES FOUND</strong></h3>";
    echo "<p style='font-size: 18px;'><strong>Some checks failed. Please run the fixes below:</strong></p>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='instant-badminton-fix.php' style='background: #dc3545; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px; display: inline-block; margin-right: 10px;'>🔧 Fix Registration Status</a>";
    echo "<a href='fix-tournament-format-complete.php' style='background: #ffc107; color: black; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 16px; display: inline-block;'>🔧 Fix Tournament Formats</a>";
    echo "</div>";
    echo "</div>";
}

echo "<h3>📋 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='debug-badminton-registrations.php'>🔍 Debug Registration Data</a></li>";
echo "<li><a href='debug-tournament-format-issue.php'>🔍 Debug Tournament Formats</a></li>";
echo "<li><a href='fix-tournament-creation.php'>🔧 Interactive Fix Tool</a></li>";
echo "</ul>";

echo "<h3>🔧 Manual SQL Fixes (if needed)</h3>";
echo "<details>";
echo "<summary>Click to see manual SQL commands</summary>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px;'>";
echo "-- Fix registration status\n";
echo "UPDATE department_sport_participations dsp\n";
echo "JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id\n";
echo "SET dsp.status = 'confirmed', edr.status = 'approved'\n";
echo "WHERE dsp.event_sport_id = {$badminton_event_sport_id};\n\n";

echo "-- Create basic tournament format (if none exist)\n";
echo "INSERT INTO tournament_formats (name, code, description, sport_types, min_participants)\n";
echo "VALUES ('Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'team,individual', 2);";
echo "</pre>";
echo "</details>";
?>
