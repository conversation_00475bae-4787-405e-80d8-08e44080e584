<?php
/**
 * Check Matches Table Structure
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Matches Table Structure Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Matches Table Structure Check</h1>
    
    <h3>Current Matches Table Structure</h3>
    <?php
    try {
        $stmt = $conn->prepare("DESCRIBE matches");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo '<table>';
        echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>';
        
        $has_tournament_structure_id = false;
        $has_tournament_round_id = false;
        $has_bracket_position = false;
        $has_is_bye_match = false;
        
        foreach ($columns as $column) {
            echo '<tr>';
            echo '<td><strong>' . htmlspecialchars($column['Field']) . '</strong></td>';
            echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
            echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
            echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
            echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
            echo '<td>' . htmlspecialchars($column['Extra'] ?? '') . '</td>';
            echo '</tr>';
            
            // Check for tournament columns
            if ($column['Field'] === 'tournament_structure_id') $has_tournament_structure_id = true;
            if ($column['Field'] === 'tournament_round_id') $has_tournament_round_id = true;
            if ($column['Field'] === 'bracket_position') $has_bracket_position = true;
            if ($column['Field'] === 'is_bye_match') $has_is_bye_match = true;
        }
        echo '</table>';
        
        echo '<h3>Tournament Column Status</h3>';
        echo '<ul>';
        echo '<li>tournament_structure_id: ' . ($has_tournament_structure_id ? '<span class="success">✅ EXISTS</span>' : '<span class="error">❌ MISSING</span>') . '</li>';
        echo '<li>tournament_round_id: ' . ($has_tournament_round_id ? '<span class="success">✅ EXISTS</span>' : '<span class="error">❌ MISSING</span>') . '</li>';
        echo '<li>bracket_position: ' . ($has_bracket_position ? '<span class="success">✅ EXISTS</span>' : '<span class="error">❌ MISSING</span>') . '</li>';
        echo '<li>is_bye_match: ' . ($has_is_bye_match ? '<span class="success">✅ EXISTS</span>' : '<span class="error">❌ MISSING</span>') . '</li>';
        echo '</ul>';
        
    } catch (Exception $e) {
        echo '<p class="error">Error checking matches table: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    ?>
    
    <h3>Test Tournament Structure ID Column Access</h3>
    <?php
    try {
        $stmt = $conn->prepare("SELECT tournament_structure_id FROM matches LIMIT 1");
        $stmt->execute();
        echo '<p class="success">✅ tournament_structure_id column is accessible</p>';
    } catch (Exception $e) {
        echo '<p class="error">❌ tournament_structure_id column access failed: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    ?>
    
    <h3>Check Tournament-Related Tables</h3>
    <?php
    $tournament_tables = ['tournament_structures', 'tournament_rounds', 'tournament_participants', 'tournament_formats'];
    
    foreach ($tournament_tables as $table) {
        try {
            $stmt = $conn->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            $exists = $stmt->fetch();
            
            if ($exists) {
                echo '<p class="success">✅ ' . $table . ' table exists</p>';
                
                // Show table structure
                $stmt = $conn->prepare("DESCRIBE $table");
                $stmt->execute();
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo '<details style="margin-left: 20px;">';
                echo '<summary>Show ' . $table . ' structure</summary>';
                echo '<table style="margin: 10px 0;">';
                echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>';
                foreach ($columns as $column) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
                    echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                echo '</details>';
            } else {
                echo '<p class="error">❌ ' . $table . ' table missing</p>';
            }
        } catch (Exception $e) {
            echo '<p class="error">❌ Error checking ' . $table . ': ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
    }
    ?>
    
    <h3>Test Insert with Tournament Columns</h3>
    <?php
    try {
        // Test if we can insert with tournament columns
        $stmt = $conn->prepare("
            INSERT INTO matches
            (event_sport_id, team1_id, team2_id, tournament_structure_id, tournament_round_id, bracket_position, is_bye_match, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        // Use test values
        $stmt->execute([999, 999, 999, 1, 1, 'TEST', 0, 'scheduled']);

        echo '<p class="success">✅ Insert with tournament columns successful</p>';

        // Clean up test record
        $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = 999 AND team1_id = 999");
        $stmt->execute();

    } catch (Exception $e) {
        echo '<p class="error">❌ Insert with tournament columns failed: ' . htmlspecialchars($e->getMessage()) . '</p>';

        // Provide specific guidance
        if (strpos($e->getMessage(), 'tournament_structure_id') !== false) {
            echo '<div style="background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 10px 0;">';
            echo '<h4 style="color: #856404;">🔧 SCHEMA FIX NEEDED</h4>';
            echo '<p>The tournament_structure_id column is missing from the matches table.</p>';
            echo '<p><a href="comprehensive-tournament-schema-fix.php" style="background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">🔧 Fix Schema Now</a></p>';
            echo '</div>';
        }
    }
    ?>
    
</body>
</html>
