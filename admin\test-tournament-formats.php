<?php
/**
 * Test Tournament Format Selection
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get available sports for event ID 1 (or any existing event)
$available_sports = getAvailableSports($conn, 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tournament Formats - <?php echo APP_NAME; ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .format-info { margin-top: 10px; padding: 15px; background: #f8f9fa; border-radius: 6px; }
        .format-description { font-weight: bold; margin-bottom: 10px; }
        .format-requirements { font-size: 0.9rem; color: #666; }
        .sport-info { margin-top: 10px; padding: 10px; background: #e3f2fd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Tournament Format Selection</h1>
        
        <div class="form-group">
            <label for="sport_id">Sport:</label>
            <select name="sport_id" id="sport_id" onchange="loadBracketTypes()">
                <option value="">Select a sport...</option>
                <?php foreach ($available_sports as $sport): ?>
                    <option value="<?php echo $sport['id']; ?>" 
                            data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>"
                            data-sport-type-name="<?php echo htmlspecialchars($sport['sport_type_name'] ?? ''); ?>">
                        <?php echo htmlspecialchars($sport['name']); ?>
                        <?php if ($sport['sport_type_name']): ?>
                            (<?php echo htmlspecialchars($sport['sport_type_name']); ?>)
                        <?php else: ?>
                            (<?php echo ucfirst($sport['type']); ?>)
                        <?php endif; ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <div class="sport-info" id="sport_info" style="display: none;">
                <div class="sport-type"></div>
                <div class="sport-category"></div>
            </div>
        </div>

        <div class="form-group">
            <label for="tournament_format_id">Tournament Format:</label>
            <select name="tournament_format_id" id="tournament_format_id" onchange="showFormatInfo()">
                <option value="">Select a sport first...</option>
            </select>
            <div class="format-info" id="format_info" style="display: none;">
                <div class="format-description"></div>
                <div class="format-requirements"></div>
            </div>
        </div>
    </div>

    <script>
        // Get sport-specific tournament formats
        function getSportSpecificFormats(sportType) {
            const formatsByType = {
                'traditional': [
                    {
                        code: 'single_elimination',
                        name: 'Single Elimination',
                        description: 'Knockout tournament where teams/participants are eliminated after one loss. Fast-paced format with clear progression to finals.',
                        min_participants: 2,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'elimination'
                    },
                    {
                        code: 'double_elimination',
                        name: 'Double Elimination',
                        description: 'Two-bracket system with winner\'s and loser\'s brackets. Teams get a second chance after their first loss.',
                        min_participants: 3,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'elimination'
                    },
                    {
                        code: 'round_robin',
                        name: 'Round Robin',
                        description: 'Every team/participant plays every other team/participant once. Best overall record wins.',
                        min_participants: 3,
                        max_participants: 16,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'multi_stage',
                        name: 'Multi-Stage Tournament',
                        description: 'Group stage round robin followed by single elimination playoffs. Combines fairness of round robin with excitement of knockouts.',
                        min_participants: 8,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'hybrid'
                    }
                ],
                'judged': [
                    {
                        code: 'judged_rounds',
                        name: 'Judged Rounds',
                        description: 'Multiple judged rounds (preliminary, semifinal, final) with scoring criteria. Participants advance based on judge scores.',
                        min_participants: 3,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'talent_showcase',
                        name: 'Talent Showcase',
                        description: 'Multiple performance rounds with audience and judge voting. Combines technical scoring with audience appeal.',
                        min_participants: 3,
                        max_participants: 50,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'performance_competition',
                        name: 'Performance Competition',
                        description: 'Elimination-style competition with performance criteria. Participants eliminated based on performance scores.',
                        min_participants: 4,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'elimination'
                    }
                ],
                'performance': [
                    {
                        code: 'performance_showcase',
                        name: 'Performance Showcase',
                        description: 'Multi-round showcase format with audition, semifinal, and final rounds. Emphasizes artistic presentation.',
                        min_participants: 3,
                        max_participants: 50,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'artistic_judging',
                        name: 'Artistic Judging',
                        description: 'Single round with multiple judging criteria (technique, creativity, presentation). Best overall artistic score wins.',
                        min_participants: 2,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'points'
                    }
                ],
                'academic': [
                    {
                        code: 'swiss_system',
                        name: 'Swiss System',
                        description: 'Pairing based on current standings without elimination. Participants with similar records compete against each other.',
                        min_participants: 4,
                        max_participants: null,
                        requires_seeding: false,
                        advancement_type: 'points'
                    },
                    {
                        code: 'knockout_rounds',
                        name: 'Knockout Rounds',
                        description: 'Academic elimination tournament with question pools and time limits. Single elimination format for knowledge competitions.',
                        min_participants: 4,
                        max_participants: null,
                        requires_seeding: true,
                        advancement_type: 'elimination'
                    }
                ]
            };

            // Return formats for the sport type, or traditional as default
            return formatsByType[sportType] || formatsByType['traditional'];
        }

        function loadBracketTypes() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const formatInfo = document.getElementById('format_info');
            const sportInfo = document.getElementById('sport_info');

            const sportId = sportSelect.value;

            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            formatInfo.style.display = 'none';

            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                sportInfo.style.display = 'none';
                return;
            }

            // Get sport type from the selected option
            const selectedSportOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedSportOption.dataset.type || 'traditional';
            const sportTypeName = selectedSportOption.dataset.sportTypeName || 'Unknown';

            // Show sport info
            sportInfo.querySelector('.sport-type').textContent = `Sport Type: ${sportTypeName}`;
            sportInfo.querySelector('.sport-category').textContent = `Category: ${sportType}`;
            sportInfo.style.display = 'block';

            // Get sport-specific tournament formats
            const formats = getSportSpecificFormats(sportType);
            
            // Populate format dropdown
            formatSelect.innerHTML = '<option value="">Select tournament format...</option>';
            
            formats.forEach(format => {
                const option = document.createElement('option');
                option.value = format.code;
                option.textContent = format.name;
                option.dataset.description = format.description;
                option.dataset.minParticipants = format.min_participants;
                option.dataset.maxParticipants = format.max_participants || 'Unlimited';
                option.dataset.seedingRequired = format.requires_seeding;
                option.dataset.advancementType = format.advancement_type;
                formatSelect.appendChild(option);
            });
        }

        function showFormatInfo() {
            const formatSelect = document.getElementById('tournament_format_id');
            const formatInfo = document.getElementById('format_info');
            const selectedOption = formatSelect.options[formatSelect.selectedIndex];

            if (selectedOption.value && selectedOption.dataset.description) {
                const description = selectedOption.dataset.description;
                const minParticipants = selectedOption.dataset.minParticipants;
                const maxParticipants = selectedOption.dataset.maxParticipants;
                const seedingRequired = selectedOption.dataset.seedingRequired;
                const advancementType = selectedOption.dataset.advancementType;

                formatInfo.querySelector('.format-description').textContent = description;
                
                let requirementsHtml = `<strong>Requirements:</strong> `;
                requirementsHtml += `Min ${minParticipants} participants`;
                if (maxParticipants && maxParticipants !== 'Unlimited') {
                    requirementsHtml += `, Max ${maxParticipants} participants`;
                }
                if (seedingRequired === 'true') {
                    requirementsHtml += `, Seeding required`;
                }
                if (advancementType) {
                    requirementsHtml += `, ${advancementType.charAt(0).toUpperCase() + advancementType.slice(1)} advancement`;
                }

                formatInfo.querySelector('.format-requirements').innerHTML = requirementsHtml;
                formatInfo.style.display = 'block';
            } else {
                formatInfo.style.display = 'none';
            }
        }
    </script>
</body>
</html>
