<?php
/**
 * Test AJAX Authentication and Tournament Formats
 */

require_once 'auth.php';
require_once '../config/database.php';

// Check if we're authenticated
echo "<h2>AJAX Authentication Test</h2>";

try {
    // Check session
    echo "<h3>Session Status:</h3>";
    echo "<p>Session ID: " . session_id() . "</p>";
    echo "<p>Admin logged in: " . (isset($_SESSION['admin_logged_in']) ? 'Yes' : 'No') . "</p>";
    echo "<p>Admin ID: " . ($_SESSION['admin_id'] ?? 'Not set') . "</p>";
    
    // Test requireAdmin function
    echo "<h3>Testing requireAdmin():</h3>";
    try {
        requireAdmin();
        echo "<p style='color: green;'>✓ requireAdmin() passed</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ requireAdmin() failed: " . $e->getMessage() . "</p>";
    }
    
    // Test database connection
    echo "<h3>Database Connection:</h3>";
    $database = new Database();
    $conn = $database->getConnection();
    echo "<p style='color: green;'>✓ Database connected</p>";
    
    // Check tournament_formats table
    echo "<h3>Tournament Formats Table:</h3>";
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    echo "<p>Tournament formats count: {$count}</p>";
    
    if ($count == 0) {
        echo "<p style='color: red;'>❌ No tournament formats found!</p>";
        echo "<p>Creating basic formats...</p>";
        
        $basic_formats = [
            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'team,individual', 2, null],
            ['Double Elimination', 'double_elimination', 'Two-bracket system', 'team,individual', 3, null],
            ['Round Robin', 'round_robin', 'Every participant plays every other', 'team,individual', 3, 16],
            ['Swiss System', 'swiss_system', 'Academic pairing system', 'academic', 4, null],
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds', 'judged,performance', 3, null]
        ];
        
        foreach ($basic_formats as $format) {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute($format);
            echo "<p style='color: green;'>✓ Added: {$format[0]}</p>";
        }
        
        // Re-check count
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
        $stmt->execute();
        $count = $stmt->fetch()['count'];
        echo "<p>New tournament formats count: {$count}</p>";
    }
    
    // Test the actual AJAX call simulation
    echo "<h3>Simulating AJAX Call:</h3>";
    
    // Simulate POST data
    $_POST['sport_type'] = 'individual';
    
    // Capture the AJAX endpoint output
    ob_start();
    
    // Include the AJAX endpoint
    include 'ajax/get-tournament-formats.php';
    
    $ajax_output = ob_get_clean();
    
    echo "<h4>AJAX Response:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($ajax_output);
    echo "</pre>";
    
    // Try to parse JSON
    $json_data = json_decode($ajax_output, true);
    if ($json_data) {
        echo "<h4>Parsed JSON:</h4>";
        if ($json_data['success']) {
            echo "<p style='color: green;'>✓ Success! Found " . count($json_data['formats']) . " formats</p>";
            foreach ($json_data['formats'] as $format) {
                echo "<p>- {$format['name']} (ID: {$format['id']})</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Error: " . ($json_data['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Invalid JSON response</p>";
    }
    
    // Clean up
    unset($_POST['sport_type']);
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { white-space: pre-wrap; word-wrap: break-word; }
</style>

<script>
// Test the actual AJAX call from JavaScript
function testRealAjax() {
    console.log('Testing real AJAX call...');
    
    fetch('ajax/get-tournament-formats.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'sport_type=individual'
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.text();
    })
    .then(text => {
        console.log('Raw response:', text);
        
        try {
            const data = JSON.parse(text);
            console.log('Parsed JSON:', data);
            
            if (data.success) {
                console.log('✓ Success! Found', data.formats.length, 'formats');
                data.formats.forEach(format => {
                    console.log('- ' + format.name + ' (ID: ' + format.id + ')');
                });
            } else {
                console.error('❌ Error:', data.message);
            }
        } catch (e) {
            console.error('❌ JSON parse error:', e.message);
        }
    })
    .catch(error => {
        console.error('❌ Fetch error:', error);
    });
}

// Run the test automatically
document.addEventListener('DOMContentLoaded', function() {
    console.log('Page loaded, testing AJAX...');
    testRealAjax();
});
</script>

<button onclick="testRealAjax()">Test Real AJAX Call</button>
