<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Tab Test</title>
    <style>
        .tab-button {
            padding: 10px 20px;
            margin: 5px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            cursor: pointer;
        }
        .tab-button.active {
            background: #007bff;
            color: white;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            margin-top: 10px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Simple Tab Test</h1>
    
    <div class="tab-buttons">
        <button class="tab-button active" data-tab="standings" onclick="showTab('standings', this)">
            Standings
        </button>
        <button class="tab-button" data-tab="sports" onclick="showTab('sports', this)">
            Sports
        </button>
        <button class="tab-button" data-tab="registrations" onclick="showTab('registrations', this)">
            Registrations
        </button>
        <button class="tab-button" data-tab="matches" onclick="showTab('matches', this)">
            Matches
        </button>
    </div>
    
    <div id="standings-tab" class="tab-content active">
        <h2>Standings Content</h2>
        <p>This is the standings tab content.</p>
    </div>
    
    <div id="sports-tab" class="tab-content">
        <h2>Sports Content</h2>
        <p>This is the sports tab content.</p>
    </div>
    
    <div id="registrations-tab" class="tab-content">
        <h2>Registrations Content</h2>
        <p>This is the registrations tab content.</p>
    </div>
    
    <div id="matches-tab" class="tab-content">
        <h2>Matches Content</h2>
        <p>This is the matches tab content.</p>
    </div>
    
    <script>
        console.log('Simple tab test loaded');
        
        function showTab(tabName, clickedButton) {
            console.log('showTab called with:', tabName, clickedButton);

            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
                console.log('Hiding:', content.id);
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('Showing:', targetTab.id);
            } else {
                console.error('Tab not found:', tabName + '-tab');
            }

            // Add active class to clicked button
            if (clickedButton) {
                clickedButton.classList.add('active');
                console.log('Activated button:', clickedButton);
            }
        }
        
        // Test event listeners
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            const buttons = document.querySelectorAll('.tab-button');
            console.log('Found buttons:', buttons.length);
            
            buttons.forEach((button, index) => {
                console.log(`Button ${index}:`, button.textContent, 'data-tab:', button.getAttribute('data-tab'));
                
                // Add backup event listener
                button.addEventListener('click', function(e) {
                    console.log('Event listener triggered for:', this.textContent);
                });
            });
        });
    </script>
</body>
</html>
