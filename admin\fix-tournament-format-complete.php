<?php
/**
 * Complete Tournament Format Fix
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Complete Tournament Format Fix</h1>";
echo "<p>This will fix the 'Invalid tournament format' error completely...</p>";

$badminton_event_sport_id = 18;

try {
    $conn->beginTransaction();
    
    echo "<h2>🔄 Step 1: Ensure Tournament Formats Table Exists</h2>";
    
    // Create tournament_formats table if it doesn't exist
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS tournament_formats (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        code VARCHAR(50) NOT NULL UNIQUE,
        description TEXT,
        sport_types VARCHAR(255) DEFAULT 'team,individual',
        min_participants INT DEFAULT 2,
        max_participants INT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $conn->exec($create_table_sql);
    echo "<p style='color: green;'>✅ Tournament formats table ensured</p>";
    
    echo "<h2>🔄 Step 2: Check and Create Tournament Formats</h2>";
    
    // Check if formats exist
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    
    if ($count == 0) {
        echo "<p style='color: orange;'>⚠ No tournament formats found. Creating default formats...</p>";
        
        // Insert basic tournament formats
        $default_formats = [
            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams/participants are eliminated after one loss.', 'team,individual', 2, null],
            ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null],
            ['Round Robin', 'round_robin', 'Every team/participant plays every other team/participant once.', 'team,individual', 3, 16],
            ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null],
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null],
            ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null]
        ];
        
        foreach ($default_formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute($format);
            echo "<p style='color: green;'>✓ Added: {$format[0]}</p>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ Found {$count} existing tournament formats</p>";
    }
    
    echo "<h2>🔄 Step 3: Verify Format ID 1 Exists</h2>";
    
    // Check if format ID 1 exists (what the frontend is trying to use)
    $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = 1");
    $stmt->execute();
    $format_1 = $stmt->fetch();
    
    if (!$format_1) {
        echo "<p style='color: red;'>❌ Format ID 1 doesn't exist. Frontend is trying to use non-existent format!</p>";
        
        // Get the first available format
        $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id LIMIT 1");
        $stmt->execute();
        $first_format = $stmt->fetch();
        
        if ($first_format) {
            echo "<p style='color: green;'>✅ Will use Format ID {$first_format['id']} ({$first_format['name']}) instead</p>";
            $default_format_id = $first_format['id'];
        } else {
            throw new Exception("No tournament formats available!");
        }
    } else {
        echo "<p style='color: green;'>✅ Format ID 1 exists: {$format_1['name']}</p>";
        $default_format_id = 1;
    }
    
    echo "<h2>🔄 Step 4: Test Tournament Creation</h2>";
    
    // Test the exact query that tournament creation uses
    $stmt = $conn->prepare("
        SELECT 
            dsp.id,
            COALESCE(dsp.team_name, d.name) as team_name,
            edr.department_id,
            d.name as department_name,
            dsp.status
        FROM event_department_registrations edr
        JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        JOIN departments d ON edr.department_id = d.id
        WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
    ");
    $stmt->execute([$badminton_event_sport_id]);
    $participants = $stmt->fetchAll();
    
    echo "<p><strong>Participants found:</strong> " . count($participants) . "</p>";
    
    if (count($participants) >= 2) {
        echo "<p style='color: green;'>✅ Sufficient participants for tournament creation</p>";
        
        // Test format validation
        $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
        $stmt->execute([$default_format_id]);
        $test_format = $stmt->fetch();
        
        if ($test_format) {
            echo "<p style='color: green;'>✅ Format validation passed</p>";
            echo "<p><strong>Using Format:</strong> {$test_format['name']} (ID: {$test_format['id']})</p>";
            
            if (count($participants) >= $test_format['min_participants']) {
                echo "<p style='color: green; font-size: 18px;'>🎉 <strong>ALL CHECKS PASSED!</strong> Tournament creation should work now!</p>";
            } else {
                echo "<p style='color: red;'>❌ Not enough participants (" . count($participants) . ") for format minimum ({$test_format['min_participants']})</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Format validation failed</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Not enough participants (" . count($participants) . ") for tournament creation</p>";
    }
    
    $conn->commit();
    
    echo "<h2>🎯 Solution Applied</h2>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ Tournament Format Issue Fixed!</h3>";
    echo "<ul>";
    echo "<li>✅ Tournament formats table created/verified</li>";
    echo "<li>✅ Default tournament formats added</li>";
    echo "<li>✅ Format ID {$default_format_id} is available for use</li>";
    echo "<li>✅ Participant validation passed</li>";
    echo "</ul>";
    echo "<p><strong>Your Badminton tournament creation should now work!</strong></p>";
    echo "<p><a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 18px; font-weight: bold; display: inline-block; margin: 10px 0;'>🏆 Create Badminton Tournament Now</a></p>";
    echo "</div>";
    
    echo "<h3>📋 Available Tournament Formats</h3>";
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
    $stmt->execute();
    $all_formats = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Name</th><th>Code</th><th>Sport Types</th><th>Min Participants</th>";
    echo "</tr>";
    
    foreach ($all_formats as $format) {
        $highlight = ($format['id'] == $default_format_id) ? 'background: #d4edda;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td><strong>{$format['id']}</strong></td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>{$format['sport_types']}</td>";
        echo "<td>{$format['min_participants']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><em>Highlighted row shows the format that will be used by default.</em></p>";
    
    echo "<h3>🔧 Technical Details</h3>";
    echo "<details>";
    echo "<summary>Click to see technical information</summary>";
    echo "<ul>";
    echo "<li><strong>Default Format ID:</strong> {$default_format_id}</li>";
    echo "<li><strong>Frontend Location:</strong> manage-category.php line 1320</li>";
    echo "<li><strong>Backend Validation:</strong> admin/ajax/create-tournament.php line 107-112</li>";
    echo "<li><strong>Participants Query:</strong> Unified registration system with confirmed status</li>";
    echo "</ul>";
    echo "</details>";
    
} catch (Exception $e) {
    $conn->rollBack();
    echo "<p style='color: red; font-size: 18px;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database connection and try again.</p>";
}
?>
