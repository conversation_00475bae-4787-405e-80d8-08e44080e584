<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$current_admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? 1;
$event = getEventById($conn, $event_id);
if (!$event) {
    $event = ['name' => 'Test Event', 'description' => 'Test Description'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Tab Test - <?php echo htmlspecialchars($event['name']); ?></title>
    
    <!-- Include admin styles -->
    <?php include 'includes/admin-styles.php'; ?>
    
    <style>
        .main-content { padding: 20px; }
        .section-tabs {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            border-bottom: 2px solid #e9ecef;
        }
        .tab-button {
            padding: 12px 24px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-bottom: none;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
            position: relative;
        }
        .tab-button.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 0 8px 8px 8px;
            background: white;
        }
        .tab-content.active {
            display: block;
        }
        .debug-panel {
            background: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <h1>Minimal Tab Test: <?php echo htmlspecialchars($event['name']); ?></h1>
        
        <div class="debug-panel" id="debugPanel">
            <strong>Debug Log:</strong><br>
            <div id="debugLog"></div>
        </div>
        
        <div class="section-tabs">
            <button class="tab-button active" data-tab="standings" onclick="showTab('standings', this)">
                Standings
            </button>
            <button class="tab-button" data-tab="sports" onclick="showTab('sports', this)">
                Sports
            </button>
            <button class="tab-button" data-tab="registrations" onclick="showTab('registrations', this)">
                Registrations
            </button>
            <button class="tab-button" data-tab="matches" onclick="showTab('matches', this)">
                Matches
            </button>
        </div>
        
        <div id="standings-tab" class="tab-content active">
            <h2>Standings Content</h2>
            <p>This is the standings tab content.</p>
        </div>
        
        <div id="sports-tab" class="tab-content">
            <h2>Sports Content</h2>
            <p>This is the sports tab content.</p>
        </div>
        
        <div id="registrations-tab" class="tab-content">
            <h2>Registrations Content</h2>
            <p>This is the registrations tab content.</p>
        </div>
        
        <div id="matches-tab" class="tab-content">
            <h2>Matches Content</h2>
            <p>This is the matches tab content.</p>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testAllTabs()" class="btn btn-primary">Test All Tabs</button>
            <button onclick="clearDebugLog()" class="btn btn-secondary">Clear Log</button>
        </div>
    </div>

    <script>
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearDebugLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        debugLog('Script loaded');
        
        function showTab(tabName, clickedButton) {
            debugLog(`showTab called: ${tabName}, button: ${clickedButton ? clickedButton.textContent.trim() : 'null'}`);

            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
                debugLog(`Hiding: ${content.id}`);
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                debugLog(`Showing: ${targetTab.id}`);
            } else {
                debugLog(`ERROR: Tab not found: ${tabName}-tab`);
            }

            // Add active class to clicked button
            if (clickedButton) {
                clickedButton.classList.add('active');
                debugLog(`Activated button: ${clickedButton.textContent.trim()}`);
            } else {
                debugLog('WARNING: No clicked button provided');
            }
        }
        
        function testAllTabs() {
            debugLog('Starting automatic tab test...');
            const tabs = ['standings', 'sports', 'registrations', 'matches'];
            let index = 0;
            
            function testNext() {
                if (index < tabs.length) {
                    const tabName = tabs[index];
                    const button = document.querySelector(`[data-tab="${tabName}"]`);
                    debugLog(`Testing tab: ${tabName}`);
                    showTab(tabName, button);
                    index++;
                    setTimeout(testNext, 1000);
                } else {
                    debugLog('Tab test completed');
                }
            }
            
            testNext();
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM loaded');
            
            const tabButtons = document.querySelectorAll('.tab-button');
            debugLog(`Found ${tabButtons.length} tab buttons`);
            
            const tabContents = document.querySelectorAll('.tab-content');
            debugLog(`Found ${tabContents.length} tab contents`);
            
            // Test after a short delay
            setTimeout(() => {
                debugLog('Running initial test...');
                showTab('standings', document.querySelector('[data-tab="standings"]'));
            }, 1000);
        });
        
        debugLog('Script setup complete');
    </script>
</body>
</html>
