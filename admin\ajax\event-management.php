<?php
/**
 * AJAX Handler for Event Management Operations
 * Handles sports management, registrations, and other event-specific operations
 */

// Prevent any output before JSON response
ob_start();

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Clear any output that might have been generated
ob_clean();

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

// Validate CSRF token for state-changing operations
$csrf_required_actions = ['add_sport', 'remove_sport', 'register_department', 'register_department_unified', 'update_registration_status', 'remove_department_registration'];
if (in_array($_POST['action'] ?? '', $csrf_required_actions)) {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid security token. Please refresh the page and try again.'
        ]);
        exit;
    }
}

try {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'check_sport_status':
            $event_id = $_POST['event_id'] ?? 0;
            $sport_id = $_POST['sport_id'] ?? 0;

            if (!$event_id || !$sport_id) {
                throw new Exception('Event ID and Sport ID are required');
            }

            // Check if sport is added to event
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$event_id, $sport_id]);
            $event_sport = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'exists' => !empty($event_sport),
                'event_sport_id' => $event_sport ? $event_sport['id'] : null
            ]);
            break;

        case 'remove_sport':
            $event_sport_id = $_POST['event_sport_id'] ?? 0;
            if (!$event_sport_id) {
                throw new Exception('Event sport ID is required');
            }
            
            // Start transaction
            $conn->beginTransaction();

            // Delete sport categories first (should cascade automatically, but being explicit)
            $stmt = $conn->prepare("DELETE FROM sport_categories WHERE event_sport_id = ?");
            $stmt->execute([$event_sport_id]);

            // Delete matches (foreign key constraint)
            $stmt = $conn->prepare("DELETE FROM matches WHERE event_sport_id = ?");
            $stmt->execute([$event_sport_id]);

            // Delete registrations
            $stmt = $conn->prepare("DELETE FROM registrations WHERE event_sport_id = ?");
            $stmt->execute([$event_sport_id]);

            // Delete event sport
            $stmt = $conn->prepare("DELETE FROM event_sports WHERE id = ?");
            $stmt->execute([$event_sport_id]);
            
            $conn->commit();
            
            logAdminActivity('REMOVE_SPORT_FROM_EVENT', 'event_sports', $event_sport_id);
            
            echo json_encode([
                'success' => true,
                'message' => 'Sport removed from event successfully'
            ]);
            break;
            
        case 'add_sport':
            $event_id = $_POST['event_id'] ?? 0;
            $sport_id = $_POST['sport_id'] ?? 0;
            $tournament_format_id = $_POST['tournament_format_id'] ?? null;
            $seeding_method = $_POST['seeding_method'] ?? 'random';
            $max_teams = $_POST['max_teams'] ?? null;
            $registration_deadline = $_POST['registration_deadline'] ?? null;

            if (!$event_id || !$sport_id || !$tournament_format_id) {
                throw new Exception('Event ID, Sport ID, and Tournament Format are required');
            }

            // Check if sport already added to event
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$event_id, $sport_id]);
            if ($stmt->fetch()) {
                throw new Exception('Sport already added to this event');
            }

            // Get tournament format details for backward compatibility
            $stmt = $conn->prepare("SELECT code FROM tournament_formats WHERE id = ?");
            $stmt->execute([$tournament_format_id]);
            $format = $stmt->fetch();
            $bracket_type = $format ? $format['code'] : 'single_elimination';

            // Insert event sport
            $stmt = $conn->prepare("
                INSERT INTO event_sports (event_id, sport_id, bracket_type, max_teams, registration_deadline)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $event_id,
                $sport_id,
                $bracket_type,
                $max_teams ?: null,
                $registration_deadline ?: null
            ]);

            $event_sport_id = $conn->lastInsertId();

            // Create basic tournament record if format is selected
            $tournamentId = null;
            if ($tournament_format_id) {
                try {
                    // Get sport name for tournament name
                    $stmt = $conn->prepare("SELECT name FROM sports WHERE id = ?");
                    $stmt->execute([$sport_id]);
                    $sport = $stmt->fetch();
                    $tournamentName = $sport['name'] . ' Tournament';

                    // Create basic tournament record (structure will be created when registrations are complete)
                    $stmt = $conn->prepare("
                        INSERT INTO tournaments (event_sport_id, tournament_format_id, name, seeding_method, settings, status)
                        VALUES (?, ?, ?, ?, ?, 'setup')
                    ");
                    $stmt->execute([
                        $event_sport_id,
                        $tournament_format_id,
                        $tournamentName,
                        $seeding_method,
                        json_encode([
                            'scoring_config' => [
                                'points_win' => 3,
                                'points_draw' => 1,
                                'points_loss' => 0
                            ]
                        ])
                    ]);

                    $tournamentId = $conn->lastInsertId();

                } catch (Exception $e) {
                    // Log tournament creation error but don't fail the sport addition
                    error_log("Tournament creation failed for event_sport_id {$event_sport_id}: " . $e->getMessage());
                }
            }

            // AUTOMATICALLY ADD ALL REGISTERED DEPARTMENTS TO THIS NEW SPORT
            $departments_added = 0;
            $stmt = $conn->prepare("SHOW TABLES LIKE 'event_department_registrations'");
            $stmt->execute();
            $new_system_available = $stmt->fetch() !== false;

            if ($new_system_available) {
                require_once '../../includes/department_registration.php';
                $result = addAllRegisteredDepartmentsToSport($conn, $event_sport_id);
                if ($result['success']) {
                    $departments_added = $result['departments_added'];
                }
            }

            logAdminActivity('ADD_SPORT_TO_EVENT', 'event_sports', $event_sport_id,
                null, "Added sport to event (Event: {$event_id}, Sport: {$sport_id}, Format: {$tournament_format_id})");

            echo json_encode([
                'success' => true,
                'message' => "Sport added to event successfully. {$departments_added} departments automatically registered.",
                'id' => $event_sport_id,
                'tournament_id' => $tournamentId ?? null,
                'departments_added' => $departments_added
            ]);
            break;
            
        case 'register_department':
            $event_sport_id = $_POST['event_sport_id'] ?? 0;
            $department_id = $_POST['department_id'] ?? 0;
            $team_name = $_POST['team_name'] ?? '';
            $participants = $_POST['participants'] ?? '';
            $status = $_POST['status'] ?? 'pending';
            
            if (!$event_sport_id || !$department_id) {
                throw new Exception('Event sport ID and Department ID are required');
            }
            
            // Check if department already registered
            $stmt = $conn->prepare("SELECT id FROM registrations WHERE event_sport_id = ? AND department_id = ?");
            $stmt->execute([$event_sport_id, $department_id]);
            if ($stmt->fetch()) {
                throw new Exception('Department already registered for this sport');
            }
            
            // Process participants
            $participant_list = [];
            if (!empty($participants)) {
                $lines = explode("\n", trim($participants));
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line)) {
                        $participant_list[] = $line;
                    }
                }
            }
            
            // Check if new department-centric system is available
            $stmt = $conn->prepare("SHOW TABLES LIKE 'event_department_registrations'");
            $stmt->execute();
            $new_system_available = $stmt->fetch() !== false;

            $department_sport_participation_id = null;

            if ($new_system_available) {
                // Try to integrate with new department-centric system
                require_once '../../includes/department_registration.php';

                // Get event ID from event_sport_id
                $stmt = $conn->prepare("SELECT event_id FROM event_sports WHERE id = ?");
                $stmt->execute([$event_sport_id]);
                $event_sport = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($event_sport) {
                    $event_id = $event_sport['event_id'];

                    // Check if department has unified registration for this event
                    if (!isDepartmentRegisteredForEvent($conn, $event_id, $department_id)) {
                        // Create unified department registration
                        $reg_data = [
                            'status' => $status,
                            'notes' => 'Auto-created from sport-specific registration'
                        ];
                        $dept_reg_result = registerDepartmentForEvent($conn, $event_id, $department_id, $reg_data);

                        if (!$dept_reg_result['success']) {
                            // Continue with old system if new system fails
                            error_log("Failed to create department registration: " . $dept_reg_result['message']);
                        }
                    }

                    // Try to add sport participation to new system
                    $dept_registration = getDepartmentEventRegistration($conn, $event_id, $department_id);
                    if ($dept_registration) {
                        $participation_data = [
                            'team_name' => $team_name,
                            'participants' => $participant_list,
                            'status' => $status,
                            'notes' => 'Created from sport-specific registration'
                        ];

                        $participation_result = addDepartmentSportParticipation($conn, $dept_registration['id'], $event_sport_id, $participation_data);
                        if ($participation_result['success']) {
                            $department_sport_participation_id = $participation_result['participation_id'];
                        }
                    }
                }
            }

            // Insert into old registrations table (for backward compatibility)
            $stmt = $conn->prepare("
                INSERT INTO registrations (event_sport_id, department_id, team_name, participants, status, department_sport_participation_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $event_sport_id,
                $department_id,
                $team_name ?: null,
                json_encode($participant_list),
                $status,
                $department_sport_participation_id
            ]);

            $registration_id = $conn->lastInsertId();
            logAdminActivity('REGISTER_DEPARTMENT', 'registrations', $registration_id);

            echo json_encode([
                'success' => true,
                'message' => 'Department registered successfully' . ($new_system_available ? ' (integrated with new system)' : ''),
                'id' => $registration_id,
                'new_system_integrated' => $new_system_available && $department_sport_participation_id !== null
            ]);
            break;
            
        case 'update_registration_status':
            $registration_id = $_POST['registration_id'] ?? 0;
            $status = $_POST['status'] ?? '';
            
            if (!$registration_id || !$status) {
                throw new Exception('Registration ID and status are required');
            }
            
            $stmt = $conn->prepare("UPDATE registrations SET status = ? WHERE id = ?");
            $stmt->execute([$status, $registration_id]);
            
            logAdminActivity('UPDATE_REGISTRATION_STATUS', 'registrations', $registration_id);
            
            echo json_encode([
                'success' => true,
                'message' => 'Registration status updated successfully'
            ]);
            break;
            
        case 'delete_registration':
            $registration_id = $_POST['registration_id'] ?? 0;
            
            if (!$registration_id) {
                throw new Exception('Registration ID is required');
            }
            
            // Start transaction
            $conn->beginTransaction();
            
            // Delete matches involving this registration
            $stmt = $conn->prepare("DELETE FROM matches WHERE team1_id = ? OR team2_id = ?");
            $stmt->execute([$registration_id, $registration_id]);
            
            // Delete registration
            $stmt = $conn->prepare("DELETE FROM registrations WHERE id = ?");
            $stmt->execute([$registration_id]);
            
            $conn->commit();
            
            logAdminActivity('DELETE_REGISTRATION', 'registrations', $registration_id);
            
            echo json_encode([
                'success' => true,
                'message' => 'Registration deleted successfully'
            ]);
            break;

        case 'announce_winner':
            $event_id = $_POST['event_id'] ?? 0;

            if (!$event_id) {
                throw new Exception('Event ID is required');
            }

            // Get event details
            $stmt = $conn->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $event = $stmt->fetch();

            if (!$event) {
                throw new Exception('Event not found');
            }

            // Get winner (top department by points)
            $stmt = $conn->prepare("
                SELECT
                    d.id, d.name as department_name, d.abbreviation,
                    COALESCE(SUM(CASE
                        WHEN m.winner_id = r.id THEN 3
                        WHEN m.status = 'completed' AND m.winner_id IS NULL AND (m.team1_id = r.id OR m.team2_id = r.id) THEN 1
                        ELSE 0
                    END), 0) as total_points
                FROM departments d
                LEFT JOIN registrations r ON d.id = r.department_id
                LEFT JOIN event_sports es ON r.event_sport_id = es.id AND es.event_id = ?
                LEFT JOIN matches m ON (m.team1_id = r.id OR m.team2_id = r.id) AND m.status = 'completed'
                WHERE d.id IN (
                    SELECT DISTINCT r2.department_id
                    FROM registrations r2
                    JOIN event_sports es2 ON r2.event_sport_id = es2.id
                    WHERE es2.event_id = ?
                )
                GROUP BY d.id, d.name, d.abbreviation
                ORDER BY total_points DESC
                LIMIT 1
            ");
            $stmt->execute([$event_id, $event_id]);
            $winner = $stmt->fetch();

            if (!$winner) {
                throw new Exception('No winner could be determined');
            }

            // Update event status and winner
            $stmt = $conn->prepare("UPDATE events SET status = 'completed', winner_id = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$winner['id'], $event_id]);

            logAdminActivity('ANNOUNCE_WINNER', 'events', $event_id, "Winner: {$winner['department_name']} ({$winner['total_points']} points)");

            echo json_encode([
                'success' => true,
                'message' => "Winner announced: {$winner['department_name']} with {$winner['total_points']} points!",
                'winner' => $winner
            ]);
            break;

        case 'register_department_unified':
            require_once '../../includes/department_registration.php';

            $event_id = $_POST['event_id'] ?? 0;
            $department_id = $_POST['department_id'] ?? 0;
            $status = $_POST['status'] ?? 'pending';
            $notes = $_POST['notes'] ?? '';

            if (!$event_id || !$department_id) {
                throw new Exception('Event ID and Department ID are required');
            }

            // Get department contact information from departments table
            $stmt = $conn->prepare("SELECT contact_person, contact_email, contact_phone FROM departments WHERE id = ?");
            $stmt->execute([$department_id]);
            $department_info = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$department_info) {
                throw new Exception('Department not found');
            }

            $registration_data = [
                'contact_person' => $department_info['contact_person'] ?? '',
                'contact_email' => $department_info['contact_email'] ?? '',
                'contact_phone' => $department_info['contact_phone'] ?? '',
                'status' => $status,
                'notes' => $notes
            ];

            $result = registerDepartmentForEvent($conn, $event_id, $department_id, $registration_data);

            if (!$result['success']) {
                throw new Exception($result['message']);
            }

            // Count how many sports the department was added to
            $stmt = $conn->prepare("
                SELECT COUNT(*) as sports_count
                FROM department_sport_participations dsp
                WHERE dsp.event_department_registration_id = ?
            ");
            $stmt->execute([$result['registration_id']]);
            $sports_added = $stmt->fetchColumn();

            logAdminActivity('REGISTER_DEPARTMENT_UNIFIED', 'event_department_registrations', $result['registration_id']);

            echo json_encode([
                'success' => true,
                'message' => 'Department registered successfully for entire event',
                'registration_id' => $result['registration_id'],
                'sports_added' => $sports_added
            ]);
            break;

        case 'get_sport_participations':
            require_once '../../includes/department_registration.php';

            $registration_id = $_POST['registration_id'] ?? 0;

            if (!$registration_id) {
                throw new Exception('Registration ID is required');
            }

            $stmt = $conn->prepare("
                SELECT
                    dsp.id,
                    dsp.status,
                    dsp.notes,
                    s.name as sport_name,
                    JSON_LENGTH(COALESCE(dsp.participants, '[]')) as participants_count
                FROM department_sport_participations dsp
                JOIN event_sports es ON dsp.event_sport_id = es.id
                JOIN sports s ON es.sport_id = s.id
                WHERE dsp.event_department_registration_id = ?
                ORDER BY s.name
            ");
            $stmt->execute([$registration_id]);
            $participations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'participations' => $participations
            ]);
            break;

        case 'update_registration_status':
            // Check if this is for unified registration
            $registration_id = $_POST['registration_id'] ?? 0;
            $status = $_POST['status'] ?? '';

            if (!$registration_id || !$status) {
                throw new Exception('Registration ID and status are required');
            }

            // Try unified registration first
            $stmt = $conn->prepare("SELECT id FROM event_department_registrations WHERE id = ?");
            $stmt->execute([$registration_id]);
            $is_unified = $stmt->fetch();

            if ($is_unified) {
                $stmt = $conn->prepare("UPDATE event_department_registrations SET status = ? WHERE id = ?");
                $stmt->execute([$status, $registration_id]);

                logAdminActivity('UPDATE_UNIFIED_REGISTRATION_STATUS', 'event_department_registrations', $registration_id);
            } else {
                // Fall back to old system
                $stmt = $conn->prepare("UPDATE registrations SET status = ? WHERE id = ?");
                $stmt->execute([$status, $registration_id]);

                logAdminActivity('UPDATE_REGISTRATION_STATUS', 'registrations', $registration_id);
            }

            echo json_encode([
                'success' => true,
                'message' => 'Registration status updated successfully'
            ]);
            break;

        case 'remove_department_registration':
            require_once '../../includes/department_registration.php';

            $registration_id = $_POST['registration_id'] ?? 0;

            if (!$registration_id) {
                throw new Exception('Registration ID is required');
            }

            // Start transaction
            $conn->beginTransaction();

            // Remove all sport participations
            $stmt = $conn->prepare("DELETE FROM department_sport_participations WHERE event_department_registration_id = ?");
            $stmt->execute([$registration_id]);

            // Remove the main registration
            $stmt = $conn->prepare("DELETE FROM event_department_registrations WHERE id = ?");
            $stmt->execute([$registration_id]);

            $conn->commit();

            logAdminActivity('REMOVE_UNIFIED_REGISTRATION', 'event_department_registrations', $registration_id);

            echo json_encode([
                'success' => true,
                'message' => 'Department registration removed successfully'
            ]);
            break;

        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }

    // Clear any output buffer to ensure clean JSON
    ob_clean();

    // Log the error for debugging
    error_log("Event Management AJAX Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'action' => $_POST['action'] ?? 'unknown'
        ]
    ]);
}

// Ensure output buffer is flushed
ob_end_flush();
?>
