<?php
/**
 * Fix script for sport deletion and re-addition issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set content type
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sport Deletion Fix - SC_IMS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .fix-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
    </style>
</head>
<body>
    <h1>Sport Deletion and Re-addition Fix</h1>
    
    <?php
    if ($_POST['action'] ?? '' === 'cleanup_orphaned_records') {
        echo '<div class="fix-section info"><h3>Cleaning Up Orphaned Records</h3>';
        
        try {
            $conn->beginTransaction();
            
            // Find and clean up orphaned sport categories
            echo '<h4>Cleaning Sport Categories</h4>';
            $stmt = $conn->prepare("
                DELETE sc FROM sport_categories sc
                LEFT JOIN event_sports es ON sc.event_sport_id = es.id
                WHERE es.id IS NULL
            ");
            $stmt->execute();
            $deleted_categories = $stmt->rowCount();
            echo "<p>Deleted {$deleted_categories} orphaned sport categories</p>";
            
            // Find and clean up orphaned registrations
            echo '<h4>Cleaning Registrations</h4>';
            $stmt = $conn->prepare("
                DELETE r FROM registrations r
                LEFT JOIN event_sports es ON r.event_sport_id = es.id
                WHERE es.id IS NULL
            ");
            $stmt->execute();
            $deleted_registrations = $stmt->rowCount();
            echo "<p>Deleted {$deleted_registrations} orphaned registrations</p>";
            
            // Find and clean up orphaned matches
            echo '<h4>Cleaning Matches</h4>';
            $stmt = $conn->prepare("
                DELETE m FROM matches m
                LEFT JOIN event_sports es ON m.event_sport_id = es.id
                WHERE es.id IS NULL
            ");
            $stmt->execute();
            $deleted_matches = $stmt->rowCount();
            echo "<p>Deleted {$deleted_matches} orphaned matches</p>";
            
            $conn->commit();
            echo '<p class="success">✓ Cleanup completed successfully</p>';
            
        } catch (Exception $e) {
            $conn->rollBack();
            echo '<p class="error">Error during cleanup: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        
        echo '</div>';
    }
    
    if ($_POST['action'] ?? '' === 'fix_constraints') {
        echo '<div class="fix-section info"><h3>Fixing Database Constraints</h3>';
        
        try {
            // Check if unique constraint exists
            $stmt = $conn->prepare("
                SELECT CONSTRAINT_NAME 
                FROM information_schema.TABLE_CONSTRAINTS 
                WHERE TABLE_NAME = 'event_sports' 
                AND CONSTRAINT_TYPE = 'UNIQUE'
                AND TABLE_SCHEMA = DATABASE()
            ");
            $stmt->execute();
            $constraints = $stmt->fetchAll();
            
            echo '<h4>Current Unique Constraints on event_sports:</h4>';
            if ($constraints) {
                foreach ($constraints as $constraint) {
                    echo "<p>- {$constraint['CONSTRAINT_NAME']}</p>";
                }
            } else {
                echo "<p>No unique constraints found</p>";
            }
            
            // Check for duplicate records that might be causing issues
            echo '<h4>Checking for Duplicate Records</h4>';
            $stmt = $conn->prepare("
                SELECT event_id, sport_id, COUNT(*) as count
                FROM event_sports
                GROUP BY event_id, sport_id
                HAVING COUNT(*) > 1
            ");
            $stmt->execute();
            $duplicates = $stmt->fetchAll();
            
            if ($duplicates) {
                echo '<p class="warning">Found duplicate records:</p>';
                foreach ($duplicates as $dup) {
                    echo "<p>Event {$dup['event_id']}, Sport {$dup['sport_id']}: {$dup['count']} records</p>";
                }
            } else {
                echo '<p class="success">✓ No duplicate records found</p>';
            }
            
        } catch (Exception $e) {
            echo '<p class="error">Error checking constraints: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
        
        echo '</div>';
    }
    ?>
    
    <div class="fix-section">
        <h3>Current Database State</h3>
        <?php
        // Show current event_sports records
        $stmt = $conn->prepare("
            SELECT es.id, es.event_id, es.sport_id, s.name as sport_name, e.name as event_name
            FROM event_sports es
            JOIN sports s ON es.sport_id = s.id
            JOIN events e ON es.event_id = e.id
            ORDER BY es.event_id, s.name
        ");
        $stmt->execute();
        $event_sports = $stmt->fetchAll();
        
        if ($event_sports) {
            echo '<h4>Current Event-Sport Relationships:</h4>';
            echo '<table border="1" cellpadding="5" cellspacing="0">';
            echo '<tr><th>ID</th><th>Event</th><th>Sport</th><th>Categories</th><th>Registrations</th></tr>';
            foreach ($event_sports as $es) {
                // Count related records
                $stmt = $conn->prepare("SELECT COUNT(*) FROM sport_categories WHERE event_sport_id = ?");
                $stmt->execute([$es['id']]);
                $categories = $stmt->fetchColumn();
                
                $stmt = $conn->prepare("SELECT COUNT(*) FROM registrations WHERE event_sport_id = ?");
                $stmt->execute([$es['id']]);
                $registrations = $stmt->fetchColumn();
                
                echo '<tr>';
                echo '<td>' . $es['id'] . '</td>';
                echo '<td>' . htmlspecialchars($es['event_name']) . '</td>';
                echo '<td>' . htmlspecialchars($es['sport_name']) . '</td>';
                echo '<td>' . $categories . '</td>';
                echo '<td>' . $registrations . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        } else {
            echo '<p>No event-sport relationships found</p>';
        }
        ?>
    </div>
    
    <div class="fix-section">
        <h3>Fix Actions</h3>
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="cleanup_orphaned_records">
            <button type="submit" class="btn-warning">Clean Up Orphaned Records</button>
        </form>
        
        <form method="POST" style="display: inline;">
            <input type="hidden" name="action" value="fix_constraints">
            <button type="submit" class="btn-primary">Check Database Constraints</button>
        </form>
    </div>
    
    <div class="fix-section">
        <h3>Manual Test</h3>
        <p>Test the sport addition manually:</p>
        <button onclick="testAddSport()" class="btn-success">Test Add Basketball to Event 1</button>
        <button onclick="testRemoveSport()" class="btn-danger">Test Remove Basketball from Event 1</button>
        <div id="testResults" style="margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; display: none;"></div>
    </div>
    
    <p><a href="manage-event.php?event_id=1">← Back to Event Management</a></p>
    
    <script>
        function showTestResult(message, isSuccess = true) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.style.display = 'block';
            resultsDiv.className = isSuccess ? 'success' : 'error';
            resultsDiv.innerHTML = message;
        }
        
        function testAddSport() {
            const formData = new FormData();
            formData.append('action', 'add_sport');
            formData.append('event_id', '1');
            formData.append('sport_id', '1');
            formData.append('tournament_format_id', '1');
            formData.append('seeding_method', 'random');
            formData.append('max_teams', '8');
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
            
            fetch('ajax/event-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                showTestResult('Add Sport Result: ' + JSON.stringify(data, null, 2), data.success);
                if (data.success) {
                    setTimeout(() => location.reload(), 2000);
                }
            })
            .catch(error => {
                showTestResult('Add Sport Error: ' + error.message, false);
            });
        }
        
        function testRemoveSport() {
            // Get the first event_sport_id for event 1 and sport 1
            fetch('', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_event_sport_id'
            })
            .then(() => {
                // For simplicity, we'll use a known ID or prompt user
                const eventSportId = prompt('Enter event_sport_id to remove (check table above):');
                if (eventSportId) {
                    const formData = new FormData();
                    formData.append('action', 'remove_sport');
                    formData.append('event_sport_id', eventSportId);
                    formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
                    
                    return fetch('ajax/event-management.php', {
                        method: 'POST',
                        body: formData
                    });
                }
            })
            .then(response => response ? response.json() : null)
            .then(data => {
                if (data) {
                    showTestResult('Remove Sport Result: ' + JSON.stringify(data, null, 2), data.success);
                    if (data.success) {
                        setTimeout(() => location.reload(), 2000);
                    }
                }
            })
            .catch(error => {
                showTestResult('Remove Sport Error: ' + error.message, false);
            });
        }
    </script>
</body>
</html>
