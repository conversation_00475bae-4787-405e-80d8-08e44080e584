<?php
/**
 * AJAX Handler for Database Table Fixes
 */

// Prevent any output before JSON response
ob_start();

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once '../auth.php';
require_once '../../config/database.php';

// Clear any output that might have been generated
ob_clean();

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'check_tables':
            $required_tables = [
                'events', 'sports', 'departments', 'event_sports', 'registrations',
                'matches', 'admin_users', 'audit_logs', 'sport_types', 'tournament_formats',
                'tournaments', 'tournament_rounds', 'tournament_participants', 'tournament_matches'
            ];
            
            $stmt = $conn->prepare("SHOW TABLES");
            $stmt->execute();
            $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $table_status = [];
            foreach ($required_tables as $table) {
                $table_status[$table] = in_array($table, $existing_tables);
            }
            
            echo json_encode([
                'success' => true,
                'tables' => $table_status
            ]);
            break;
            
        case 'create_missing_tables':
            $created_tables = [];
            $errors = [];
            
            // Check which tables exist
            $stmt = $conn->prepare("SHOW TABLES");
            $stmt->execute();
            $existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Create tournaments table if missing
            if (!in_array('tournaments', $existing_tables)) {
                try {
                    $sql = "
                    CREATE TABLE tournaments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        event_sport_id INT NOT NULL,
                        tournament_format_id INT NOT NULL,
                        name VARCHAR(255) NOT NULL,
                        status ENUM('setup', 'in_progress', 'completed') DEFAULT 'setup',
                        max_participants INT DEFAULT 8,
                        current_round INT DEFAULT 1,
                        total_rounds INT DEFAULT 1,
                        seeding_method ENUM('random', 'ranking', 'manual') DEFAULT 'random',
                        bracket_data JSON,
                        settings JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
                        FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $conn->exec($sql);
                    $created_tables[] = 'tournaments';
                } catch (Exception $e) {
                    $errors[] = "tournaments: " . $e->getMessage();
                }
            }
            
            // Create tournament_rounds table if missing
            if (!in_array('tournament_rounds', $existing_tables)) {
                try {
                    $sql = "
                    CREATE TABLE tournament_rounds (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        tournament_id INT NOT NULL,
                        round_number INT NOT NULL,
                        round_name VARCHAR(100) NOT NULL,
                        status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                        start_date DATETIME,
                        end_date DATETIME,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_tournament_round (tournament_id, round_number)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $conn->exec($sql);
                    $created_tables[] = 'tournament_rounds';
                } catch (Exception $e) {
                    $errors[] = "tournament_rounds: " . $e->getMessage();
                }
            }
            
            // Create tournament_participants table if missing
            if (!in_array('tournament_participants', $existing_tables)) {
                try {
                    $sql = "
                    CREATE TABLE tournament_participants (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        tournament_id INT NOT NULL,
                        department_id INT NOT NULL,
                        seed_number INT,
                        status ENUM('active', 'eliminated', 'bye') DEFAULT 'active',
                        registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
                        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_tournament_participant (tournament_id, department_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $conn->exec($sql);
                    $created_tables[] = 'tournament_participants';
                } catch (Exception $e) {
                    $errors[] = "tournament_participants: " . $e->getMessage();
                }
            }
            
            // Create tournament_matches table if missing
            if (!in_array('tournament_matches', $existing_tables)) {
                try {
                    $sql = "
                    CREATE TABLE tournament_matches (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        tournament_id INT NOT NULL,
                        round_id INT NOT NULL,
                        match_number INT NOT NULL,
                        participant1_id INT,
                        participant2_id INT,
                        winner_id INT,
                        status ENUM('scheduled', 'in_progress', 'completed', 'bye') DEFAULT 'scheduled',
                        scheduled_time DATETIME,
                        start_time DATETIME,
                        end_time DATETIME,
                        score_data JSON,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
                        FOREIGN KEY (round_id) REFERENCES tournament_rounds(id) ON DELETE CASCADE,
                        FOREIGN KEY (participant1_id) REFERENCES tournament_participants(id) ON DELETE SET NULL,
                        FOREIGN KEY (participant2_id) REFERENCES tournament_participants(id) ON DELETE SET NULL,
                        FOREIGN KEY (winner_id) REFERENCES tournament_participants(id) ON DELETE SET NULL
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                    ";
                    $conn->exec($sql);
                    $created_tables[] = 'tournament_matches';
                } catch (Exception $e) {
                    $errors[] = "tournament_matches: " . $e->getMessage();
                }
            }
            
            if (empty($created_tables) && empty($errors)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'All required tables already exist.',
                    'details' => 'No tables needed to be created.'
                ]);
            } elseif (!empty($errors)) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Some tables could not be created.',
                    'details' => 'Created: ' . implode(', ', $created_tables) . "\nErrors: " . implode(', ', $errors)
                ]);
            } else {
                echo json_encode([
                    'success' => true,
                    'message' => 'Successfully created missing tables.',
                    'details' => 'Created tables: ' . implode(', ', $created_tables)
                ]);
            }
            break;
            
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    // Clear any output buffer to ensure clean JSON
    ob_clean();
    
    // Log the error for debugging
    error_log("Fix Database Tables AJAX Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'file' => basename($e->getFile()),
            'line' => $e->getLine()
        ]
    ]);
}

// Ensure output buffer is flushed
ob_end_flush();
?>
