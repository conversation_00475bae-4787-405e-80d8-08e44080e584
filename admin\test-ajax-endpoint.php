<?php
/**
 * Test AJAX Endpoint for Tournament Formats
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Simulate admin session
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

echo "<h2>Testing Tournament Formats AJAX Endpoint</h2>";

$test_sport_types = ['traditional', 'team', 'individual', 'academic', 'judged', 'performance'];

foreach ($test_sport_types as $sport_type) {
    echo "<h3>Testing sport type: {$sport_type}</h3>";
    
    // Simulate POST request
    $_POST['sport_type'] = $sport_type;
    
    // Capture the output from the AJAX endpoint
    ob_start();
    include 'ajax/get-tournament-formats.php';
    $output = ob_get_clean();
    
    echo "<h4>Raw Response:</h4>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($output);
    echo "</pre>";
    
    // Try to decode JSON
    $decoded = json_decode($output, true);
    if ($decoded) {
        echo "<h4>Parsed JSON:</h4>";
        if ($decoded['success']) {
            echo "<p style='color: green;'>✓ Success: Found " . count($decoded['formats']) . " formats</p>";
            if (!empty($decoded['formats'])) {
                echo "<ul>";
                foreach ($decoded['formats'] as $format) {
                    echo "<li><strong>{$format['name']}</strong> (ID: {$format['id']}, Code: {$format['code']})</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ Error: " . ($decoded['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Invalid JSON response</p>";
    }
    
    echo "<hr>";
}

// Clean up
unset($_POST['sport_type']);
?>

<script>
// Also test via JavaScript fetch (like the actual frontend)
console.log('Testing AJAX endpoint via JavaScript...');

const testSportTypes = ['traditional', 'team', 'individual', 'academic', 'judged', 'performance'];

testSportTypes.forEach(sportType => {
    fetch('ajax/get-tournament-formats.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `sport_type=${encodeURIComponent(sportType)}`
    })
    .then(response => response.text())
    .then(text => {
        console.log(`Raw response for ${sportType}:`, text);
        try {
            const data = JSON.parse(text);
            console.log(`Parsed response for ${sportType}:`, data);
        } catch (e) {
            console.error(`JSON parse error for ${sportType}:`, e);
        }
    })
    .catch(error => {
        console.error(`Fetch error for ${sportType}:`, error);
    });
});
</script>
