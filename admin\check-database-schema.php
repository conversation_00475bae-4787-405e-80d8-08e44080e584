<?php
/**
 * Check Database Schema
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Schema Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>Database Schema Check</h1>
    
    <h3>Available Tables</h3>
    <?php
    try {
        $stmt = $conn->prepare("SHOW TABLES");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo '<table>';
        echo '<tr><th>Table Name</th><th>Status</th></tr>';
        
        $required_tables = [
            'events', 'sports', 'departments', 'event_sports', 'registrations',
            'matches', 'admin_users', 'audit_logs', 'sport_types', 'tournament_formats',
            'tournaments', 'tournament_rounds', 'tournament_participants', 'tournament_matches'
        ];
        
        foreach ($required_tables as $table) {
            $exists = in_array($table, $tables);
            $status = $exists ? '<span class="success">✅ Exists</span>' : '<span class="error">❌ Missing</span>';
            echo "<tr><td>{$table}</td><td>{$status}</td></tr>";
        }
        
        echo '</table>';
        
        echo '<h3>All Tables in Database</h3>';
        echo '<ul>';
        foreach ($tables as $table) {
            echo '<li>' . htmlspecialchars($table) . '</li>';
        }
        echo '</ul>';
        
    } catch (Exception $e) {
        echo '<p class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    ?>
    
    <h3>Check Event Sports Table Structure</h3>
    <?php
    try {
        $stmt = $conn->prepare("DESCRIBE event_sports");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo '<table>';
        echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>';
        foreach ($columns as $column) {
            echo '<tr>';
            echo '<td>' . htmlspecialchars($column['Field']) . '</td>';
            echo '<td>' . htmlspecialchars($column['Type']) . '</td>';
            echo '<td>' . htmlspecialchars($column['Null']) . '</td>';
            echo '<td>' . htmlspecialchars($column['Key']) . '</td>';
            echo '<td>' . htmlspecialchars($column['Default'] ?? 'NULL') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        
    } catch (Exception $e) {
        echo '<p class="error">Error checking event_sports table: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    ?>
    
    <h3>Current Event 1 Sports</h3>
    <?php
    try {
        $stmt = $conn->prepare("
            SELECT es.*, s.name as sport_name, s.type as sport_type 
            FROM event_sports es 
            LEFT JOIN sports s ON es.sport_id = s.id 
            WHERE es.event_id = 1
        ");
        $stmt->execute();
        $event_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($event_sports)) {
            echo '<p>No sports found for Event 1.</p>';
        } else {
            echo '<table>';
            echo '<tr><th>ID</th><th>Sport ID</th><th>Sport Name</th><th>Type</th><th>Status</th></tr>';
            foreach ($event_sports as $sport) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($sport['id']) . '</td>';
                echo '<td>' . htmlspecialchars($sport['sport_id']) . '</td>';
                echo '<td>' . htmlspecialchars($sport['sport_name'] ?? 'Unknown') . '</td>';
                echo '<td>' . htmlspecialchars($sport['sport_type'] ?? 'Unknown') . '</td>';
                echo '<td>' . htmlspecialchars($sport['status']) . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
        
    } catch (Exception $e) {
        echo '<p class="error">Error checking Event 1 sports: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    ?>
    
    <h3>Test AJAX Response</h3>
    <button onclick="testAjaxResponse()">Test Add Sport AJAX</button>
    <div id="ajax-result"></div>
    
    <script>
        async function testAjaxResponse() {
            const resultDiv = document.getElementById('ajax-result');
            resultDiv.innerHTML = '<p>Testing AJAX response...</p>';
            
            try {
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=check_sport_status&event_id=1&sport_id=1'
                });
                
                const text = await response.text();
                resultDiv.innerHTML += `<p><strong>Response Status:</strong> ${response.status}</p>`;
                resultDiv.innerHTML += `<p><strong>Response Length:</strong> ${text.length} characters</p>`;
                resultDiv.innerHTML += `<p><strong>Raw Response:</strong></p><pre style="background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;">${text}</pre>`;
                
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML += `<p style="color: green;"><strong>✅ Valid JSON Response</strong></p>`;
                    resultDiv.innerHTML += `<pre style="background: #d4edda; padding: 10px; border-radius: 3px;">${JSON.stringify(data, null, 2)}</pre>`;
                } catch (e) {
                    resultDiv.innerHTML += `<p style="color: red;"><strong>❌ JSON Parse Error:</strong> ${e.message}</p>`;
                    
                    // Look for specific issues
                    if (text.includes('<br')) {
                        resultDiv.innerHTML += `<p style="color: red;">❌ Found HTML &lt;br&gt; tags in response</p>`;
                    }
                    if (text.includes('Warning:') || text.includes('Notice:') || text.includes('Error:')) {
                        resultDiv.innerHTML += `<p style="color: red;">❌ Found PHP error messages in response</p>`;
                    }
                    if (text.includes('Fatal error:')) {
                        resultDiv.innerHTML += `<p style="color: red;">❌ Found PHP fatal error in response</p>`;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML += `<p style="color: red;"><strong>❌ Fetch Error:</strong> ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
