<?php
/**
 * Tournament Fix Status
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Tournament Fix Status</h1>";
echo "<p>Current status of tournament system fixes and next steps...</p>";

$issues = [];
$fixes_needed = [];

echo "<h2>📊 Current System Status</h2>";

// Check 1: tournament_structure_id column in matches table
echo "<h3>1. Matches Table Tournament Columns</h3>";
try {
    $stmt = $conn->prepare("DESCRIBE matches");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $existing_columns = array_column($columns, 'Field');
    $required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f5f5f5;'><th>Column</th><th>Status</th></tr>";
    
    foreach ($required_columns as $col) {
        if (in_array($col, $existing_columns)) {
            echo "<tr><td>{$col}</td><td style='color: #28a745;'>✅ EXISTS</td></tr>";
        } else {
            echo "<tr><td>{$col}</td><td style='color: #dc3545;'>❌ MISSING</td></tr>";
            $issues[] = "Missing column in matches table: {$col}";
            $fixes_needed[] = "Add {$col} column to matches table";
        }
    }
    echo "</table>";
    
    // Test column access
    try {
        $stmt = $conn->prepare("SELECT tournament_structure_id FROM matches LIMIT 1");
        $stmt->execute();
        echo "<p style='color: #28a745;'>✅ tournament_structure_id column is accessible</p>";
    } catch (Exception $e) {
        echo "<p style='color: #dc3545;'>❌ tournament_structure_id column access failed</p>";
        $issues[] = "tournament_structure_id column not accessible";
        $fixes_needed[] = "Fix tournament_structure_id column access";
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>❌ Could not check matches table: " . $e->getMessage() . "</p>";
    $issues[] = "Cannot access matches table";
}

// Check 2: Tournament tables exist
echo "<h3>2. Tournament Tables</h3>";
$required_tables = ['tournament_formats', 'tournament_structures', 'tournament_rounds', 'tournament_participants'];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr style='background: #f5f5f5;'><th>Table</th><th>Status</th><th>Row Count</th></tr>";

foreach ($required_tables as $table) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        $exists = $stmt->fetch();
        
        if ($exists) {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM {$table}");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<tr><td>{$table}</td><td style='color: #28a745;'>✅ EXISTS</td><td>{$count}</td></tr>";
        } else {
            echo "<tr><td>{$table}</td><td style='color: #dc3545;'>❌ MISSING</td><td>-</td></tr>";
            $issues[] = "Missing table: {$table}";
            $fixes_needed[] = "Create {$table} table";
        }
    } catch (Exception $e) {
        echo "<tr><td>{$table}</td><td style='color: #dc3545;'>❌ ERROR</td><td>-</td></tr>";
        $issues[] = "Error checking table {$table}: " . $e->getMessage();
    }
}
echo "</table>";

// Check 3: Tournament format with algorithm class
echo "<h3>3. Tournament Format Configuration</h3>";
try {
    $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = 7");
    $stmt->execute();
    $format = $stmt->fetch();
    
    if ($format) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><td><strong>ID</strong></td><td>{$format['id']}</td></tr>";
        echo "<tr><td><strong>Name</strong></td><td>{$format['name']}</td></tr>";
        echo "<tr><td><strong>Code</strong></td><td>{$format['code']}</td></tr>";
        echo "<tr><td><strong>Algorithm Class</strong></td><td>" . ($format['algorithm_class'] ?: 'MISSING') . "</td></tr>";
        echo "</table>";
        
        if (empty($format['algorithm_class'])) {
            echo "<p style='color: #dc3545;'>❌ Algorithm class is missing</p>";
            $issues[] = "Tournament format missing algorithm_class";
            $fixes_needed[] = "Update tournament format with algorithm_class";
        } else {
            echo "<p style='color: #28a745;'>✅ Algorithm class configured</p>";
        }
    } else {
        echo "<p style='color: #dc3545;'>❌ Tournament format ID 7 not found</p>";
        $issues[] = "Tournament format ID 7 missing";
        $fixes_needed[] = "Create tournament format ID 7 (Single Elimination)";
    }
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>❌ Could not check tournament format: " . $e->getMessage() . "</p>";
    $issues[] = "Cannot access tournament_formats table";
}

echo "<h2>📋 Summary</h2>";

if (empty($issues)) {
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 <strong>ALL SYSTEMS READY!</strong></h3>";
    echo "<p style='font-size: 18px;'>Tournament system is fully configured and ready to use.</p>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='test-tournament-creation-now.php' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block; margin-right: 10px;'>🧪 TEST TOURNAMENT</a>";
    echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #007bff; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE TOURNAMENT</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>ISSUES FOUND: " . count($issues) . "</strong></h3>";
    echo "<h4>Problems:</h4>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li style='color: #721c24; font-weight: bold;'>{$issue}</li>";
    }
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border: 2px solid #ffc107; margin: 20px 0;'>";
    echo "<h3 style='color: #856404; margin-top: 0;'>🔧 <strong>FIXES NEEDED:</strong></h3>";
    echo "<ol>";
    foreach ($fixes_needed as $fix) {
        echo "<li style='color: #856404; font-weight: bold;'>{$fix}</li>";
    }
    echo "</ol>";
    echo "</div>";
}

echo "<h2>🔧 Available Fix Options</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; margin: 20px 0;'>";
echo "<h3>Option 1: Automated Fixes</h3>";
echo "<ul>";
echo "<li><a href='direct-matches-table-fix.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;'>🔧 Direct Matches Table Fix</a></li>";
echo "<li><a href='comprehensive-schema-fix.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 5px;'>🔧 Comprehensive Schema Fix</a></li>";
echo "</ul>";

echo "<h3>Option 2: Manual Fix</h3>";
echo "<p>If automated fixes don't work, run the SQL commands manually:</p>";
echo "<ol>";
echo "<li>Open phpMyAdmin or your MySQL client</li>";
echo "<li>Select your SC_IMS database</li>";
echo "<li>Run the SQL commands from: <a href='manual-schema-fix.sql' target='_blank' style='background: #6c757d; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px;'>📄 manual-schema-fix.sql</a></li>";
echo "</ol>";

echo "<h3>Option 3: Step-by-Step Manual Commands</h3>";
echo "<p>Copy and paste these SQL commands one by one:</p>";
echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 4px; border: 1px solid #dee2e6;'>";
echo "ALTER TABLE matches ADD COLUMN tournament_structure_id INT NULL;\n";
echo "ALTER TABLE matches ADD COLUMN tournament_round_id INT NULL;\n";
echo "ALTER TABLE matches ADD COLUMN bracket_position VARCHAR(50) NULL;\n";
echo "ALTER TABLE matches ADD COLUMN is_bye_match BOOLEAN DEFAULT FALSE;\n";
echo "</pre>";
echo "</div>";

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='tournament-fix-status.php'>🔄 Refresh Status</a></li>";
echo "<li><a href='direct-matches-table-fix.php'>🔧 Run Direct Fix</a></li>";
echo "<li><a href='test-tournament-creation-now.php'>🧪 Test Tournament Creation</a></li>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation</a></li>";
echo "</ul>";
?>
