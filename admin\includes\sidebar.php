<?php
/**
 * Admin Sidebar Navigation Component
 * Reusable sidebar for all admin pages
 */

// Get current page for active navigation highlighting
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Sidebar Overlay for Mobile -->
<div class="sidebar-overlay" id="sidebarOverlay"></div>

<!-- Admin Sidebar -->
<div class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <div class="sidebar-logo-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="sidebar-logo-text">
                <h1>SC_IMS</h1>
                <p>Admin Panel</p>
            </div>
        </div>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <div class="nav-section-title">Main</div>
            <div class="nav-item">
                <a href="index.php" class="nav-link <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Management</div>
            <div class="nav-item">
                <a href="events.php" class="nav-link <?php echo ($current_page == 'events.php') ? 'active' : ''; ?>">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Events</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="sports.php" class="nav-link <?php echo ($current_page == 'sports.php') ? 'active' : ''; ?>">
                    <i class="fas fa-futbol"></i>
                    <span>Sports</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="departments.php" class="nav-link <?php echo ($current_page == 'departments.php') ? 'active' : ''; ?>">
                    <i class="fas fa-building"></i>
                    <span>Departments</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="matches.php" class="nav-link <?php echo ($current_page == 'matches.php') ? 'active' : ''; ?>">
                    <i class="fas fa-trophy"></i>
                    <span>Matches</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="department-registrations.php" class="nav-link <?php echo ($current_page == 'department-registrations.php') ? 'active' : ''; ?>">
                    <i class="fas fa-user-plus"></i>
                    <span>Department Registration</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="events.php" class="nav-link <?php echo ($current_page == 'event-sports.php') ? 'active' : ''; ?>" title="Manage event sports through Events">
                    <i class="fas fa-link"></i>
                    <span>Event Sports</span>
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Competition</div>
            <div class="nav-item">
                <a href="overall-standings.php" class="nav-link <?php echo ($current_page == 'overall-standings.php') ? 'active' : ''; ?>">
                    <i class="fas fa-trophy"></i>
                    <span>Overall Standings</span>
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Analytics</div>
            <div class="nav-item">
                <a href="reports.php" class="nav-link <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="system-optimization.php" class="nav-link <?php echo ($current_page == 'system-optimization.php') ? 'active' : ''; ?>">
                    <i class="fas fa-cogs"></i>
                    <span>System</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="migrate-registrations.php" class="nav-link <?php echo ($current_page == 'migrate-registrations.php') ? 'active' : ''; ?>">
                    <i class="fas fa-exchange-alt"></i>
                    <span>Migration Tool</span>
                </a>
            </div>
        </div>

        <div class="nav-section">
            <div class="nav-section-title">Quick Links</div>
            <div class="nav-item">
                <a href="../public/" class="nav-link" target="_blank">
                    <i class="fas fa-eye"></i>
                    <span>Public View</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="../referee/" class="nav-link" target="_blank">
                    <i class="fas fa-whistle"></i>
                    <span>Referee Panel</span>
                </a>
            </div>
        </div>
    </nav>
</div>
