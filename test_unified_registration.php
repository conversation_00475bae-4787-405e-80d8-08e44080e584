<?php
/**
 * Test Script for Unified Department Registration System
 * This script tests the unified registration behavior to ensure departments
 * automatically participate in all sports when registered for an event.
 */

require_once 'config/database.php';
require_once 'includes/department_registration.php';
require_once 'includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Testing Unified Department Registration System</h2>\n";

// Test 1: Check if unified registration tables exist
echo "<h3>Test 1: Database Schema Check</h3>\n";
$tables_to_check = [
    'event_department_registrations',
    'department_sport_participations',
    'department_overall_scores'
];

foreach ($tables_to_check as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$table]);
    if ($stmt->fetch()) {
        echo "✅ Table '$table' exists<br>\n";
    } else {
        echo "❌ Table '$table' missing<br>\n";
    }
}

// Test 2: Get a test event and department
echo "<h3>Test 2: Get Test Data</h3>\n";
$stmt = $conn->prepare("SELECT id, name FROM events ORDER BY id DESC LIMIT 1");
$stmt->execute();
$test_event = $stmt->fetch(PDO::FETCH_ASSOC);

$stmt = $conn->prepare("SELECT id, name FROM departments ORDER BY id LIMIT 1");
$stmt->execute();
$test_department = $stmt->fetch(PDO::FETCH_ASSOC);

if ($test_event && $test_department) {
    echo "✅ Test Event: {$test_event['name']} (ID: {$test_event['id']})<br>\n";
    echo "✅ Test Department: {$test_department['name']} (ID: {$test_department['id']})<br>\n";
    
    $event_id = $test_event['id'];
    $department_id = $test_department['id'];
    
    // Test 3: Check existing sports in the event
    echo "<h3>Test 3: Check Existing Sports in Event</h3>\n";
    $stmt = $conn->prepare("
        SELECT es.id, s.name as sport_name 
        FROM event_sports es 
        JOIN sports s ON es.sport_id = s.id 
        WHERE es.event_id = ?
    ");
    $stmt->execute([$event_id]);
    $existing_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Existing sports in event: " . count($existing_sports) . "<br>\n";
    foreach ($existing_sports as $sport) {
        echo "- {$sport['sport_name']} (Event Sport ID: {$sport['id']})<br>\n";
    }
    
    // Test 4: Test unified registration
    echo "<h3>Test 4: Test Unified Registration</h3>\n";
    
    // Check if department is already registered
    $stmt = $conn->prepare("
        SELECT id FROM event_department_registrations 
        WHERE event_id = ? AND department_id = ?
    ");
    $stmt->execute([$event_id, $department_id]);
    $existing_registration = $stmt->fetch();
    
    if ($existing_registration) {
        echo "Department already registered for this event (Registration ID: {$existing_registration['id']})<br>\n";
        
        // Check sport participations
        $stmt = $conn->prepare("
            SELECT dsp.id, s.name as sport_name, dsp.notes
            FROM department_sport_participations dsp
            JOIN event_sports es ON dsp.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            WHERE dsp.event_department_registration_id = ?
        ");
        $stmt->execute([$existing_registration['id']]);
        $participations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Current sport participations: " . count($participations) . "<br>\n";
        foreach ($participations as $participation) {
            echo "- {$participation['sport_name']} (Notes: {$participation['notes']})<br>\n";
        }
        
        // Check if all sports are covered
        if (count($participations) === count($existing_sports)) {
            echo "✅ Department is participating in all sports in the event<br>\n";
        } else {
            echo "⚠️ Department is missing some sport participations<br>\n";
        }
    } else {
        echo "Department not yet registered for this event<br>\n";
        echo "Note: To test registration, use the admin interface or run a registration test<br>\n";
    }
    
} else {
    echo "❌ No test data available (need at least one event and one department)<br>\n";
}

// Test 5: Check function availability
echo "<h3>Test 5: Function Availability Check</h3>\n";
$functions_to_check = [
    'registerDepartmentForEvent',
    'addAllRegisteredDepartmentsToSport',
    'getEventDepartmentRegistrations',
    'getDepartmentRegistrationStats'
];

foreach ($functions_to_check as $function) {
    if (function_exists($function)) {
        echo "✅ Function '$function' is available<br>\n";
    } else {
        echo "❌ Function '$function' is missing<br>\n";
    }
}

echo "<h3>Test Complete</h3>\n";
echo "Check the results above to verify the unified registration system is working correctly.<br>\n";
?>
