<?php
/**
 * Detailed Add Sport Debug Tool
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Add Sport - Detailed Analysis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <h1>Debug Add Sport - Detailed Analysis</h1>
    
    <div class="section">
        <h3>1. Check Tournament Tables</h3>
        <?php
        try {
            $stmt = $conn->prepare("SHOW TABLES LIKE '%tournament%'");
            $stmt->execute();
            $tournament_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo '<p><strong>Tournament-related tables found:</strong></p>';
            echo '<ul>';
            foreach ($tournament_tables as $table) {
                echo '<li>' . htmlspecialchars($table) . '</li>';
            }
            echo '</ul>';
            
            // Check if tournament_structures exists
            if (in_array('tournament_structures', $tournament_tables)) {
                echo '<div class="success">✅ tournament_structures table exists</div>';
            } else {
                echo '<div class="error">❌ tournament_structures table missing</div>';
                echo '<div class="info">The tournament manager expects tournament_structures but we have tournaments table</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
    </div>
    
    <div class="section">
        <h3>2. Check Tournament Manager File Path</h3>
        <?php
        $tournament_manager_path = '../includes/tournament_manager.php';
        $full_path = realpath($tournament_manager_path);
        
        if (file_exists($tournament_manager_path)) {
            echo '<div class="success">✅ Tournament manager file exists at: ' . htmlspecialchars($full_path) . '</div>';
        } else {
            echo '<div class="error">❌ Tournament manager file not found at: ' . htmlspecialchars($tournament_manager_path) . '</div>';
        }
        ?>
    </div>
    
    <div class="section">
        <h3>3. Test Add Sport AJAX Call</h3>
        <button onclick="testAddSport()">Test Add Sport AJAX</button>
        <div id="ajax-test-result"></div>
    </div>
    
    <div class="section">
        <h3>4. Check Current Event Sports</h3>
        <?php
        try {
            $stmt = $conn->prepare("
                SELECT es.*, s.name as sport_name, s.type as sport_type 
                FROM event_sports es 
                LEFT JOIN sports s ON es.sport_id = s.id 
                WHERE es.event_id = 1
                ORDER BY es.id DESC
            ");
            $stmt->execute();
            $event_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($event_sports)) {
                echo '<p>No sports found for Event 1.</p>';
            } else {
                echo '<table>';
                echo '<tr><th>ID</th><th>Sport ID</th><th>Sport Name</th><th>Bracket Type</th><th>Max Teams</th><th>Created</th></tr>';
                foreach ($event_sports as $sport) {
                    echo '<tr>';
                    echo '<td>' . htmlspecialchars($sport['id']) . '</td>';
                    echo '<td>' . htmlspecialchars($sport['sport_id']) . '</td>';
                    echo '<td>' . htmlspecialchars($sport['sport_name'] ?? 'Unknown') . '</td>';
                    echo '<td>' . htmlspecialchars($sport['bracket_type'] ?? 'N/A') . '</td>';
                    echo '<td>' . htmlspecialchars($sport['max_teams'] ?? 'N/A') . '</td>';
                    echo '<td>' . htmlspecialchars($sport['created_at'] ?? 'N/A') . '</td>';
                    echo '</tr>';
                }
                echo '</table>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
    </div>
    
    <div class="section">
        <h3>5. Clear Event Sports for Testing</h3>
        <button onclick="clearEventSports()" class="danger" style="background: #dc3545;">Clear All Sports from Event 1</button>
        <div id="clear-result"></div>
    </div>

    <script>
        async function testAddSport() {
            const resultDiv = document.getElementById('ajax-test-result');
            resultDiv.innerHTML = '<p>Testing Add Sport AJAX...</p>';
            
            // Get CSRF token first
            try {
                const tokenResponse = await fetch('../includes/get_csrf_token.php');
                const tokenData = await tokenResponse.json();
                const csrfToken = tokenData.token;
                
                const formData = new FormData();
                formData.append('action', 'add_sport');
                formData.append('event_id', '1');
                formData.append('sport_id', '2'); // Try Volleyball
                formData.append('tournament_format_id', '1');
                formData.append('seeding_method', 'random');
                formData.append('csrf_token', csrfToken);
                
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                resultDiv.innerHTML += `<p><strong>Response Status:</strong> ${response.status}</p>`;
                resultDiv.innerHTML += `<p><strong>Response Length:</strong> ${text.length} characters</p>`;
                resultDiv.innerHTML += `<p><strong>Raw Response:</strong></p><pre>${text}</pre>`;
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML += `<div class="success">✅ Success: ${data.message}</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML += `<div class="error">❌ JSON Parse Error: ${e.message}</div>`;
                    
                    // Check for specific error patterns
                    if (text.includes('tournament_structures')) {
                        resultDiv.innerHTML += `<div class="warning">⚠️ Found reference to tournament_structures table</div>`;
                    }
                    if (text.includes('Fatal error:')) {
                        resultDiv.innerHTML += `<div class="error">❌ PHP Fatal Error detected</div>`;
                    }
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }
        
        async function clearEventSports() {
            const resultDiv = document.getElementById('clear-result');
            resultDiv.innerHTML = '<p>Clearing event sports...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=clear_event_sports&event_id=1'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `<div class="success">✅ ${data.message}</div>`;
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="error">❌ JSON Parse Error: ${e.message}</div>`;
                    resultDiv.innerHTML += `<pre>Raw Response: ${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
