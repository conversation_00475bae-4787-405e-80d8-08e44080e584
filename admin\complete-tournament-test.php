<?php
/**
 * Complete Tournament Creation Test
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Complete Tournament Creation Test</h1>";
echo "<p>Testing the complete tournament creation flow after schema fixes...</p>";

$badminton_event_sport_id = 18;

echo "<h2>🔍 Step 1: Verify Database Schema</h2>";

// Check matches table has required columns
$stmt = $conn->prepare("DESCRIBE matches");
$stmt->execute();
$matches_columns = $stmt->fetchAll();

$required_columns = ['tournament_structure_id', 'tournament_round_id', 'bracket_position', 'is_bye_match'];
$missing_columns = [];

foreach ($required_columns as $required_col) {
    $found = false;
    foreach ($matches_columns as $column) {
        if ($column['Field'] == $required_col) {
            $found = true;
            break;
        }
    }
    if (!$found) {
        $missing_columns[] = $required_col;
    }
}

if (empty($missing_columns)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>MATCHES TABLE SCHEMA OK!</strong></h3>";
    echo "<p>All required tournament columns exist in matches table.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>MISSING COLUMNS!</strong></h3>";
    echo "<p>Missing columns: " . implode(', ', $missing_columns) . "</p>";
    echo "<p><a href='fix-matches-table-schema.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Fix Schema Now</a></p>";
    echo "</div>";
    exit;
}

// Check tournament tables exist
$required_tables = ['tournament_structures', 'tournament_rounds', 'tournament_formats'];
$missing_tables = [];

foreach ($required_tables as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$table]);
    if (!$stmt->fetch()) {
        $missing_tables[] = $table;
    }
}

if (empty($missing_tables)) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 10px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ <strong>TOURNAMENT TABLES OK!</strong></h3>";
    echo "<p>All required tournament tables exist.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>MISSING TABLES!</strong></h3>";
    echo "<p>Missing tables: " . implode(', ', $missing_tables) . "</p>";
    echo "<p><a href='fix-matches-table-schema.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Fix Schema Now</a></p>";
    echo "</div>";
    exit;
}

echo "<h2>🔍 Step 2: Test Dynamic Format ID Selection</h2>";

// Test the exact query from manage-category.php
$default_format_id = 1; // Fallback default
try {
    $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = 'single_elimination' OR name LIKE '%Single Elimination%' ORDER BY id ASC LIMIT 1");
    $stmt->execute();
    $format_result = $stmt->fetch();
    if ($format_result) {
        $default_format_id = $format_result['id'];
    }
} catch (Exception $e) {
    // Use fallback default
}

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border: 2px solid #007bff; margin: 10px 0;'>";
echo "<h3>📊 Dynamic Format ID Selection Result</h3>";
echo "<p><strong>Selected Format ID:</strong> {$default_format_id}</p>";

// Get the format details
$stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
$stmt->execute([$default_format_id]);
$selected_format = $stmt->fetch();

if ($selected_format) {
    echo "<p><strong>Format Name:</strong> {$selected_format['name']}</p>";
    echo "<p><strong>Format Code:</strong> {$selected_format['code']}</p>";
    echo "<p><strong>Min Participants:</strong> {$selected_format['min_participants']}</p>";
    echo "<p style='color: #28a745; font-weight: bold;'>✅ Format found and valid!</p>";
} else {
    echo "<p style='color: #dc3545; font-weight: bold;'>❌ Format not found!</p>";
}
echo "</div>";

echo "<h2>🔍 Step 3: Test Complete Tournament Creation</h2>";

if (isset($_POST['test_complete_creation'])) {
    echo "<h3>🔄 Testing Complete Tournament Creation...</h3>";
    
    // Simulate the exact request that will now be sent
    $_POST['event_sport_id'] = $badminton_event_sport_id;
    $_POST['tournament_name'] = 'Badminton - Mixed Doubles Tournament';
    $_POST['format_id'] = $default_format_id; // Use the dynamic format ID
    $_POST['seeding_method'] = 'random';
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 8px; border: 2px solid #007bff; margin: 10px 0;'>";
    echo "<h4>📤 Request Parameters:</h4>";
    echo "<ul>";
    echo "<li><strong>event_sport_id:</strong> {$_POST['event_sport_id']}</li>";
    echo "<li><strong>tournament_name:</strong> {$_POST['tournament_name']}</li>";
    echo "<li><strong>format_id:</strong> {$_POST['format_id']} (Dynamic - was previously hardcoded to 1)</li>";
    echo "<li><strong>seeding_method:</strong> {$_POST['seeding_method']}</li>";
    echo "</ul>";
    echo "</div>";
    
    // Test the create-tournament.php logic
    ob_start();
    $success = false;
    $error_message = '';
    
    try {
        // Capture any output from the included file
        include 'ajax/create-tournament.php';
        $output = ob_get_clean();
        $success = true;
        
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0; font-size: 24px;'>🎉 <strong>TOURNAMENT CREATION SUCCESSFUL!</strong></h3>";
        echo "<p style='font-size: 18px;'>The complete solution works perfectly!</p>";
        echo "<h4>Response:</h4>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px;'>{$output}</pre>";
        echo "</div>";
        
    } catch (Exception $e) {
        ob_end_clean();
        $error_message = $e->getMessage();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 10px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>TOURNAMENT CREATION FAILED!</strong></h3>";
        echo "<p><strong>Error:</strong> " . $error_message . "</p>";
        echo "</div>";
        
        // Provide specific troubleshooting based on error
        if (strpos($error_message, 'tournament_structure_id') !== false) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 10px 0;'>";
            echo "<h4 style='color: #856404;'>🔧 <strong>SCHEMA ISSUE DETECTED!</strong></h4>";
            echo "<p>The error indicates missing tournament_structure_id column. Please run the schema fix.</p>";
            echo "<p><a href='fix-matches-table-schema.php' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;'>🔧 Fix Schema Now</a></p>";
            echo "</div>";
        }
    }
    
    if ($success) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🏆 <strong>COMPLETE SOLUTION SUCCESS!</strong></h3>";
        echo "<p style='font-size: 18px;'>Both the format ID issue and schema issue have been resolved!</p>";
        echo "<h4>What was fixed:</h4>";
        echo "<ul>";
        echo "<li>✅ <strong>Dynamic Format ID Selection:</strong> Frontend automatically selects correct format ID</li>";
        echo "<li>✅ <strong>Database Schema:</strong> Added missing tournament_structure_id column</li>";
        echo "<li>✅ <strong>Tournament Tables:</strong> Created all required tournament tables</li>";
        echo "<li>✅ <strong>Complete Flow:</strong> Tournament creation works end-to-end</li>";
        echo "</ul>";
        echo "<div style='margin: 20px 0;'>";
        echo "<a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 4px; font-size: 20px; font-weight: bold; display: inline-block;'>🏆 CREATE REAL TOURNAMENT NOW</a>";
        echo "</div>";
        echo "</div>";
    }
}

echo "<h3>🚀 Test Complete Tournament Creation</h3>";
echo "<form method='POST'>";
echo "<button type='submit' name='test_complete_creation' value='1' style='background: #007bff; color: white; padding: 20px 40px; border: none; border-radius: 4px; font-size: 18px; font-weight: bold; cursor: pointer;'>🧪 Test Complete Tournament Creation Flow</button>";
echo "</form>";

echo "<h2>📊 Solution Summary</h2>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; border: 1px solid #dee2e6; margin: 20px 0;'>";
echo "<h3>🔧 Issues Fixed:</h3>";
echo "<ol>";
echo "<li><strong>Invalid tournament format:</strong> Fixed by implementing dynamic format ID selection</li>";
echo "<li><strong>Missing tournament_structure_id column:</strong> Fixed by adding required columns to matches table</li>";
echo "<li><strong>Missing tournament tables:</strong> Fixed by creating tournament_structures and tournament_rounds tables</li>";
echo "</ol>";

echo "<h3>🎯 Current Status:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #f5f5f5;'>";
echo "<th>Component</th><th>Status</th><th>Details</th>";
echo "</tr>";
echo "<tr>";
echo "<td><strong>Format ID Selection</strong></td>";
echo "<td style='color: #28a745;'>✅ Fixed</td>";
echo "<td>Dynamic query finds format ID {$default_format_id}</td>";
echo "</tr>";
echo "<tr>";
echo "<td><strong>Database Schema</strong></td>";
echo "<td style='color: " . (empty($missing_columns) ? '#28a745;">✅ Fixed' : '#dc3545;">❌ Needs Fix') . "</td>";
echo "<td>" . (empty($missing_columns) ? 'All columns exist' : 'Missing: ' . implode(', ', $missing_columns)) . "</td>";
echo "</tr>";
echo "<tr>";
echo "<td><strong>Tournament Tables</strong></td>";
echo "<td style='color: " . (empty($missing_tables) ? '#28a745;">✅ Fixed' : '#dc3545;">❌ Needs Fix') . "</td>";
echo "<td>" . (empty($missing_tables) ? 'All tables exist' : 'Missing: ' . implode(', ', $missing_tables)) . "</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

echo "<h3>🔧 Quick Actions</h3>";
echo "<ul>";
echo "<li><a href='manage-category.php?event_id=3&sport_id=40&category_id=2'>🏆 Go to Tournament Creation Page</a></li>";
echo "<li><a href='fix-matches-table-schema.php'>🔧 Fix Database Schema</a></li>";
echo "<li><a href='final-tournament-solution-test.php'>🧪 Test Format ID Solution</a></li>";
echo "</ul>";
?>
