<?php
/**
 * Test Modal Handler Direct Call
 */

// Start output buffering to prevent any unwanted output
ob_start();

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Clean any output that might have been generated
ob_clean();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'test_modal_handler_call':
                // Test calling modal handler exactly like events.php does
                $eventId = $_POST['event_id'] ?? 1;
                
                // Get CSRF token
                $csrfToken = generateCSRFToken();
                
                // Simulate the exact call that events.php makes
                $postData = [
                    'entity' => 'event',
                    'action' => 'delete',
                    'id' => $eventId,
                    'csrf_token' => $csrfToken,
                    'force_cascade' => 'true'
                ];
                
                // Use file_get_contents with stream context to make the request
                $context = stream_context_create([
                    'http' => [
                        'method' => 'POST',
                        'header' => [
                            'Content-Type: application/x-www-form-urlencoded',
                            'Cookie: ' . session_name() . '=' . session_id()
                        ],
                        'content' => http_build_query($postData)
                    ]
                ]);
                
                $response = file_get_contents('http://localhost/SC_IMS/admin/ajax/modal-handler.php', false, $context);
                
                // Check if response is valid JSON
                $jsonData = json_decode($response, true);
                $isValidJson = json_last_error() === JSON_ERROR_NONE;
                
                echo json_encode([
                    'success' => true,
                    'modal_handler_response' => $response,
                    'response_length' => strlen($response),
                    'is_valid_json' => $isValidJson,
                    'json_data' => $isValidJson ? $jsonData : null,
                    'json_error' => $isValidJson ? null : json_last_error_msg(),
                    'post_data_sent' => $postData
                ]);
                break;
                
            case 'test_dependency_endpoint':
                // Test the dependency checking endpoint
                $eventId = $_POST['event_id'] ?? 1;
                
                $context = stream_context_create([
                    'http' => [
                        'method' => 'POST',
                        'header' => [
                            'Content-Type: application/x-www-form-urlencoded',
                            'Cookie: ' . session_name() . '=' . session_id()
                        ],
                        'content' => http_build_query(['event_id' => $eventId])
                    ]
                ]);
                
                $response = file_get_contents('http://localhost/SC_IMS/admin/ajax/check-event-dependencies.php', false, $context);
                
                // Check if response is valid JSON
                $jsonData = json_decode($response, true);
                $isValidJson = json_last_error() === JSON_ERROR_NONE;
                
                echo json_encode([
                    'success' => true,
                    'dependency_response' => $response,
                    'response_length' => strlen($response),
                    'is_valid_json' => $isValidJson,
                    'json_data' => $isValidJson ? $jsonData : null,
                    'json_error' => $isValidJson ? null : json_last_error_msg()
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'Invalid action'
                ]);
        }
    } catch (Exception $e) {
        // Clean any output that might have been generated
        ob_clean();
        
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} else {
    // Clean output buffer for HTML response
    ob_clean();
    header('Content-Type: text/html');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test Modal Handler Direct Call</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
            .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
            button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            button:hover { background: #0056b3; }
            pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
            input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
            .response-details { margin: 10px 0; }
            .response-details h4 { margin: 5px 0; }
        </style>
    </head>
    <body>
        <h1>Test Modal Handler Direct Call</h1>
        
        <div class="section">
            <h3>1. Test Modal Handler Call</h3>
            <div id="modal-result"></div>
            <input type="number" id="event-id-input" placeholder="Event ID" value="1">
            <button onclick="testModalHandlerCall()">Test Modal Handler</button>
        </div>
        
        <div class="section">
            <h3>2. Test Dependency Endpoint</h3>
            <div id="dependency-result"></div>
            <button onclick="testDependencyEndpoint()">Test Dependency Endpoint</button>
        </div>

        <script>
            async function testModalHandlerCall() {
                const resultDiv = document.getElementById('modal-result');
                const eventId = document.getElementById('event-id-input').value;
                
                resultDiv.innerHTML = '<p>Testing modal handler call...</p>';
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'test_modal_handler_call');
                    formData.append('event_id', eventId);
                    
                    const response = await fetch('', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    console.log('Modal handler test response:', responseText);
                    
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ Modal handler test completed</div>
                            <div class="response-details">
                                <h4>Response Length: ${data.response_length}</h4>
                                <h4>Is Valid JSON: ${data.is_valid_json ? 'Yes' : 'No'}</h4>
                                ${data.json_error ? `<h4>JSON Error: ${data.json_error}</h4>` : ''}
                                <h4>Post Data Sent:</h4>
                                <pre>${JSON.stringify(data.post_data_sent, null, 2)}</pre>
                                <h4>Modal Handler Response:</h4>
                                <pre>${data.modal_handler_response}</pre>
                                ${data.json_data ? `<h4>Parsed JSON Data:</h4><pre>${JSON.stringify(data.json_data, null, 2)}</pre>` : ''}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    console.error('Modal handler test error:', error);
                }
            }
            
            async function testDependencyEndpoint() {
                const resultDiv = document.getElementById('dependency-result');
                const eventId = document.getElementById('event-id-input').value;
                
                resultDiv.innerHTML = '<p>Testing dependency endpoint...</p>';
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'test_dependency_endpoint');
                    formData.append('event_id', eventId);
                    
                    const response = await fetch('', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    console.log('Dependency endpoint test response:', responseText);
                    
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ Dependency endpoint test completed</div>
                            <div class="response-details">
                                <h4>Response Length: ${data.response_length}</h4>
                                <h4>Is Valid JSON: ${data.is_valid_json ? 'Yes' : 'No'}</h4>
                                ${data.json_error ? `<h4>JSON Error: ${data.json_error}</h4>` : ''}
                                <h4>Dependency Response:</h4>
                                <pre>${data.dependency_response}</pre>
                                ${data.json_data ? `<h4>Parsed JSON Data:</h4><pre>${JSON.stringify(data.json_data, null, 2)}</pre>` : ''}
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    console.error('Dependency endpoint test error:', error);
                }
            }
        </script>
    </body>
    </html>
    <?php
}

// Ensure clean output
ob_end_flush();
?>
