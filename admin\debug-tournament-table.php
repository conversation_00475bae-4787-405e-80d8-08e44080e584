<?php
/**
 * Debug Tournament Formats Table Structure and Data
 */

require_once '../config/database.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Formats Table Debug</h2>";

try {
    // Check if table exists and get structure
    echo "<h3>1. Table Structure</h3>";
    $stmt = $conn->prepare("DESCRIBE tournament_formats");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    if (empty($columns)) {
        echo "<p style='color: red;'>❌ tournament_formats table does not exist!</p>";
        
        // Try to create the table
        echo "<h3>2. Creating tournament_formats table...</h3>";
        $sql = "CREATE TABLE IF NOT EXISTS tournament_formats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            code VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            sport_types VARCHAR(255), -- Comma-separated list of sport types
            min_participants INT DEFAULT 2,
            max_participants INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ tournament_formats table created</p>";
        
        // Insert basic formats
        $basic_formats = [
            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'team,individual', 2, 0],
            ['Double Elimination', 'double_elimination', 'Tournament with winner and loser brackets', 'team,individual', 2, 0],
            ['Round Robin', 'round_robin', 'Every participant plays every other participant', 'team,individual', 2, 16],
            ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic', 4, 0]
        ];
        
        foreach ($basic_formats as $format) {
            try {
                $stmt = $conn->prepare("INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute($format);
                echo "<p style='color: green;'>✓ Added format: {$format[0]}</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠ Format {$format[0]} might already exist: " . $e->getMessage() . "</p>";
            }
        }
        
        // Re-check structure
        $stmt = $conn->prepare("DESCRIBE tournament_formats");
        $stmt->execute();
        $columns = $stmt->fetchAll();
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check current data
    echo "<h3>3. Current Data</h3>";
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    if (empty($formats)) {
        echo "<p style='color: red;'>❌ No tournament formats found in database!</p>";
    } else {
        echo "<p style='color: green;'>✓ Found " . count($formats) . " tournament formats</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Types</th><th>Min Participants</th><th>Max Participants</th></tr>";
        foreach ($formats as $format) {
            echo "<tr>";
            echo "<td>{$format['id']}</td>";
            echo "<td>{$format['name']}</td>";
            echo "<td>{$format['code']}</td>";
            echo "<td>" . ($format['sport_types'] ?? $format['sport_type_category'] ?? 'N/A') . "</td>";
            echo "<td>{$format['min_participants']}</td>";
            echo "<td>" . ($format['max_participants'] ?: 'Unlimited') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test the AJAX endpoint logic
    echo "<h3>4. Testing AJAX Endpoint Logic</h3>";
    
    $test_sport_types = ['traditional', 'team', 'individual', 'academic', 'judged', 'performance'];
    
    foreach ($test_sport_types as $sport_type) {
        echo "<h4>Testing sport type: {$sport_type}</h4>";
        
        // Map sport types to database categories (same logic as AJAX endpoint)
        $type_mapping = [
            'traditional' => ['team', 'individual'],
            'team' => ['team'],
            'individual' => ['individual'],
            'academic' => ['academic'],
            'judged' => ['judged'],
            'performance' => ['performance']
        ];
        
        $sport_types = $type_mapping[$sport_type] ?? ['team', 'individual'];
        $sport_types_string = implode(',', $sport_types);
        
        echo "<p>Mapped to: " . implode(', ', $sport_types) . "</p>";
        
        // Try the query from the AJAX endpoint
        try {
            $stmt = $conn->prepare("
                SELECT id, name, code, description, sport_types, min_participants, max_participants
                FROM tournament_formats 
                WHERE sport_types REGEXP CONCAT('(^|,)(', REPLACE(?, ',', '|'), ')(,|$)')
                ORDER BY name
            ");
            $stmt->execute([$sport_types_string]);
            $results = $stmt->fetchAll();
            
            if (empty($results)) {
                echo "<p style='color: red;'>❌ No formats found for {$sport_type}</p>";
            } else {
                echo "<p style='color: green;'>✓ Found " . count($results) . " formats:</p>";
                echo "<ul>";
                foreach ($results as $result) {
                    echo "<li>{$result['name']} (Code: {$result['code']})</li>";
                }
                echo "</ul>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Query error for {$sport_type}: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>
