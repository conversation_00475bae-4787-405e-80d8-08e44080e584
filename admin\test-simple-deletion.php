<?php
/**
 * Simple Event Deletion Test
 */

// Start output buffering to prevent any unwanted output
ob_start();

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Clean any output that might have been generated
ob_clean();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'test_csrf_generation':
                $token = generateCSRFToken();
                echo json_encode([
                    'success' => true,
                    'token' => $token,
                    'session_id' => session_id(),
                    'message' => 'CSRF token generated successfully'
                ]);
                break;
                
            case 'test_csrf_validation':
                $token = $_POST['csrf_token'] ?? '';
                $isValid = verifyCSRFToken($token);
                echo json_encode([
                    'success' => $isValid,
                    'token_provided' => $token,
                    'is_valid' => $isValid,
                    'message' => $isValid ? 'CSRF token is valid' : 'CSRF token is invalid'
                ]);
                break;
                
            case 'test_direct_deletion':
                // Test direct deletion without going through modal-handler
                $eventId = $_POST['event_id'] ?? 0;
                
                if (!$eventId) {
                    throw new Exception('Event ID is required');
                }
                
                // Validate CSRF token
                if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
                    throw new Exception('Invalid security token');
                }
                
                // Get database connection
                $database = new Database();
                $conn = $database->getConnection();
                
                // Check if event exists
                $stmt = $conn->prepare("SELECT name FROM events WHERE id = ?");
                $stmt->execute([$eventId]);
                $event = $stmt->fetch();
                
                if (!$event) {
                    throw new Exception('Event not found');
                }
                
                // For testing, just return success without actually deleting
                echo json_encode([
                    'success' => true,
                    'message' => 'Direct deletion test successful (no actual deletion performed)',
                    'event_name' => $event['name'],
                    'event_id' => $eventId
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'Invalid action'
                ]);
        }
    } catch (Exception $e) {
        // Clean any output that might have been generated
        ob_clean();
        
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
} else {
    // Clean output buffer for HTML response
    ob_clean();
    header('Content-Type: text/html');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Event Deletion Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
            .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
            button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
            button:hover { background: #0056b3; }
            pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
            input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h1>Simple Event Deletion Test</h1>
        
        <div class="section">
            <h3>1. Test CSRF Token Generation</h3>
            <div id="csrf-gen-result"></div>
            <button onclick="testCSRFGeneration()">Generate CSRF Token</button>
        </div>
        
        <div class="section">
            <h3>2. Test CSRF Token Validation</h3>
            <div id="csrf-val-result"></div>
            <input type="text" id="csrf-token-input" placeholder="Enter CSRF token" style="width: 300px;">
            <button onclick="testCSRFValidation()">Validate CSRF Token</button>
        </div>
        
        <div class="section">
            <h3>3. Test Direct Deletion (No Modal Handler)</h3>
            <div id="direct-result"></div>
            <input type="number" id="event-id-input" placeholder="Event ID" value="1">
            <button onclick="testDirectDeletion()">Test Direct Deletion</button>
        </div>

        <script>
            let currentCSRFToken = '';
            
            async function testCSRFGeneration() {
                const resultDiv = document.getElementById('csrf-gen-result');
                resultDiv.innerHTML = '<p>Generating CSRF token...</p>';
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'test_csrf_generation');
                    
                    const response = await fetch('', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    console.log('CSRF generation response:', responseText);
                    
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        currentCSRFToken = data.token;
                        document.getElementById('csrf-token-input').value = data.token;
                        resultDiv.innerHTML = `
                            <div class="success">✅ ${data.message}</div>
                            <div><strong>Token:</strong> ${data.token}</div>
                            <div><strong>Session ID:</strong> ${data.session_id}</div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    console.error('CSRF generation error:', error);
                }
            }
            
            async function testCSRFValidation() {
                const resultDiv = document.getElementById('csrf-val-result');
                const token = document.getElementById('csrf-token-input').value;
                
                if (!token) {
                    resultDiv.innerHTML = '<div class="error">Please enter a CSRF token</div>';
                    return;
                }
                
                resultDiv.innerHTML = '<p>Validating CSRF token...</p>';
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'test_csrf_validation');
                    formData.append('csrf_token', token);
                    
                    const response = await fetch('', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    console.log('CSRF validation response:', responseText);
                    
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ ${data.message}</div>
                            <div><strong>Token Valid:</strong> ${data.is_valid}</div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="error">❌ ${data.message}</div>
                            <div><strong>Token Valid:</strong> ${data.is_valid}</div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    console.error('CSRF validation error:', error);
                }
            }
            
            async function testDirectDeletion() {
                const resultDiv = document.getElementById('direct-result');
                const eventId = document.getElementById('event-id-input').value;
                
                if (!eventId) {
                    resultDiv.innerHTML = '<div class="error">Please enter an event ID</div>';
                    return;
                }
                
                if (!currentCSRFToken) {
                    resultDiv.innerHTML = '<div class="error">Please generate a CSRF token first</div>';
                    return;
                }
                
                resultDiv.innerHTML = '<p>Testing direct deletion...</p>';
                
                try {
                    const formData = new FormData();
                    formData.append('action', 'test_direct_deletion');
                    formData.append('event_id', eventId);
                    formData.append('csrf_token', currentCSRFToken);
                    
                    const response = await fetch('', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const responseText = await response.text();
                    console.log('Direct deletion response:', responseText);
                    
                    const data = JSON.parse(responseText);
                    
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">✅ ${data.message}</div>
                            <div><strong>Event:</strong> ${data.event_name} (ID: ${data.event_id})</div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                    console.error('Direct deletion error:', error);
                }
            }
        </script>
    </body>
    </html>
    <?php
}

// Ensure clean output
ob_end_flush();
?>
