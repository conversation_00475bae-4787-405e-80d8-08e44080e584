<?php
/**
 * Comprehensive Fix for Tournament Format Loading Issue
 */

require_once '../config/database.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h2>Fixing Tournament Format Loading Issue</h2>";

try {
    // Step 1: Ensure tournament_formats table exists with correct structure
    echo "<h3>Step 1: Checking/Creating tournament_formats table</h3>";
    
    $stmt = $conn->prepare("SHOW TABLES LIKE 'tournament_formats'");
    $stmt->execute();
    $table_exists = $stmt->fetch();
    
    if (!$table_exists) {
        echo "<p style='color: orange;'>⚠ Creating tournament_formats table...</p>";
        $sql = "CREATE TABLE tournament_formats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            sport_types VARCHAR(255) DEFAULT 'team,individual',
            min_participants INT DEFAULT 2,
            max_participants INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_sport_types (sport_types),
            INDEX idx_code (code)
        )";
        $conn->exec($sql);
        echo "<p style='color: green;'>✓ tournament_formats table created</p>";
    } else {
        echo "<p style='color: green;'>✓ tournament_formats table exists</p>";
    }
    
    // Step 2: Clear existing data and insert comprehensive tournament formats
    echo "<h3>Step 2: Inserting/Updating tournament formats</h3>";
    
    // Clear existing formats to avoid conflicts
    $conn->exec("DELETE FROM tournament_formats");
    echo "<p style='color: orange;'>⚠ Cleared existing tournament formats</p>";
    
    // Insert comprehensive tournament formats
    $tournament_formats = [
        // Team Sports Formats
        ['Single Elimination', 'single_elimination', 'Traditional knockout tournament where teams are eliminated after one loss.', 'team,individual', 2, null],
        ['Double Elimination', 'double_elimination', 'Two-bracket system with winner\'s and loser\'s brackets.', 'team,individual', 3, null],
        ['Round Robin', 'round_robin', 'Every team plays every other team once.', 'team,individual', 3, 16],
        ['Multi-Stage', 'multi_stage', 'Group stage round robin followed by single elimination playoffs.', 'team,individual', 8, null],
        
        // Individual Sports Formats
        ['Elimination Rounds', 'elimination_rounds', 'Progressive elimination rounds for individual competitors.', 'individual', 4, null],
        ['Time Trials', 'time_trials', 'Individual time-based competition format.', 'individual', 2, null],
        
        // Academic Formats
        ['Swiss System', 'swiss_system', 'Pairing system commonly used for academic competitions.', 'academic', 4, null],
        ['Knockout Rounds', 'knockout_rounds', 'Academic knockout competition with elimination rounds.', 'academic', 4, null],
        
        // Judged/Performance Formats
        ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria.', 'judged,performance', 3, null],
        ['Talent Showcase', 'talent_showcase', 'Performance showcase with judged scoring.', 'judged,performance', 3, null],
        ['Performance Competition', 'performance_competition', 'Structured performance competition with multiple rounds.', 'performance', 3, null],
        ['Artistic Judging', 'artistic_judging', 'Artistic performance with panel judging.', 'performance', 3, null]
    ];
    
    foreach ($tournament_formats as $format) {
        $stmt = $conn->prepare("
            INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute($format);
        echo "<p style='color: green;'>✓ Added: {$format[0]} (Types: {$format[3]})</p>";
    }
    
    // Step 3: Verify sport types and categories
    echo "<h3>Step 3: Checking sport types and categories</h3>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM sport_types");
    $stmt->execute();
    $sport_types_count = $stmt->fetch()['count'];
    
    if ($sport_types_count == 0) {
        echo "<p style='color: orange;'>⚠ No sport types found, creating default ones...</p>";
        
        $default_sport_types = [
            ['Traditional Team Sports', 'team', 'Traditional team-based sports like basketball, volleyball, football', '#007bff', 'fas fa-users'],
            ['Traditional Individual Sports', 'individual', 'Individual competitive sports and athletics', '#28a745', 'fas fa-user'],
            ['Academic Games', 'academic', 'Academic competitions like chess, quiz bowl, debate', '#6f42c1', 'fas fa-brain'],
            ['Judged Competitions', 'judged', 'Competitions requiring judged scoring like singing, debate', '#fd7e14', 'fas fa-star'],
            ['Performance Arts', 'performance', 'Performance-based competitions like dance, drama, music', '#e83e8c', 'fas fa-theater-masks']
        ];
        
        foreach ($default_sport_types as $type) {
            $stmt = $conn->prepare("
                INSERT INTO sport_types (name, category, description, color_code, icon_class)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute($type);
            echo "<p style='color: green;'>✓ Added sport type: {$type[0]} (Category: {$type[1]})</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Found {$sport_types_count} sport types</p>";
    }
    
    // Step 4: Update sports to have proper sport_type_id if missing
    echo "<h3>Step 4: Updating sports with sport type associations</h3>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM sports WHERE sport_type_id IS NULL");
    $stmt->execute();
    $sports_without_types = $stmt->fetch()['count'];
    
    if ($sports_without_types > 0) {
        echo "<p style='color: orange;'>⚠ Found {$sports_without_types} sports without sport type associations</p>";
        
        // Map old type values to new sport_type_id
        $type_mapping = [
            'traditional' => 1, // Traditional Team Sports
            'team' => 1,
            'individual' => 2, // Traditional Individual Sports
            'academic' => 3, // Academic Games
            'judged' => 4, // Judged Competitions
            'performance' => 5 // Performance Arts
        ];
        
        $stmt = $conn->prepare("SELECT id, name, type FROM sports WHERE sport_type_id IS NULL");
        $stmt->execute();
        $sports_to_update = $stmt->fetchAll();
        
        foreach ($sports_to_update as $sport) {
            $old_type = $sport['type'] ?? 'traditional';
            $new_sport_type_id = $type_mapping[$old_type] ?? 1;
            
            $stmt = $conn->prepare("UPDATE sports SET sport_type_id = ? WHERE id = ?");
            $stmt->execute([$new_sport_type_id, $sport['id']]);
            
            echo "<p style='color: green;'>✓ Updated sport '{$sport['name']}': {$old_type} → sport_type_id {$new_sport_type_id}</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ All sports have sport type associations</p>";
    }
    
    // Step 5: Test the AJAX endpoint
    echo "<h3>Step 5: Testing AJAX endpoint</h3>";
    
    $test_sport_types = ['traditional', 'team', 'individual', 'academic', 'judged', 'performance'];
    
    foreach ($test_sport_types as $sport_type) {
        echo "<h4>Testing: {$sport_type}</h4>";
        
        // Simulate the AJAX endpoint logic
        $type_mapping = [
            'traditional' => ['team', 'individual'],
            'team' => ['team'],
            'individual' => ['individual'],
            'academic' => ['academic'],
            'judged' => ['judged'],
            'performance' => ['performance']
        ];
        
        $sport_types = $type_mapping[$sport_type] ?? ['team', 'individual'];
        
        // Build WHERE clause
        $where_conditions = [];
        $params = [];
        
        foreach ($sport_types as $type) {
            $where_conditions[] = "sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?";
            $params[] = $type . ',%';
            $params[] = '%,' . $type . ',%';
            $params[] = '%,' . $type;
            $params[] = $type;
        }
        
        $where_clause = '(' . implode(') OR (', $where_conditions) . ')';
        
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_types
            FROM tournament_formats 
            WHERE {$where_clause}
            ORDER BY name
        ");
        
        $stmt->execute($params);
        $formats = $stmt->fetchAll();
        
        if (empty($formats)) {
            echo "<p style='color: red;'>❌ No formats found</p>";
        } else {
            echo "<p style='color: green;'>✓ Found " . count($formats) . " formats:</p>";
            echo "<ul>";
            foreach ($formats as $format) {
                echo "<li>{$format['name']} (ID: {$format['id']}, Types: {$format['sport_types']})</li>";
            }
            echo "</ul>";
        }
    }
    
    echo "<h3>✅ Fix Complete!</h3>";
    echo "<p style='color: green; font-weight: bold;'>The tournament format loading system has been fixed. You can now test the Add Sport modal.</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
