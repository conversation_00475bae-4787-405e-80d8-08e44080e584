<?php
/**
 * Comprehensive Database Cleanup Tool for SC_IMS
 * Fixes orphaned records, duplicate entries, and data inconsistencies
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Cleanup Tool - SC_IMS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: #212529; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <h1>🧹 Database Cleanup Tool</h1>
    
    <div class="section warning">
        <h3>⚠️ Important Notice</h3>
        <p><strong>This tool performs database modifications that cannot be undone.</strong></p>
        <p>It is recommended to backup your database before running cleanup operations.</p>
    </div>
    
    <div class="section info">
        <h3>🔍 Database Health Check</h3>
        <p>Check for orphaned records, duplicates, and inconsistencies.</p>
        <button class="btn-primary" onclick="runHealthCheck()">Run Health Check</button>
        <div id="health-check-results"></div>
    </div>
    
    <div class="section">
        <h3>🧽 Cleanup Operations</h3>
        
        <div style="margin: 10px 0;">
            <h4>Event Sports Cleanup</h4>
            <button class="btn-warning" onclick="cleanupEventSports()">Clean Orphaned Event Sports</button>
            <button class="btn-warning" onclick="removeDuplicateEventSports()">Remove Duplicate Event Sports</button>
        </div>
        
        <div style="margin: 10px 0;">
            <h4>Registration Cleanup</h4>
            <button class="btn-warning" onclick="cleanupOrphanedRegistrations()">Clean Orphaned Registrations</button>
        </div>
        
        <div style="margin: 10px 0;">
            <h4>Tournament Cleanup</h4>
            <button class="btn-warning" onclick="cleanupOrphanedTournaments()">Clean Orphaned Tournaments</button>
        </div>
        
        <div style="margin: 10px 0;">
            <h4>Complete Cleanup</h4>
            <button class="btn-danger" onclick="runCompleteCleanup()">Run Complete Cleanup</button>
        </div>
        
        <div id="cleanup-results"></div>
    </div>
    
    <div class="section">
        <h3>📊 Database Statistics</h3>
        <button class="btn-primary" onclick="showDatabaseStats()">Show Statistics</button>
        <div id="database-stats"></div>
    </div>
    
    <div class="section">
        <h3>🔧 Specific Event Management</h3>
        <p>Manage sports for specific events:</p>
        <label for="event-select">Select Event:</label>
        <select id="event-select">
            <?php
            $stmt = $conn->prepare("SELECT id, name FROM events ORDER BY name");
            $stmt->execute();
            $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($events as $event) {
                echo '<option value="' . $event['id'] . '">' . htmlspecialchars($event['name']) . '</option>';
            }
            ?>
        </select>
        <button class="btn-warning" onclick="clearEventSports()">Clear All Sports from Event</button>
        <button class="btn-primary" onclick="showEventSports()">Show Event Sports</button>
        <div id="event-management-results"></div>
    </div>

    <script>
        async function runHealthCheck() {
            const resultsDiv = document.getElementById('health-check-results');
            resultsDiv.innerHTML = '<p>🔍 Running health check...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=investigate'
                });
                
                const data = await response.json();
                if (data.success) {
                    let html = '<div class="success"><h4>✅ Health Check Complete</h4>';
                    
                    // Event Sports Analysis
                    html += `<h5>Event Sports (Event 1): ${data.results.event_1_sports.length} sports</h5>`;
                    if (data.results.event_1_sports.length > 0) {
                        html += '<table><tr><th>Sport</th><th>Type</th><th>Status</th></tr>';
                        data.results.event_1_sports.forEach(sport => {
                            html += `<tr><td>${sport.sport_name || 'Unknown'}</td><td>${sport.sport_type || 'Unknown'}</td><td>${sport.status}</td></tr>`;
                        });
                        html += '</table>';
                    }
                    
                    // Orphaned Records
                    html += `<h5>Orphaned Records: ${data.results.orphaned_records.length}</h5>`;
                    if (data.results.orphaned_records.length > 0) {
                        html += '<p style="color: red;">⚠️ Found orphaned records that need cleanup!</p>';
                    }
                    
                    // Duplicates
                    html += `<h5>Duplicate Event Sports: ${data.results.duplicate_event_sports.length}</h5>`;
                    if (data.results.duplicate_event_sports.length > 0) {
                        html += '<p style="color: red;">⚠️ Found duplicate records that need cleanup!</p>';
                    }
                    
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Health check failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function runCompleteCleanup() {
            if (!confirm('Are you sure you want to run complete database cleanup? This will remove all orphaned and duplicate records.')) {
                return;
            }
            
            const resultsDiv = document.getElementById('cleanup-results');
            resultsDiv.innerHTML = '<p>🧹 Running complete cleanup...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=cleanup'
                });
                
                const data = await response.json();
                if (data.success) {
                    let html = '<div class="success"><h4>✅ Cleanup Complete</h4>';
                    html += `<p>Orphaned records deleted: ${data.results.orphaned_deleted}</p>`;
                    html += `<p>Duplicate records deleted: ${data.results.duplicates_deleted}</p>`;
                    html += `<p>Orphaned registrations deleted: ${data.results.orphaned_registrations_deleted}</p>`;
                    html += `<p>Orphaned tournaments deleted: ${data.results.orphaned_tournaments_deleted}</p>`;
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Cleanup failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function clearEventSports() {
            const eventId = document.getElementById('event-select').value;
            if (!confirm(`Are you sure you want to clear all sports from Event ${eventId}?`)) {
                return;
            }
            
            const resultsDiv = document.getElementById('event-management-results');
            resultsDiv.innerHTML = '<p>🧹 Clearing event sports...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=clear_event_sports&event_id=${eventId}`
                });
                
                const data = await response.json();
                if (data.success) {
                    resultsDiv.innerHTML = `<div class="success">✅ ${data.message}</div>`;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function showEventSports() {
            const eventId = document.getElementById('event-select').value;
            const resultsDiv = document.getElementById('event-management-results');
            resultsDiv.innerHTML = '<p>📊 Loading event sports...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=show_event_sports&event_id=${eventId}`
                });
                
                const data = await response.json();
                if (data.success) {
                    let html = `<div class="info"><h4>Event ${eventId} Sports</h4>`;
                    if (data.results.length === 0) {
                        html += '<p>No sports found for this event.</p>';
                    } else {
                        html += '<table><tr><th>ID</th><th>Sport</th><th>Type</th><th>Status</th></tr>';
                        data.results.forEach(sport => {
                            html += `<tr><td>${sport.id}</td><td>${sport.sport_name || 'Unknown'}</td><td>${sport.sport_type || 'Unknown'}</td><td>${sport.status}</td></tr>`;
                        });
                        html += '</table>';
                    }
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        // Add more specific cleanup functions
        async function cleanupEventSports() {
            await runSpecificCleanup('cleanup_event_sports', 'Event Sports');
        }
        
        async function removeDuplicateEventSports() {
            await runSpecificCleanup('remove_duplicate_event_sports', 'Duplicate Event Sports');
        }
        
        async function cleanupOrphanedRegistrations() {
            await runSpecificCleanup('cleanup_orphaned_registrations', 'Orphaned Registrations');
        }
        
        async function cleanupOrphanedTournaments() {
            await runSpecificCleanup('cleanup_orphaned_tournaments', 'Orphaned Tournaments');
        }
        
        async function runSpecificCleanup(action, description) {
            if (!confirm(`Are you sure you want to cleanup ${description}?`)) {
                return;
            }
            
            const resultsDiv = document.getElementById('cleanup-results');
            resultsDiv.innerHTML = `<p>🧹 Cleaning up ${description}...</p>`;
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `action=${action}`
                });
                
                const data = await response.json();
                if (data.success) {
                    resultsDiv.innerHTML = `<div class="success">✅ ${description} cleanup complete: ${data.message}</div>`;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ ${description} cleanup failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function showDatabaseStats() {
            const resultsDiv = document.getElementById('database-stats');
            resultsDiv.innerHTML = '<p>📊 Loading database statistics...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=database_stats'
                });
                
                const data = await response.json();
                if (data.success) {
                    let html = '<div class="info"><h4>📊 Database Statistics</h4>';
                    html += `<p>Total Events: ${data.results.total_events}</p>`;
                    html += `<p>Total Sports: ${data.results.total_sports}</p>`;
                    html += `<p>Total Event Sports: ${data.results.total_event_sports}</p>`;
                    html += `<p>Total Registrations: ${data.results.total_registrations}</p>`;
                    html += `<p>Total Tournaments: ${data.results.total_tournaments}</p>`;
                    html += '</div>';
                    resultsDiv.innerHTML = html;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
