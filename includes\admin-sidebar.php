<?php
/**
 * Admin Sidebar Navigation for SC_IMS
 */
?>
<div class="admin-sidebar">
    <div class="sidebar-header">
        <h3>SC_IMS Admin</h3>
        <p>Sports Competition Management</p>
    </div>
    
    <nav class="sidebar-nav">
        <ul>
            <li>
                <a href="index.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : ''; ?>">
                    <i class="icon">🏠</i>
                    <span>Dashboard</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Event Management</span>
            </li>
            <li>
                <a href="events.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'events.php' ? 'active' : ''; ?>">
                    <i class="icon">📅</i>
                    <span>Events</span>
                </a>
            </li>
            <li>
                <a href="sports.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'sports.php' ? 'active' : ''; ?>">
                    <i class="icon">⚽</i>
                    <span>Sports</span>
                </a>
            </li>
            <li>
                <a href="departments.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'departments.php' ? 'active' : ''; ?>">
                    <i class="icon">🏢</i>
                    <span>Departments</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">Competition</span>
            </li>
            <li>
                <a href="matches.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'matches.php' ? 'active' : ''; ?>">
                    <i class="icon">🏆</i>
                    <span>Matches</span>
                </a>
            </li>
            <li>
                <a href="reports.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'reports.php' ? 'active' : ''; ?>">
                    <i class="icon">📊</i>
                    <span>Reports</span>
                </a>
            </li>
            
            <li class="nav-section">
                <span class="section-title">System</span>
            </li>
            <li>
                <a href="system-optimization.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'system-optimization.php' ? 'active' : ''; ?>">
                    <i class="icon">⚙️</i>
                    <span>System</span>
                </a>
            </li>
        </ul>
    </nav>
    
    <div class="sidebar-footer">
        <div class="user-info">
            <div class="user-avatar">👤</div>
            <div class="user-details">
                <div class="user-name"><?php echo htmlspecialchars($current_admin['full_name'] ?? $current_admin['username'] ?? 'Admin'); ?></div>
                <div class="user-role">Administrator</div>
            </div>
        </div>
        <div class="sidebar-actions">
            <a href="../public/" class="btn-link" title="View Public Dashboard">🌐 Public</a>
            <a href="../referee/" class="btn-link" title="Referee Panel">⚽ Referee</a>
            <a href="logout.php" class="btn-link logout" title="Logout">🚪 Logout</a>
        </div>
    </div>
</div>

<style>
.admin-sidebar {
    width: 250px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    text-align: center;
}

.sidebar-header h3 {
    margin: 0 0 5px 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.sidebar-header p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.8;
}

.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sidebar-nav li {
    margin: 0;
}

.nav-section {
    margin-top: 20px;
}

.section-title {
    display: block;
    padding: 10px 20px 5px;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.6;
    font-weight: 600;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    border-left-color: #3498db;
}

.sidebar-nav a.active {
    background: rgba(52, 152, 219, 0.2);
    color: white;
    border-left-color: #3498db;
}

.sidebar-nav .icon {
    margin-right: 12px;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.2);
    padding: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 1.2rem;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.user-role {
    font-size: 0.7rem;
    opacity: 0.7;
}

.sidebar-actions {
    display: flex;
    gap: 10px;
    justify-content: space-between;
}

.btn-link {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    font-size: 0.8rem;
    padding: 5px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.btn-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.btn-link.logout:hover {
    background: rgba(231, 76, 60, 0.3);
}

/* Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        width: 200px;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
}
</style>
