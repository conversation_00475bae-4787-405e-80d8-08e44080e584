<?php
/**
 * Navigation Test Page for SC_IMS Admin Panel
 * Tests all admin pages for PHP errors and navigation functionality
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$test_results = [];

// Test all admin pages
$admin_pages = [
    'index.php' => 'Dashboard',
    'events.php' => 'Events Management',
    'sports.php' => 'Sports Management',
    'departments.php' => 'Departments Management',
    'matches.php' => 'Matches Management',
    'system-optimization.php' => 'System Optimization',
    'reports.php' => 'Reports & Analytics'
];

foreach ($admin_pages as $page => $title) {
    $test_results[$page] = [
        'title' => $title,
        'status' => 'unknown',
        'errors' => []
    ];
    
    // Test if page exists
    if (file_exists($page)) {
        $test_results[$page]['status'] = 'exists';
        
        // Test for basic PHP syntax by including the file in a buffer
        ob_start();
        $error_occurred = false;
        
        try {
            // Capture any PHP errors
            set_error_handler(function($severity, $message, $file, $line) use (&$test_results, $page) {
                $test_results[$page]['errors'][] = "Line $line: $message";
                return true;
            });
            
            // Test basic page load (just check if it can be parsed)
            $content = file_get_contents($page);
            if (strpos($content, '<?php') !== false) {
                $test_results[$page]['status'] = 'php_valid';
            }
            
            restore_error_handler();
        } catch (Exception $e) {
            $test_results[$page]['errors'][] = $e->getMessage();
            $test_results[$page]['status'] = 'error';
        }
        
        ob_end_clean();
    } else {
        $test_results[$page]['status'] = 'missing';
    }
}

// Test sidebar navigation
$sidebar_test = [
    'file_exists' => file_exists('includes/sidebar.php'),
    'styles_exist' => file_exists('includes/admin-styles.php'),
    'scripts_exist' => file_exists('includes/admin-scripts.php')
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
    
    <style>
        .test-results {
            margin: 20px 0;
        }
        .test-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .test-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .test-warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .test-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .error-list {
            margin-top: 10px;
            padding-left: 20px;
        }
        .nav-test {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-bug"></i>
                        <span>Navigation Test</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>
        
        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Navigation & Error Test</h1>
                <p class="page-description">Testing admin panel navigation and checking for PHP errors</p>
            </div>

            <!-- Sidebar Component Test -->
            <div class="nav-test">
                <h3>Sidebar Components Test</h3>
                <div class="test-item <?php echo $sidebar_test['file_exists'] ? 'test-success' : 'test-error'; ?>">
                    <strong>Sidebar File:</strong> <?php echo $sidebar_test['file_exists'] ? '✓ Found' : '✗ Missing'; ?>
                </div>
                <div class="test-item <?php echo $sidebar_test['styles_exist'] ? 'test-success' : 'test-error'; ?>">
                    <strong>Admin Styles:</strong> <?php echo $sidebar_test['styles_exist'] ? '✓ Found' : '✗ Missing'; ?>
                </div>
                <div class="test-item <?php echo $sidebar_test['scripts_exist'] ? 'test-success' : 'test-error'; ?>">
                    <strong>Admin Scripts:</strong> <?php echo $sidebar_test['scripts_exist'] ? '✓ Found' : '✗ Missing'; ?>
                </div>
            </div>

            <!-- Page Tests -->
            <div class="test-results">
                <h3>Admin Pages Test</h3>
                <?php foreach ($test_results as $page => $result): ?>
                    <div class="test-item <?php 
                        if ($result['status'] === 'php_valid') echo 'test-success';
                        elseif ($result['status'] === 'exists') echo 'test-warning';
                        else echo 'test-error';
                    ?>">
                        <strong><?php echo htmlspecialchars($result['title']); ?></strong> (<?php echo $page; ?>)
                        <br>
                        Status: <?php echo ucfirst(str_replace('_', ' ', $result['status'])); ?>
                        
                        <?php if (!empty($result['errors'])): ?>
                            <div class="error-list">
                                <strong>Errors:</strong>
                                <ul>
                                    <?php foreach ($result['errors'] as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <div style="margin-top: 10px;">
                            <a href="<?php echo $page; ?>" class="btn btn-sm btn-primary" target="_blank">Test Page</a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Navigation Links Test -->
            <div class="nav-test">
                <h3>Navigation Links Test</h3>
                <p>Click the links below to test navigation:</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 15px;">
                    <?php foreach ($admin_pages as $page => $title): ?>
                        <a href="<?php echo $page; ?>" class="btn btn-outline-primary"><?php echo $title; ?></a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
