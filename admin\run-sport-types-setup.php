<?php
/**
 * Direct execution of enhanced sport types setup
 */

require_once __DIR__ . '/../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "Starting enhanced sport types setup...\n";
    
    // Read the SQL file
    $sqlFile = __DIR__ . '/../database/enhanced_sport_types_update.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception('SQL file not found: ' . $sqlFile);
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Remove comments and split into statements
    $lines = explode("\n", $sql);
    $cleanedLines = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (!empty($line) && !preg_match('/^\s*--/', $line)) {
            $cleanedLines[] = $line;
        }
    }
    
    $cleanedSql = implode("\n", $cleanedLines);
    $statements = array_filter(array_map('trim', explode(';', $cleanedSql)));
    
    $conn->beginTransaction();
    
    $executedCount = 0;
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $conn->exec($statement);
                $executedCount++;
                echo "Executed statement " . $executedCount . "\n";
            } catch (Exception $e) {
                echo "Warning on statement " . ($executedCount + 1) . ": " . $e->getMessage() . "\n";
                // Continue with other statements
            }
        }
    }
    
    $conn->commit();
    
    echo "\nSetup completed successfully!\n";
    echo "Executed {$executedCount} SQL statements.\n";
    
    // Verify the setup
    echo "\nVerifying setup...\n";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM sport_types");
    $sportTypesCount = $stmt->fetch()['count'] ?? 0;
    echo "Sport types created: {$sportTypesCount}\n";
    
    $stmt = $conn->query("SHOW COLUMNS FROM sports LIKE 'sport_type_id'");
    $hasColumn = $stmt->rowCount() > 0;
    echo "Sports table updated: " . ($hasColumn ? "Yes" : "No") . "\n";
    
    $stmt = $conn->query("SELECT COUNT(*) as count FROM sports WHERE sport_type_id IS NOT NULL");
    $linkedSports = $stmt->fetch()['count'] ?? 0;
    echo "Sports linked to types: {$linkedSports}\n";
    
    echo "\nSetup verification complete!\n";
    
} catch (Exception $e) {
    if (isset($conn) && $conn->inTransaction()) {
        $conn->rollback();
    }
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
