<!DOCTYPE html>
<html>
<head>
    <title>Test Add Sport Workflow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Add Sport Workflow</h1>
    
    <div class="test-section">
        <h3>Test 1: Check Current Event Sports</h3>
        <button onclick="checkCurrentSports()">Check Current Sports for Event 1</button>
        <div id="current-sports-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Test Tournament Format Loading</h3>
        <button onclick="testTournamentFormats()">Test Tournament Format AJAX</button>
        <div id="tournament-formats-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Test Add Sport AJAX Call</h3>
        <p>This will attempt to add a sport to Event 1</p>
        <button onclick="testAddSport()">Test Add Sport</button>
        <div id="add-sport-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 4: Test Error Handling</h3>
        <p>This will test adding the same sport again (should show proper error)</p>
        <button onclick="testAddSportError()">Test Add Sport Error</button>
        <div id="add-sport-error-result" class="result"></div>
    </div>

    <script>
        async function checkCurrentSports() {
            const resultDiv = document.getElementById('current-sports-result');
            resultDiv.innerHTML = '<p class="info">Checking current sports...</p>';
            
            try {
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_event_sports&event_id=1'
                });
                
                const text = await response.text();
                resultDiv.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                resultDiv.innerHTML += `<p class="info">Response length: ${text.length} characters</p>`;
                
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML += `<p class="success">✓ Valid JSON response</p>`;
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } catch (e) {
                    resultDiv.innerHTML += `<p class="error">❌ JSON parse error: ${e.message}</p>`;
                    resultDiv.innerHTML += `<p class="error">Raw response:</p><pre>${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">❌ Fetch error: ${error.message}</p>`;
            }
        }
        
        async function testTournamentFormats() {
            const resultDiv = document.getElementById('tournament-formats-result');
            resultDiv.innerHTML = '<p class="info">Testing tournament format loading...</p>';
            
            try {
                const response = await fetch('ajax/get-tournament-formats.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'sport_type=individual'
                });
                
                const text = await response.text();
                resultDiv.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                resultDiv.innerHTML += `<p class="info">Response length: ${text.length} characters</p>`;
                
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML += `<p class="success">✓ Valid JSON response</p>`;
                    if (data.success && data.formats) {
                        resultDiv.innerHTML += `<p class="success">✓ Found ${data.formats.length} tournament formats</p>`;
                    }
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } catch (e) {
                    resultDiv.innerHTML += `<p class="error">❌ JSON parse error: ${e.message}</p>`;
                    resultDiv.innerHTML += `<p class="error">Raw response:</p><pre>${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">❌ Fetch error: ${error.message}</p>`;
            }
        }
        
        async function testAddSport() {
            const resultDiv = document.getElementById('add-sport-result');
            resultDiv.innerHTML = '<p class="info">Testing add sport functionality...</p>';
            
            // First get CSRF token
            try {
                const tokenResponse = await fetch('ajax/get-csrf-token.php');
                const tokenData = await tokenResponse.json();
                const csrfToken = tokenData.token;
                
                resultDiv.innerHTML += `<p class="info">Got CSRF token: ${csrfToken.substring(0, 10)}...</p>`;
                
                // Now test adding a sport
                const formData = new FormData();
                formData.append('action', 'add_sport');
                formData.append('event_id', '1');
                formData.append('sport_id', '1'); // Art Exhibition
                formData.append('tournament_format_id', '11'); // Elimination Rounds
                formData.append('seeding_method', 'random');
                formData.append('csrf_token', csrfToken);
                
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                resultDiv.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                resultDiv.innerHTML += `<p class="info">Response length: ${text.length} characters</p>`;
                
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML += `<p class="success">✓ Valid JSON response</p>`;
                    if (data.success) {
                        resultDiv.innerHTML += `<p class="success">✓ Sport added successfully!</p>`;
                    } else {
                        resultDiv.innerHTML += `<p class="error">❌ Add sport failed: ${data.message}</p>`;
                    }
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } catch (e) {
                    resultDiv.innerHTML += `<p class="error">❌ JSON parse error: ${e.message}</p>`;
                    resultDiv.innerHTML += `<p class="error">Raw response:</p><pre>${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }
        
        async function testAddSportError() {
            const resultDiv = document.getElementById('add-sport-error-result');
            resultDiv.innerHTML = '<p class="info">Testing add sport error handling (duplicate sport)...</p>';
            
            // First get CSRF token
            try {
                const tokenResponse = await fetch('ajax/get-csrf-token.php');
                const tokenData = await tokenResponse.json();
                const csrfToken = tokenData.token;
                
                resultDiv.innerHTML += `<p class="info">Got CSRF token: ${csrfToken.substring(0, 10)}...</p>`;
                
                // Try to add the same sport again (should fail)
                const formData = new FormData();
                formData.append('action', 'add_sport');
                formData.append('event_id', '1');
                formData.append('sport_id', '1'); // Art Exhibition (same as above)
                formData.append('tournament_format_id', '11'); // Elimination Rounds
                formData.append('seeding_method', 'random');
                formData.append('csrf_token', csrfToken);
                
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                resultDiv.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                resultDiv.innerHTML += `<p class="info">Response length: ${text.length} characters</p>`;
                
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML += `<p class="success">✓ Valid JSON response</p>`;
                    if (!data.success && data.message.includes('already added')) {
                        resultDiv.innerHTML += `<p class="success">✓ Error handling working correctly!</p>`;
                    } else if (data.success) {
                        resultDiv.innerHTML += `<p class="error">❌ Expected error but got success</p>`;
                    } else {
                        resultDiv.innerHTML += `<p class="error">❌ Unexpected error: ${data.message}</p>`;
                    }
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } catch (e) {
                    resultDiv.innerHTML += `<p class="error">❌ JSON parse error: ${e.message}</p>`;
                    resultDiv.innerHTML += `<p class="error">Raw response:</p><pre>${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<p class="error">❌ Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
