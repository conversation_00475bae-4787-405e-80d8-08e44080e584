<?php
/**
 * Database Investigation AJAX Handler
 * Investigates and fixes sport addition/removal issues
 */

// Prevent any output before JSON response
ob_start();

// Disable error display to prevent HTML in JSON response
ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Clear any output that might have been generated
ob_clean();

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'investigate':
            $results = [];
            
            // 1. Check Event 1 current state
            $stmt = $conn->prepare("SELECT * FROM events WHERE id = 1");
            $stmt->execute();
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
            $results['event_1'] = $event;
            
            // 2. Check event_sports for Event 1
            $stmt = $conn->prepare("
                SELECT es.*, s.name as sport_name, s.type as sport_type 
                FROM event_sports es 
                LEFT JOIN sports s ON es.sport_id = s.id 
                WHERE es.event_id = 1
            ");
            $stmt->execute();
            $event_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $results['event_1_sports'] = $event_sports;
            
            // 3. Check for orphaned records
            $stmt = $conn->prepare("
                SELECT es.* FROM event_sports es 
                LEFT JOIN events e ON es.event_id = e.id 
                LEFT JOIN sports s ON es.sport_id = s.id 
                WHERE e.id IS NULL OR s.id IS NULL
            ");
            $stmt->execute();
            $orphaned_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $results['orphaned_records'] = $orphaned_records;
            
            // 4. Check all sports available
            $stmt = $conn->prepare("SELECT id, name, type FROM sports ORDER BY name");
            $stmt->execute();
            $all_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $results['all_sports'] = $all_sports;
            
            // 5. Check tournament formats
            $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
            $stmt->execute();
            $tournament_formats = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $results['tournament_formats'] = $tournament_formats;
            
            // 6. Check for duplicate constraints
            $stmt = $conn->prepare("
                SELECT event_id, sport_id, COUNT(*) as count 
                FROM event_sports 
                GROUP BY event_id, sport_id 
                HAVING COUNT(*) > 1
            ");
            $stmt->execute();
            $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $results['duplicate_event_sports'] = $duplicates;
            
            echo json_encode([
                'success' => true,
                'results' => $results
            ]);
            break;
            
        case 'cleanup':
            $results = [];
            $conn->beginTransaction();
            
            try {
                // 1. Remove orphaned event_sports records
                $stmt = $conn->prepare("
                    DELETE es FROM event_sports es 
                    LEFT JOIN events e ON es.event_id = e.id 
                    LEFT JOIN sports s ON es.sport_id = s.id 
                    WHERE e.id IS NULL OR s.id IS NULL
                ");
                $orphaned_deleted = $stmt->execute();
                $results['orphaned_deleted'] = $stmt->rowCount();
                
                // 2. Remove duplicate event_sports (keep the first one)
                $stmt = $conn->prepare("
                    DELETE es1 FROM event_sports es1
                    INNER JOIN event_sports es2 
                    WHERE es1.id > es2.id 
                    AND es1.event_id = es2.event_id 
                    AND es1.sport_id = es2.sport_id
                ");
                $duplicates_deleted = $stmt->execute();
                $results['duplicates_deleted'] = $stmt->rowCount();
                
                // 3. Clean up related tables (registrations, matches, etc.)
                $stmt = $conn->prepare("
                    DELETE r FROM registrations r 
                    LEFT JOIN event_sports es ON r.event_sport_id = es.id 
                    WHERE es.id IS NULL
                ");
                $orphaned_registrations = $stmt->execute();
                $results['orphaned_registrations_deleted'] = $stmt->rowCount();
                
                // 4. Clean up tournaments
                $stmt = $conn->prepare("
                    DELETE t FROM tournaments t 
                    LEFT JOIN event_sports es ON t.event_sport_id = es.id 
                    WHERE es.id IS NULL
                ");
                $orphaned_tournaments = $stmt->execute();
                $results['orphaned_tournaments_deleted'] = $stmt->rowCount();
                
                $conn->commit();
                $results['status'] = 'success';
                
                echo json_encode([
                    'success' => true,
                    'results' => $results
                ]);
                
            } catch (Exception $e) {
                $conn->rollback();
                throw $e;
            }
            break;
            
        case 'test_ajax':
            $results = [];
            
            // Test 1: Check CSRF token generation
            try {
                $csrf_token = generateCSRFToken();
                $results['csrf_token_test'] = [
                    'success' => !empty($csrf_token),
                    'token_length' => strlen($csrf_token)
                ];
            } catch (Exception $e) {
                $results['csrf_token_test'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
            
            // Test 2: Check tournament formats endpoint
            try {
                $test_response = file_get_contents('http://localhost/SC_IMS/admin/ajax/get-tournament-formats.php', false, stream_context_create([
                    'http' => [
                        'method' => 'POST',
                        'header' => 'Content-Type: application/x-www-form-urlencoded',
                        'content' => 'sport_type=traditional'
                    ]
                ]));
                
                $test_data = json_decode($test_response, true);
                $results['tournament_formats_test'] = [
                    'success' => $test_data !== null,
                    'response_length' => strlen($test_response),
                    'is_valid_json' => json_last_error() === JSON_ERROR_NONE
                ];
            } catch (Exception $e) {
                $results['tournament_formats_test'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
            
            // Test 3: Check event management endpoint basic response
            $results['event_management_endpoint'] = [
                'file_exists' => file_exists('../ajax/event-management.php'),
                'readable' => is_readable('../ajax/event-management.php')
            ];
            
            echo json_encode([
                'success' => true,
                'results' => $results
            ]);
            break;
            
        case 'test_workflow':
            $results = [];

            // Test workflow with a safe sport that we can add/remove
            $test_event_id = 1;
            $test_sport_id = 2; // Use a different sport for testing

            // Step 1: Check current state
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
            $stmt->execute([$test_event_id, $test_sport_id]);
            $existing = $stmt->fetch();
            $results['initial_state'] = [
                'sport_exists' => !empty($existing),
                'event_sport_id' => $existing ? $existing['id'] : null
            ];

            // Step 2: If sport exists, remove it first
            if ($existing) {
                $stmt = $conn->prepare("DELETE FROM event_sports WHERE id = ?");
                $removed = $stmt->execute([$existing['id']]);
                $results['removal_test'] = [
                    'success' => $removed,
                    'rows_affected' => $stmt->rowCount()
                ];
            }

            // Step 3: Try to add the sport
            try {
                $stmt = $conn->prepare("
                    INSERT INTO event_sports (event_id, sport_id, max_teams, status)
                    VALUES (?, ?, 8, 'registration')
                ");
                $added = $stmt->execute([$test_event_id, $test_sport_id]);
                $new_id = $conn->lastInsertId();

                $results['addition_test'] = [
                    'success' => $added,
                    'new_id' => $new_id
                ];

                // Step 4: Verify it was added
                $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
                $stmt->execute([$test_event_id, $test_sport_id]);
                $verified = $stmt->fetch();

                $results['verification_test'] = [
                    'success' => !empty($verified),
                    'found_id' => $verified ? $verified['id'] : null
                ];

                // Step 5: Clean up - remove the test sport
                if ($verified) {
                    $stmt = $conn->prepare("DELETE FROM event_sports WHERE id = ?");
                    $cleanup = $stmt->execute([$verified['id']]);
                    $results['cleanup_test'] = [
                        'success' => $cleanup,
                        'rows_affected' => $stmt->rowCount()
                    ];
                }

            } catch (Exception $e) {
                $results['addition_test'] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }

            echo json_encode([
                'success' => true,
                'results' => $results
            ]);
            break;

        case 'clear_event_sports':
            $event_id = $_POST['event_id'] ?? 1;

            $conn->beginTransaction();
            try {
                // Remove all sports from the specified event
                $stmt = $conn->prepare("DELETE FROM event_sports WHERE event_id = ?");
                $deleted = $stmt->execute([$event_id]);
                $rows_affected = $stmt->rowCount();

                $conn->commit();

                echo json_encode([
                    'success' => true,
                    'message' => "Removed {$rows_affected} sports from event {$event_id}",
                    'rows_affected' => $rows_affected
                ]);

            } catch (Exception $e) {
                $conn->rollback();
                throw $e;
            }
            break;

        case 'show_event_sports':
            $event_id = $_POST['event_id'] ?? 1;

            $stmt = $conn->prepare("
                SELECT es.*, s.name as sport_name, s.type as sport_type
                FROM event_sports es
                LEFT JOIN sports s ON es.sport_id = s.id
                WHERE es.event_id = ?
                ORDER BY es.id
            ");
            $stmt->execute([$event_id]);
            $event_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'results' => $event_sports
            ]);
            break;

        case 'database_stats':
            $stats = [];

            // Count events
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM events");
            $stmt->execute();
            $stats['total_events'] = $stmt->fetch()['count'];

            // Count sports
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM sports");
            $stmt->execute();
            $stats['total_sports'] = $stmt->fetch()['count'];

            // Count event_sports
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM event_sports");
            $stmt->execute();
            $stats['total_event_sports'] = $stmt->fetch()['count'];

            // Count registrations
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM registrations");
            $stmt->execute();
            $stats['total_registrations'] = $stmt->fetch()['count'];

            // Count tournaments
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournaments");
            $stmt->execute();
            $stats['total_tournaments'] = $stmt->fetch()['count'];

            echo json_encode([
                'success' => true,
                'results' => $stats
            ]);
            break;

        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    
    // Clear any output buffer to ensure clean JSON
    ob_clean();
    
    // Log the error for debugging
    error_log("Database Investigation Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'file' => basename($e->getFile()),
            'line' => $e->getLine(),
            'action' => $_POST['action'] ?? 'unknown'
        ]
    ]);
}

// Ensure output buffer is flushed
ob_end_flush();
?>
