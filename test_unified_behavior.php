<?php
/**
 * Comprehensive Test for Unified Registration Behavior
 * Tests the complete unified registration workflow
 */

require_once 'config/database.php';
require_once 'includes/department_registration.php';
require_once 'includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Testing Unified Registration Behavior</h2>\n";

// Get test data
$stmt = $conn->prepare("SELECT id, name FROM events ORDER BY id DESC LIMIT 1");
$stmt->execute();
$test_event = $stmt->fetch(PDO::FETCH_ASSOC);

$stmt = $conn->prepare("SELECT id, name FROM departments ORDER BY id LIMIT 1");
$stmt->execute();
$test_department = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$test_event || !$test_department) {
    die("❌ Need at least one event and one department for testing\n");
}

$event_id = $test_event['id'];
$department_id = $test_department['id'];

echo "Test Event: {$test_event['name']} (ID: $event_id)<br>\n";
echo "Test Department: {$test_department['name']} (ID: $department_id)<br>\n";

// Step 1: Check existing sports in event
echo "<h3>Step 1: Check Existing Sports</h3>\n";
$stmt = $conn->prepare("
    SELECT es.id, s.name as sport_name 
    FROM event_sports es 
    JOIN sports s ON es.sport_id = s.id 
    WHERE es.event_id = ?
");
$stmt->execute([$event_id]);
$existing_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "Existing sports in event: " . count($existing_sports) . "<br>\n";
foreach ($existing_sports as $sport) {
    echo "- {$sport['sport_name']} (Event Sport ID: {$sport['id']})<br>\n";
}

// Step 2: Clean up any existing registration for this test
echo "<h3>Step 2: Clean Up Previous Test Data</h3>\n";
$stmt = $conn->prepare("DELETE FROM event_department_registrations WHERE event_id = ? AND department_id = ?");
$stmt->execute([$event_id, $department_id]);
echo "Cleaned up any existing registration<br>\n";

// Step 3: Test unified registration
echo "<h3>Step 3: Test Unified Department Registration</h3>\n";
$registration_data = [
    'contact_person' => 'Test Contact',
    'contact_email' => '<EMAIL>',
    'contact_phone' => '************',
    'status' => 'approved'
];

$result = registerDepartmentForEvent($conn, $event_id, $department_id, $registration_data);

if ($result['success']) {
    echo "✅ Department registered successfully (Registration ID: {$result['registration_id']})<br>\n";
    
    // Check if department was automatically added to all existing sports
    $stmt = $conn->prepare("
        SELECT dsp.id, s.name as sport_name, dsp.notes
        FROM department_sport_participations dsp
        JOIN event_sports es ON dsp.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        WHERE dsp.event_department_registration_id = ?
    ");
    $stmt->execute([$result['registration_id']]);
    $participations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Sport participations created: " . count($participations) . "<br>\n";
    foreach ($participations as $participation) {
        echo "- {$participation['sport_name']} (Notes: {$participation['notes']})<br>\n";
    }
    
    if (count($participations) === count($existing_sports)) {
        echo "✅ Department automatically registered for ALL existing sports<br>\n";
    } else {
        echo "❌ Department not registered for all sports (Expected: " . count($existing_sports) . ", Got: " . count($participations) . ")<br>\n";
    }
} else {
    echo "❌ Registration failed: {$result['message']}<br>\n";
    exit;
}

// Step 4: Test adding a new sport and automatic department registration
echo "<h3>Step 4: Test Adding New Sport with Automatic Registration</h3>\n";

// Find a sport not yet in the event
$stmt = $conn->prepare("
    SELECT s.id, s.name 
    FROM sports s 
    WHERE s.id NOT IN (
        SELECT es.sport_id 
        FROM event_sports es 
        WHERE es.event_id = ?
    ) 
    LIMIT 1
");
$stmt->execute([$event_id]);
$new_sport = $stmt->fetch(PDO::FETCH_ASSOC);

if ($new_sport) {
    echo "Adding sport: {$new_sport['name']} (ID: {$new_sport['id']})<br>\n";
    
    // Add sport to event (simulating the admin action)
    $stmt = $conn->prepare("
        INSERT INTO event_sports (event_id, sport_id, max_teams, registration_deadline) 
        VALUES (?, ?, NULL, NULL)
    ");
    $stmt->execute([$event_id, $new_sport['id']]);
    $new_event_sport_id = $conn->lastInsertId();
    
    echo "Sport added to event (Event Sport ID: $new_event_sport_id)<br>\n";
    
    // Test automatic department registration
    $auto_result = addAllRegisteredDepartmentsToSport($conn, $new_event_sport_id);
    
    if ($auto_result['success']) {
        echo "✅ Automatic registration successful<br>\n";
        echo "Departments automatically added: {$auto_result['departments_added']}<br>\n";
        
        // Verify the department was added
        $stmt = $conn->prepare("
            SELECT dsp.id, dsp.notes
            FROM department_sport_participations dsp
            WHERE dsp.event_sport_id = ? AND dsp.event_department_registration_id = ?
        ");
        $stmt->execute([$new_event_sport_id, $result['registration_id']]);
        $new_participation = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($new_participation) {
            echo "✅ Department automatically added to new sport (Notes: {$new_participation['notes']})<br>\n";
        } else {
            echo "❌ Department was not automatically added to new sport<br>\n";
        }
    } else {
        echo "❌ Automatic registration failed: {$auto_result['message']}<br>\n";
    }
} else {
    echo "No available sports to add for testing<br>\n";
}

// Step 5: Final verification
echo "<h3>Step 5: Final Verification</h3>\n";
$stmt = $conn->prepare("
    SELECT COUNT(*) as sport_count
    FROM event_sports 
    WHERE event_id = ?
");
$stmt->execute([$event_id]);
$total_sports = $stmt->fetchColumn();

$stmt = $conn->prepare("
    SELECT COUNT(*) as participation_count
    FROM department_sport_participations dsp
    JOIN event_sports es ON dsp.event_sport_id = es.id
    WHERE es.event_id = ? AND dsp.event_department_registration_id = ?
");
$stmt->execute([$event_id, $result['registration_id']]);
$total_participations = $stmt->fetchColumn();

echo "Total sports in event: $total_sports<br>\n";
echo "Department participations: $total_participations<br>\n";

if ($total_sports == $total_participations) {
    echo "✅ UNIFIED REGISTRATION WORKING CORRECTLY - Department participates in ALL sports<br>\n";
} else {
    echo "❌ UNIFIED REGISTRATION ISSUE - Department not participating in all sports<br>\n";
}

echo "<h3>Test Complete</h3>\n";
?>
