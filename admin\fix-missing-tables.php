<?php
/**
 * Fix Missing Database Tables
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix Missing Database Tables</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .danger { background: #dc3545; }
        .danger:hover { background: #c82333; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Fix Missing Database Tables</h1>
    
    <div class="info">
        <h3>⚠️ Important Notice</h3>
        <p>This tool will create missing database tables required for the tournament system.</p>
        <p>Make sure you have a database backup before proceeding.</p>
    </div>
    
    <button onclick="createMissingTables()">Create Missing Tables</button>
    <button onclick="checkTableStatus()" class="info">Check Table Status</button>
    
    <div id="result"></div>

    <script>
        async function createMissingTables() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Creating missing tables...</p>';
            
            try {
                const response = await fetch('ajax/fix-database-tables.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=create_missing_tables'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `<div class="success">✅ ${data.message}</div>`;
                        if (data.details) {
                            resultDiv.innerHTML += `<pre>${data.details}</pre>`;
                        }
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="error">❌ JSON Parse Error: ${e.message}</div>`;
                    resultDiv.innerHTML += `<pre>Raw Response: ${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }
        
        async function checkTableStatus() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Checking table status...</p>';
            
            try {
                const response = await fetch('ajax/fix-database-tables.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=check_tables'
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        let html = '<div class="info"><h4>Table Status</h4>';
                        for (const [table, exists] of Object.entries(data.tables)) {
                            const status = exists ? '✅ Exists' : '❌ Missing';
                            const color = exists ? 'green' : 'red';
                            html += `<p style="color: ${color};">${table}: ${status}</p>`;
                        }
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="error">❌ JSON Parse Error: ${e.message}</div>`;
                    resultDiv.innerHTML += `<pre>Raw Response: ${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
