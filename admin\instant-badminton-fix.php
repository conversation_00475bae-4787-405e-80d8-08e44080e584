<?php
/**
 * Instant Badminton Tournament Fix
 * SC_IMS Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>⚡ Instant Badminton Tournament Fix</h1>";
echo "<p>This will immediately fix your Badminton tournament creation issue...</p>";

$badminton_event_sport_id = 18;

try {
    $conn->beginTransaction();
    
    echo "<h2>🔄 Applying Fixes...</h2>";
    
    // 1. Fix unified registration system status
    $stmt = $conn->prepare("
        UPDATE department_sport_participations dsp
        JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
        SET dsp.status = 'confirmed', edr.status = 'approved'
        WHERE dsp.event_sport_id = ?
    ");
    $stmt->execute([$badminton_event_sport_id]);
    $unified_fixed = $stmt->rowCount();
    echo "<p style='color: green;'>✅ Fixed {$unified_fixed} unified registrations</p>";
    
    // 2. Fix old registration system as backup
    $stmt = $conn->prepare("
        UPDATE registrations 
        SET status = 'confirmed' 
        WHERE event_sport_id = ? AND status != 'confirmed'
    ");
    $stmt->execute([$badminton_event_sport_id]);
    $old_fixed = $stmt->rowCount();
    echo "<p style='color: green;'>✅ Fixed {$old_fixed} old system registrations</p>";
    
    // 3. Verify the fix worked
    $stmt = $conn->prepare("
        SELECT 
            dsp.id,
            COALESCE(dsp.team_name, d.name) as team_name,
            edr.department_id,
            d.name as department_name,
            dsp.status
        FROM event_department_registrations edr
        JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        JOIN departments d ON edr.department_id = d.id
        WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status = 'confirmed'
    ");
    $stmt->execute([$badminton_event_sport_id]);
    $participants = $stmt->fetchAll();
    
    $conn->commit();
    
    echo "<h2>🧪 Verification Test</h2>";
    if (count($participants) >= 2) {
        echo "<p style='color: green; font-size: 20px; font-weight: bold;'>🎉 SUCCESS! Found " . count($participants) . " confirmed participants!</p>";
        
        echo "<h3>Confirmed Participants:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #d4edda;'><th>Team Name</th><th>Department</th><th>Status</th></tr>";
        foreach ($participants as $p) {
            echo "<tr><td>{$p['team_name']}</td><td>{$p['department_name']}</td><td style='color: green; font-weight: bold;'>{$p['status']}</td></tr>";
        }
        echo "</table>";
        
        echo "<h2>🏆 Tournament Creation Ready!</h2>";
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Problem Solved!</h3>";
        echo "<p><strong>Your Badminton tournament creation should now work perfectly!</strong></p>";
        echo "<p><a href='manage-category.php?event_id=3&sport_id=40&category_id=2' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; font-size: 18px; font-weight: bold; display: inline-block; margin: 10px 0;'>🏆 Create Badminton Tournament Now</a></p>";
        echo "</div>";
        
        echo "<h3>📋 What Was Fixed:</h3>";
        echo "<ul>";
        echo "<li>✅ Updated registration status from 'registered' to 'confirmed'</li>";
        echo "<li>✅ Updated event registration status to 'approved'</li>";
        echo "<li>✅ Verified tournament creation query finds participants</li>";
        echo "<li>✅ Ready for immediate tournament creation</li>";
        echo "</ul>";
        
    } else {
        echo "<p style='color: red; font-size: 18px;'>❌ Still only found " . count($participants) . " participants after fix</p>";
        echo "<p>There may be a deeper issue. Let me check for registrations...</p>";
        
        // Check if registrations exist at all
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM department_sport_participations dsp
            WHERE dsp.event_sport_id = ?
        ");
        $stmt->execute([$badminton_event_sport_id]);
        $total_count = $stmt->fetch()['count'];
        
        if ($total_count == 0) {
            echo "<p style='color: red;'>No registrations found at all. You need to create registrations first.</p>";
            echo "<p><a href='quick-registration-fix.php?event_sport_id={$badminton_event_sport_id}' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Create Badminton Registrations</a></p>";
        } else {
            echo "<p>Found {$total_count} total registrations but they don't meet tournament criteria.</p>";
        }
    }
    
} catch (Exception $e) {
    $conn->rollBack();
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h3>🔍 Technical Details</h3>";
echo "<details>";
echo "<summary>Click to see what the fix does</summary>";
echo "<p><strong>SQL Updates Applied:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px;'>";
echo "-- Fix unified registration system\n";
echo "UPDATE department_sport_participations dsp\n";
echo "JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id\n";
echo "SET dsp.status = 'confirmed', edr.status = 'approved'\n";
echo "WHERE dsp.event_sport_id = {$badminton_event_sport_id};\n\n";
echo "-- Fix old registration system (backup)\n";
echo "UPDATE registrations \n";
echo "SET status = 'confirmed' \n";
echo "WHERE event_sport_id = {$badminton_event_sport_id};";
echo "</pre>";
echo "</details>";
?>
