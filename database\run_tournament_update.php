<?php
/**
 * Database Tournament Schema Update Script
 * SC_IMS Sports Competition and Event Management System
 */

require_once __DIR__ . '/../config/database.php';

echo "Starting tournament schema update...\n";

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    if (!$conn) {
        throw new Exception('Could not connect to database');
    }
    // Read the SQL file
    $sql = file_get_contents(__DIR__ . '/tournament_schema_update.sql');
    
    if ($sql === false) {
        throw new Exception('Could not read tournament_schema_update.sql file');
    }
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
            try {
                $conn->exec($statement);
                $successCount++;
                echo "✓ Executed: " . substr(str_replace(["\n", "\r"], ' ', $statement), 0, 80) . "...\n";
            } catch (PDOException $e) {
                $errorCount++;
                echo "✗ Error: " . $e->getMessage() . "\n";
                echo "  Statement: " . substr(str_replace(["\n", "\r"], ' ', $statement), 0, 80) . "...\n";
            }
        }
    }
    
    echo "\n=== Tournament Schema Update Summary ===\n";
    echo "Successful statements: {$successCount}\n";
    echo "Failed statements: {$errorCount}\n";
    
    if ($errorCount == 0) {
        echo "✓ Tournament schema update completed successfully!\n";
    } else {
        echo "⚠ Tournament schema update completed with {$errorCount} errors.\n";
    }
    
} catch (Exception $e) {
    echo "Fatal error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
