<?php
/**
 * Test Script for SC_IMS Functions
 * Tests database functions to ensure they work correctly
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$tests = [];
$errors = [];

try {
    // Test 1: Get all events
    $events = getAllEvents($conn);
    $tests[] = "✅ getAllEvents() - Found " . count($events) . " events";
    
    if (!empty($events)) {
        $event_id = $events[0]['id'];
        
        // Test 2: Get event by ID
        $event = getEventById($conn, $event_id);
        if ($event) {
            $tests[] = "✅ getEventById($event_id) - Event: " . $event['name'];
        } else {
            $errors[] = "❌ getEventById($event_id) - Failed to get event";
        }
        
        // Test 3: Get event sports
        $event_sports = getEventSports($conn, $event_id);
        $tests[] = "✅ getEventSports($event_id) - Found " . count($event_sports) . " sports";
        
        // Test 4: Get available sports
        $available_sports = getAvailableSports($conn, $event_id);
        $tests[] = "✅ getAvailableSports($event_id) - Found " . count($available_sports) . " available sports";
        
        // Test 5: Get event standings
        $standings = getEventStandings($conn, $event_id);
        $tests[] = "✅ getEventStandings($event_id) - Found " . count($standings) . " departments in standings";
        
        // Test 6: Get recent matches
        $recent_matches = getEventRecentMatches($conn, $event_id);
        $tests[] = "✅ getEventRecentMatches($event_id) - Found " . count($recent_matches) . " recent matches";
    } else {
        $errors[] = "❌ No events found in database";
    }
    
    // Test 7: Get all departments
    $departments = getAllDepartments($conn);
    $tests[] = "✅ getAllDepartments() - Found " . count($departments) . " departments";
    
    // Test 8: Get all sports
    $sports = getAllSports($conn);
    $tests[] = "✅ getAllSports() - Found " . count($sports) . " sports";
    
} catch (Exception $e) {
    $errors[] = "❌ Exception: " . $e->getMessage();
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Function Tests - SC_IMS Admin</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-results {
            margin: 20px 0;
        }
        .test-results h3 {
            margin-bottom: 15px;
        }
        .test-list {
            list-style-type: none;
            padding: 0;
        }
        .test-list li {
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
        }
        .test-success {
            background-color: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        .test-error {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        .summary {
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: center;
        }
        .summary.success {
            background-color: #d4edda;
            color: #155724;
        }
        .summary.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .back-button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .back-button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>SC_IMS Function Tests</h1>
            <p>Testing database functions and connectivity</p>
        </div>
        
        <?php if (!empty($tests)): ?>
        <div class="test-results">
            <h3>Test Results:</h3>
            <ul class="test-list">
                <?php foreach ($tests as $test): ?>
                <li class="test-success"><?php echo htmlspecialchars($test); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <?php if (!empty($errors)): ?>
        <div class="test-results">
            <h3>Errors Found:</h3>
            <ul class="test-list">
                <?php foreach ($errors as $error): ?>
                <li class="test-error"><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>
        
        <div class="summary <?php echo empty($errors) ? 'success' : 'error'; ?>">
            <strong>
                <?php if (empty($errors)): ?>
                    ✅ All tests passed! Functions are working correctly.
                <?php else: ?>
                    ⚠️ Some tests failed. Please check the errors above.
                <?php endif; ?>
            </strong>
            <br>
            <small>Tests completed: <?php echo count($tests); ?> | Errors: <?php echo count($errors); ?></small>
        </div>
        
        <a href="index.php" class="back-button">← Back to Admin Dashboard</a>
        <a href="manage-event.php?event_id=1" class="back-button">Test Event Management →</a>
    </div>
</body>
</html>
