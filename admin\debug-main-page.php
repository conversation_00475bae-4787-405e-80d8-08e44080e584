<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$current_admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? 1;
$event = getEventById($conn, $event_id);
if (!$event) {
    $event = ['name' => 'Test Event', 'description' => 'Test Description'];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Main Page JavaScript</title>
    
    <!-- Include admin styles (same as main page) -->
    <?php include 'includes/admin-styles.php'; ?>
    
    <style>
        .debug-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .debug-log {
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            background: white;
            padding: 10px;
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .test-button {
            margin: 5px;
            padding: 8px 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JavaScript Debug Analysis</h1>
        
        <div class="debug-section">
            <h3>1. Script Loading Test</h3>
            <div id="scriptLoadLog" class="debug-log"></div>
            <button onclick="testScriptLoading()" class="test-button btn btn-primary">Test Script Loading</button>
        </div>
        
        <div class="debug-section">
            <h3>2. EventTabManager Test</h3>
            <div id="tabManagerLog" class="debug-log"></div>
            <button onclick="testEventTabManager()" class="test-button btn btn-primary">Test EventTabManager</button>
        </div>
        
        <div class="debug-section">
            <h3>3. DOM Elements Test</h3>
            <div id="domLog" class="debug-log"></div>
            <button onclick="testDOMElements()" class="test-button btn btn-primary">Test DOM Elements</button>
        </div>
        
        <div class="debug-section">
            <h3>4. Event Listeners Test</h3>
            <div id="eventLog" class="debug-log"></div>
            <button onclick="testEventListeners()" class="test-button btn btn-primary">Test Event Listeners</button>
        </div>
        
        <div class="debug-section">
            <h3>5. Simple Tab Test (Like Minimal Page)</h3>
            <div class="section-tabs" style="margin: 10px 0;">
                <button class="tab-button active" data-tab="test1" onclick="simpleShowTab('test1', this)">Test 1</button>
                <button class="tab-button" data-tab="test2" onclick="simpleShowTab('test2', this)">Test 2</button>
            </div>
            <div id="test1-tab" class="tab-content active">Test 1 Content</div>
            <div id="test2-tab" class="tab-content">Test 2 Content</div>
            <div id="simpleTabLog" class="debug-log"></div>
        </div>
    </div>

    <!-- Include admin scripts (same as main page) -->
    <?php include 'includes/admin-scripts.php'; ?>

    <script>
        function log(section, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById(section + 'Log');
            if (logDiv) {
                logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
                logDiv.scrollTop = logDiv.scrollHeight;
            }
            console.log(`[${section}] ${message}`);
        }
        
        function testScriptLoading() {
            log('scriptLoad', 'Testing script loading...');
            log('scriptLoad', 'jQuery available: ' + (typeof $ !== 'undefined'));
            log('scriptLoad', 'Document ready state: ' + document.readyState);
            log('scriptLoad', 'Window EventTabManager: ' + (typeof window.EventTabManager !== 'undefined'));
            log('scriptLoad', 'Window modalManager: ' + (typeof window.modalManager !== 'undefined'));
            
            // Check for JavaScript errors
            window.addEventListener('error', function(e) {
                log('scriptLoad', 'JavaScript Error: ' + e.message + ' at ' + e.filename + ':' + e.lineno);
            });
        }
        
        function testEventTabManager() {
            log('tabManager', 'Testing EventTabManager...');
            
            if (typeof window.EventTabManager === 'undefined') {
                log('tabManager', 'ERROR: EventTabManager not found');
                return;
            }
            
            log('tabManager', 'EventTabManager exists');
            log('tabManager', 'Current tab: ' + window.EventTabManager.currentTab);
            
            try {
                const result = window.EventTabManager.showTab('standings');
                log('tabManager', 'showTab test result: ' + result);
            } catch (e) {
                log('tabManager', 'ERROR calling showTab: ' + e.message);
            }
        }
        
        function testDOMElements() {
            log('dom', 'Testing DOM elements...');
            
            const tabButtons = document.querySelectorAll('.tab-button');
            log('dom', 'Tab buttons found: ' + tabButtons.length);
            
            const tabContents = document.querySelectorAll('.tab-content');
            log('dom', 'Tab contents found: ' + tabContents.length);
            
            tabButtons.forEach((button, index) => {
                const dataTab = button.getAttribute('data-tab');
                const onclick = button.getAttribute('onclick');
                log('dom', `Button ${index}: data-tab="${dataTab}", onclick="${onclick}"`);
            });
        }
        
        function testEventListeners() {
            log('event', 'Testing event listeners...');
            
            // Test if click events work
            const testButton = document.createElement('button');
            testButton.textContent = 'Test Click';
            testButton.onclick = function() {
                log('event', 'Test button clicked successfully');
            };
            
            // Simulate click
            testButton.click();
            
            // Test event delegation
            document.addEventListener('click', function(e) {
                if (e.target.matches('.test-event-button')) {
                    log('event', 'Event delegation working');
                }
            });
            
            const delegationTest = document.createElement('button');
            delegationTest.className = 'test-event-button';
            delegationTest.textContent = 'Delegation Test';
            document.body.appendChild(delegationTest);
            delegationTest.click();
            document.body.removeChild(delegationTest);
        }
        
        // Simple tab function like the minimal page
        function simpleShowTab(tabName, clickedButton) {
            log('simpleTab', `simpleShowTab called: ${tabName}`);
            
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                log('simpleTab', `Activated: ${tabName}-tab`);
            } else {
                log('simpleTab', `ERROR: Tab not found: ${tabName}-tab`);
            }

            // Add active class to clicked button
            if (clickedButton) {
                clickedButton.classList.add('active');
                log('simpleTab', 'Button activated');
            }
        }
        
        // Auto-run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('scriptLoad', 'DOM loaded, running auto-tests...');
            setTimeout(() => {
                testScriptLoading();
                testEventTabManager();
                testDOMElements();
            }, 1000);
        });
    </script>
</body>
</html>
