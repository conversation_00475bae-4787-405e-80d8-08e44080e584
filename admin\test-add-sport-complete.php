<?php
/**
 * Complete Add Sport Test
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Complete Add Sport Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-dialog { position: relative; margin: 50px auto; width: 90%; max-width: 600px; background: white; border-radius: 5px; }
        .modal-header { padding: 15px; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center; }
        .modal-body { padding: 20px; }
        .modal-footer { padding: 15px; border-top: 1px solid #ddd; text-align: right; }
        .form-group { margin-bottom: 15px; }
        .form-label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; }
        .btn { padding: 8px 16px; border: none; border-radius: 3px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <h1>Complete Add Sport Test</h1>
    
    <div class="section">
        <h3>1. Current Event Sports Status</h3>
        <div id="current-sports"></div>
        <button onclick="loadCurrentSports()">Refresh Current Sports</button>
    </div>
    
    <div class="section">
        <h3>2. Test Add Sport Modal</h3>
        <button onclick="openAddSportModal()">Open Add Sport Modal</button>
        <div id="modal-test-result"></div>
    </div>
    
    <div class="section">
        <h3>3. Direct AJAX Test</h3>
        <button onclick="testDirectAjax()">Test Direct AJAX Call</button>
        <div id="ajax-test-result"></div>
    </div>
    
    <!-- Add Sport Modal -->
    <div id="addSportModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3>Add Sport to Event</h3>
                <button type="button" onclick="closeModal()">×</button>
            </div>
            <form id="addSportForm" onsubmit="submitAddSport(event)">
                <div class="modal-body">
                    <input type="hidden" name="event_id" value="1">
                    <input type="hidden" name="csrf_token" id="csrf_token" value="">
                    
                    <div class="form-group">
                        <label class="form-label">Sport:</label>
                        <select name="sport_id" id="sport_id" class="form-control" required onchange="loadTournamentFormats()">
                            <option value="">Select a sport...</option>
                            <option value="2" data-type="team">Volleyball (Team Sports)</option>
                            <option value="3" data-type="individual">Tennis (Individual Sports)</option>
                            <option value="4" data-type="academic">Chess (Academic Games)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Tournament Format:</label>
                        <select name="tournament_format_id" id="tournament_format_id" class="form-control" required>
                            <option value="">Select tournament format...</option>
                        </select>
                        <div id="format-info"></div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Seeding Method:</label>
                        <select name="seeding_method" class="form-control">
                            <option value="random">Random Seeding</option>
                            <option value="ranking">Ranking Based</option>
                            <option value="manual">Manual Seeding</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Sport</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        async function loadCurrentSports() {
            const resultDiv = document.getElementById('current-sports');
            resultDiv.innerHTML = '<p>Loading current sports...</p>';
            
            try {
                const response = await fetch('ajax/investigate-database.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=show_event_sports&event_id=1'
                });
                
                const text = await response.text();
                const data = JSON.parse(text);
                
                if (data.success) {
                    if (data.sports && data.sports.length > 0) {
                        let html = '<table style="width:100%; border-collapse: collapse;">';
                        html += '<tr style="background: #f8f9fa;"><th style="border:1px solid #ddd; padding:8px;">ID</th><th style="border:1px solid #ddd; padding:8px;">Sport</th><th style="border:1px solid #ddd; padding:8px;">Type</th></tr>';
                        data.sports.forEach(sport => {
                            html += `<tr><td style="border:1px solid #ddd; padding:8px;">${sport.id}</td><td style="border:1px solid #ddd; padding:8px;">${sport.sport_name}</td><td style="border:1px solid #ddd; padding:8px;">${sport.sport_type || 'N/A'}</td></tr>`;
                        });
                        html += '</table>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<p>No sports currently added to Event 1.</p>';
                    }
                } else {
                    resultDiv.innerHTML = `<div class="error">Error: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function openAddSportModal() {
            // Get CSRF token first
            try {
                const tokenResponse = await fetch('../includes/get_csrf_token.php');
                const tokenData = await tokenResponse.json();
                document.getElementById('csrf_token').value = tokenData.token;
                
                document.getElementById('addSportModal').style.display = 'block';
                document.getElementById('modal-test-result').innerHTML = '<div class="info">Modal opened successfully</div>';
            } catch (error) {
                document.getElementById('modal-test-result').innerHTML = `<div class="error">Error getting CSRF token: ${error.message}</div>`;
            }
        }
        
        function closeModal() {
            document.getElementById('addSportModal').style.display = 'none';
        }
        
        async function loadTournamentFormats() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const formatInfo = document.getElementById('format-info');
            
            const selectedOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedOption.getAttribute('data-type');
            
            if (!sportType) {
                formatSelect.innerHTML = '<option value="">Select tournament format...</option>';
                formatInfo.innerHTML = '';
                return;
            }
            
            try {
                const response = await fetch('ajax/get-tournament-formats.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `sport_type=${sportType}`
                });
                
                const data = await response.json();
                
                if (data.success) {
                    formatSelect.innerHTML = '<option value="">Select tournament format...</option>';
                    data.formats.forEach(format => {
                        formatSelect.innerHTML += `<option value="${format.id}">${format.name}</option>`;
                    });
                } else {
                    formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                }
            } catch (error) {
                formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                console.error('Error loading tournament formats:', error);
            }
        }
        
        async function submitAddSport(event) {
            event.preventDefault();
            
            const form = event.target;
            const formData = new FormData(form);
            formData.append('action', 'add_sport');
            
            const resultDiv = document.getElementById('modal-test-result');
            resultDiv.innerHTML = '<div class="info">Submitting add sport request...</div>';
            
            try {
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `<div class="success">✅ Success: ${data.message}</div>`;
                        closeModal();
                        loadCurrentSports();
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="error">❌ JSON Parse Error: ${e.message}</div>`;
                    resultDiv.innerHTML += `<pre>Raw Response: ${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }
        
        async function testDirectAjax() {
            const resultDiv = document.getElementById('ajax-test-result');
            resultDiv.innerHTML = '<p>Testing direct AJAX call...</p>';
            
            try {
                // Get CSRF token
                const tokenResponse = await fetch('../includes/get_csrf_token.php');
                const tokenData = await tokenResponse.json();
                
                const formData = new FormData();
                formData.append('action', 'add_sport');
                formData.append('event_id', '1');
                formData.append('sport_id', '3'); // Tennis
                formData.append('tournament_format_id', '1');
                formData.append('seeding_method', 'random');
                formData.append('csrf_token', tokenData.token);
                
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                });
                
                const text = await response.text();
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `<div class="success">✅ Direct AJAX Success: ${data.message}</div>`;
                        loadCurrentSports();
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Direct AJAX Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="error">❌ JSON Parse Error: ${e.message}</div>`;
                    resultDiv.innerHTML += `<pre>Raw Response: ${text}</pre>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
            }
        }
        
        // Load current sports on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentSports();
        });
    </script>
</body>
</html>
