<?php
/**
 * Test Add Sport Modal Functionality
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Simulate admin session
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_id'] = 1;

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get available sports
$stmt = $conn->prepare("
    SELECT s.*, st.name as sport_type_name, st.category as sport_type_category
    FROM sports s
    LEFT JOIN sport_types st ON s.sport_type_id = st.id
    ORDER BY s.name
");
$stmt->execute();
$available_sports = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Sport Modal</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .debug { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <h1>Test Add Sport Modal</h1>
    
    <div class="debug">
        <h3>Available Sports:</h3>
        <ul>
            <?php foreach ($available_sports as $sport): ?>
                <li>
                    <strong><?php echo htmlspecialchars($sport['name']); ?></strong>
                    - Type: <?php echo htmlspecialchars($sport['sport_type_name'] ?? 'Unknown'); ?>
                    - Category: <?php echo htmlspecialchars($sport['sport_type_category'] ?? $sport['type'] ?? 'Unknown'); ?>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
    
    <form id="addSportForm">
        <div class="form-group">
            <label class="form-label" for="sport_id">Sport:</label>
            <select name="sport_id" id="sport_id" class="form-control" required onchange="loadBracketTypes()">
                <option value="">Select a sport...</option>
                <?php foreach ($available_sports as $sport): ?>
                    <option value="<?php echo $sport['id']; ?>"
                            data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>"
                            data-sport-type-name="<?php echo htmlspecialchars($sport['sport_type_name'] ?? ''); ?>">
                        <?php echo htmlspecialchars($sport['name']); ?>
                        <?php if ($sport['sport_type_name']): ?>
                            (<?php echo htmlspecialchars($sport['sport_type_name']); ?>)
                        <?php else: ?>
                            (<?php echo ucfirst($sport['type']); ?>)
                        <?php endif; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="form-group">
            <label class="form-label" for="tournament_format_id">Tournament Format:</label>
            <select name="tournament_format_id" id="tournament_format_id" class="form-control" required onchange="showFormatInfo()">
                <option value="">Select a sport first...</option>
            </select>
        </div>

        <div class="form-group">
            <label class="form-label" for="seeding_method">Seeding Method:</label>
            <select name="seeding_method" id="seeding_method" class="form-control">
                <option value="random">Random Seeding</option>
                <option value="ranking">Ranking-based Seeding</option>
                <option value="manual">Manual Seeding</option>
            </select>
        </div>

        <button type="button" class="btn" onclick="testAddSport()">Test Add Sport</button>
    </form>

    <div id="debug_output" class="debug" style="display: none;">
        <h3>Debug Output:</h3>
        <div id="debug_content"></div>
    </div>

    <script>
        // Load bracket types based on selected sport
        function loadBracketTypes() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const debugOutput = document.getElementById('debug_output');
            const debugContent = document.getElementById('debug_content');

            const sportId = sportSelect.value;

            // Show debug output
            debugOutput.style.display = 'block';
            debugContent.innerHTML = '<p class="info">Loading tournament formats...</p>';

            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';

            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                debugContent.innerHTML = '<p class="error">No sport selected</p>';
                return;
            }

            // Get sport type from the selected option
            const selectedSportOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedSportOption.dataset.type || 'traditional';
            const sportName = selectedSportOption.textContent;

            debugContent.innerHTML += `<p class="info">Selected sport: ${sportName}</p>`;
            debugContent.innerHTML += `<p class="info">Sport type: ${sportType}</p>`;

            // Fetch tournament formats from database
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => {
                debugContent.innerHTML += `<p class="info">Response status: ${response.status}</p>`;
                return response.text();
            })
            .then(text => {
                debugContent.innerHTML += `<p class="info">Raw response:</p><pre>${text}</pre>`;
                
                try {
                    const data = JSON.parse(text);
                    debugContent.innerHTML += `<p class="info">Parsed JSON successfully</p>`;
                    
                    formatSelect.innerHTML = '<option value="">Select tournament format...</option>';

                    if (data.success && data.formats) {
                        debugContent.innerHTML += `<p class="success">Found ${data.formats.length} formats</p>`;
                        
                        data.formats.forEach(format => {
                            const option = document.createElement('option');
                            option.value = format.id;
                            option.textContent = format.name;
                            option.dataset.description = format.description;
                            option.dataset.minParticipants = format.min_participants;
                            option.dataset.maxParticipants = format.max_participants || 'Unlimited';
                            option.dataset.seedingRequired = format.requires_seeding;
                            option.dataset.advancementType = format.advancement_type;
                            formatSelect.appendChild(option);
                            
                            debugContent.innerHTML += `<p class="success">Added format: ${format.name} (ID: ${format.id})</p>`;
                        });
                    } else {
                        formatSelect.innerHTML = '<option value="">No formats available</option>';
                        debugContent.innerHTML += `<p class="error">No formats found or error: ${data.message || 'Unknown error'}</p>`;
                    }
                } catch (e) {
                    debugContent.innerHTML += `<p class="error">JSON parse error: ${e.message}</p>`;
                    formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                }
            })
            .catch(error => {
                console.error('Error loading tournament formats:', error);
                debugContent.innerHTML += `<p class="error">Fetch error: ${error.message}</p>`;
                formatSelect.innerHTML = '<option value="">Error loading formats</option>';
            });
        }

        function showFormatInfo() {
            const formatSelect = document.getElementById('tournament_format_id');
            const selectedOption = formatSelect.options[formatSelect.selectedIndex];
            
            if (selectedOption && selectedOption.value) {
                const debugContent = document.getElementById('debug_content');
                debugContent.innerHTML += `<p class="info">Selected format: ${selectedOption.textContent}</p>`;
                debugContent.innerHTML += `<p class="info">Description: ${selectedOption.dataset.description}</p>`;
                debugContent.innerHTML += `<p class="info">Min participants: ${selectedOption.dataset.minParticipants}</p>`;
                debugContent.innerHTML += `<p class="info">Max participants: ${selectedOption.dataset.maxParticipants}</p>`;
            }
        }

        function testAddSport() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const seedingSelect = document.getElementById('seeding_method');
            const debugContent = document.getElementById('debug_content');
            
            debugContent.innerHTML += '<hr><h4>Test Add Sport Results:</h4>';
            debugContent.innerHTML += `<p>Sport ID: ${sportSelect.value}</p>`;
            debugContent.innerHTML += `<p>Format ID: ${formatSelect.value}</p>`;
            debugContent.innerHTML += `<p>Seeding: ${seedingSelect.value}</p>`;
            
            if (!sportSelect.value || !formatSelect.value) {
                debugContent.innerHTML += '<p class="error">Please select both sport and tournament format</p>';
                return;
            }
            
            debugContent.innerHTML += '<p class="success">All required fields selected - ready to add sport!</p>';
        }
    </script>
</body>
</html>
