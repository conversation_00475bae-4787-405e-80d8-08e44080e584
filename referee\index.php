<?php
/**
 * Referee Dashboard for SC_IMS
 * Sports Competition and Event Management System
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get upcoming matches for scoring
$assigned_matches = getUpcomingMatches($conn, 50);

// Get recent scoring activity
$recent_scores = getRecentResults($conn, 10);

// Get live matches
$live_matches = getLiveMatches($conn);

function getRefereeMatches($conn, $referee_id) {
    $sql = "SELECT m.*, 
            es.bracket_type,
            s.name as sport_name, s.scoring_system,
            e.name as event_name,
            r1.department_id as team1_dept_id, d1.name as team1_name, d1.abbreviation as team1_abbr, d1.color_code as team1_color,
            r2.department_id as team2_dept_id, d2.name as team2_name, d2.abbreviation as team2_abbr, d2.color_code as team2_color
            FROM matches m
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN events e ON es.event_id = e.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE m.referee_id = ? OR ? IN (SELECT id FROM users WHERE role = 'admin')
            ORDER BY 
                CASE m.status 
                    WHEN 'live' THEN 1 
                    WHEN 'scheduled' THEN 2 
                    WHEN 'completed' THEN 3 
                    ELSE 4 
                END,
                m.scheduled_time ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$referee_id, $referee_id]);
    return $stmt->fetchAll();
}

function getRecentScores($conn, $referee_id, $limit) {
    $sql = "SELECT sc.*, m.round,
            s.name as sport_name,
            d1.name as team1_name, d1.abbreviation as team1_abbr,
            d2.name as team2_name, d2.abbreviation as team2_abbr
            FROM scores sc
            JOIN matches m ON sc.match_id = m.id
            JOIN event_sports es ON m.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN registrations r1 ON m.team1_id = r1.id
            JOIN departments d1 ON r1.department_id = d1.id
            LEFT JOIN registrations r2 ON m.team2_id = r2.id
            LEFT JOIN departments d2 ON r2.department_id = d2.id
            WHERE sc.referee_id = ?
            ORDER BY sc.created_at DESC
            LIMIT ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$referee_id, $limit]);
    return $stmt->fetchAll();
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referee Dashboard - <?php echo APP_NAME; ?></title>

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-color: #059669;
            --primary-dark: #047857;
            --secondary-color: #0891b2;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f0fdf4;
            --border-color: #d1fae5;
            --text-primary: #064e3b;
            --text-secondary: #065f46;
            --live-color: #dc2626;
            --live-bg: #fef2f2;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--light-color);
            line-height: 1.6;
            color: var(--text-primary);
            font-size: 16px;
        }

        /* Header Styles */
        .referee-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 32px 24px;
            border-radius: var(--border-radius);
            margin-bottom: 32px;
            box-shadow: var(--shadow-lg);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .referee-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>') repeat;
            background-size: 40px 40px;
        }

        .referee-header-content {
            position: relative;
            z-index: 1;
        }

        .referee-header h2 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
        }

        .referee-header p {
            font-size: 1.125rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 24px;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: white;
            padding: 24px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 2px solid var(--border-color);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 56px;
            height: 56px;
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: white;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .stat-content {
            text-align: center;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            color: var(--primary-color);
            margin-bottom: 8px;
            line-height: 1;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* Live Indicator */
        .live-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: var(--live-color);
            border-radius: 50%;
            animation: pulse 1.5s infinite;
            margin-right: 8px;
            box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2);
            }
            50% {
                transform: scale(1.1);
                box-shadow: 0 0 0 8px rgba(220, 38, 38, 0.1);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2);
            }
        }

        /* Live Matches Alert */
        .live-matches-alert {
            background: linear-gradient(135deg, var(--live-bg), #fef7f7);
            border: 2px solid var(--live-color);
            border-radius: var(--border-radius);
            padding: 24px;
            margin-bottom: 32px;
            box-shadow: var(--shadow-md);
        }

        .live-matches-alert h3 {
            color: var(--live-color);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .live-matches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
        }

        .live-match-card {
            background: white;
            border: 2px solid var(--live-color);
            border-radius: 12px;
            padding: 20px;
            transition: var(--transition);
        }

        .live-match-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        /* Match Cards */
        .match-card {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            margin-bottom: 24px;
            overflow: hidden;
            transition: var(--transition);
        }

        .match-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .match-header {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            padding: 20px 24px;
            border-bottom: 2px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .match-info h4 {
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .match-info p {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin: 0;
        }

        .match-body {
            padding: 24px;
        }

        .teams-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 24px;
            background: linear-gradient(135deg, #f0fdf4, #ecfdf5);
            border-radius: 12px;
            border: 2px solid var(--border-color);
        }

        .team {
            text-align: center;
            flex: 1;
        }

        .team-name {
            font-weight: 700;
            font-size: 1.125rem;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .team-score {
            font-size: 2.5rem;
            font-weight: 900;
            color: var(--primary-color);
            line-height: 1;
        }

        .vs-divider {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--text-secondary);
            margin: 0 24px;
            padding: 12px;
            background: white;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-sm);
        }

        /* Status Badges */
        .status-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-scheduled {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 1px solid #3b82f6;
        }

        .status-live {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            color: var(--live-color);
            border: 1px solid var(--live-color);
        }

        .status-completed {
            background: linear-gradient(135deg, #d1fae5, #a7f3d0);
            color: #065f46;
            border: 1px solid var(--success-color);
        }

        /* Quick Actions */
        .quick-actions {
            text-align: center;
            margin-top: 24px;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 600;
            transition: var(--transition);
            box-shadow: var(--shadow-sm);
        }

        .btn:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #059669);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.75rem;
        }

        .btn-lg {
            padding: 16px 32px;
            font-size: 1rem;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
            border: 2px solid var(--border-color);
            margin-bottom: 32px;
            overflow: hidden;
            transition: var(--transition);
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .card-header {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            padding: 20px 24px;
            border-bottom: 2px solid var(--border-color);
            font-weight: 700;
            color: var(--text-primary);
            font-size: 1.125rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-body {
            padding: 24px;
        }

        /* Tables */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 16px;
        }

        .table th,
        .table td {
            padding: 16px 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table th {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            font-weight: 700;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .table-striped tbody tr:nth-child(odd) {
            background: #f9fafb;
        }

        .table tbody tr:hover {
            background: var(--light-color);
        }

        /* Utility Classes */
        .text-center {
            text-align: center;
        }

        .text-muted {
            color: var(--text-secondary);
        }

        .mb-0 { margin-bottom: 0; }
        .mb-2 { margin-bottom: 8px; }
        .mb-3 { margin-bottom: 16px; }
        .mb-4 { margin-bottom: 24px; }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .container {
                padding: 16px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .live-matches-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .referee-header {
                padding: 24px 16px;
            }

            .referee-header h2 {
                font-size: 2rem;
                flex-direction: column;
                gap: 8px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .teams-display {
                flex-direction: column;
                gap: 16px;
                text-align: center;
            }

            .vs-divider {
                margin: 0;
                transform: rotate(90deg);
            }

            .match-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .card-body {
                padding: 16px;
            }

            .table {
                font-size: 0.875rem;
            }

            .table th,
            .table td {
                padding: 12px 8px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 12px;
            }

            .referee-header {
                padding: 20px 12px;
            }

            .referee-header h2 {
                font-size: 1.75rem;
            }

            .stat-card {
                padding: 16px;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .match-card {
                margin-bottom: 16px;
            }

            .match-header,
            .card-header {
                padding: 16px;
            }

            .match-body,
            .card-body {
                padding: 16px;
            }

            .btn {
                padding: 10px 16px;
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="referee-header">
            <div class="referee-header-content">
                <h2>
                    <i class="fas fa-whistle"></i>
                    Referee Interface
                </h2>
                <p>Focused scoring interface for real-time match management</p>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo count(array_filter($assigned_matches, fn($m) => $m['status'] === 'scheduled')); ?></div>
                    <div class="stat-label">Scheduled Matches</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-broadcast-tower"></i>
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo count(array_filter($assigned_matches, fn($m) => $m['status'] === 'live')); ?></div>
                    <div class="stat-label">Live Matches</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo count(array_filter($assigned_matches, fn($m) => $m['status'] === 'completed')); ?></div>
                    <div class="stat-label">Completed Matches</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon">
                        <i class="fas fa-history"></i>
                    </div>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><?php echo count($recent_scores); ?></div>
                    <div class="stat-label">Recent Scores</div>
                </div>
            </div>
        </div>

        <!-- Live Matches Alert -->
        <?php if (!empty($live_matches)): ?>
        <div class="live-matches-alert">
            <h3>
                <span class="live-indicator"></span>
                Live Matches in Progress
            </h3>
            <div class="live-matches-grid">
                <?php foreach ($live_matches as $live_match): ?>
                <div class="live-match-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <div>
                            <h4 style="margin: 0; color: var(--live-color); font-size: 1rem;">
                                <?php echo htmlspecialchars($live_match['sport_name']); ?>
                            </h4>
                            <p style="margin: 0; font-size: 0.875rem; color: var(--text-secondary);">
                                Round <?php echo $live_match['round']; ?>
                            </p>
                        </div>
                        <span class="status-badge status-live">
                            <span class="live-indicator"></span>
                            LIVE
                        </span>
                    </div>
                    <div style="text-align: center; margin-bottom: 16px;">
                        <strong style="color: var(--text-primary);">
                            <?php echo htmlspecialchars($live_match['team1_name'] . ' vs ' . $live_match['team2_name']); ?>
                        </strong>
                    </div>
                    <a href="scoring.php?match_id=<?php echo $live_match['id']; ?>" class="btn btn-danger btn-lg" style="width: 100%;">
                        <i class="fas fa-play"></i>
                        Continue Scoring
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Assigned Matches -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clipboard-list"></i>
                Your Assigned Matches
            </div>
            <div class="card-body">
                <?php if (!empty($assigned_matches)): ?>
                    <?php foreach ($assigned_matches as $match): ?>
                        <div class="match-card">
                            <div class="match-header">
                                <div class="match-info">
                                    <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                    <p><?php echo htmlspecialchars($match['event_name']); ?> - Round <?php echo $match['round']; ?></p>
                                </div>
                                <div>
                                    <span class="status-badge status-<?php echo $match['status']; ?>">
                                        <?php if ($match['status'] === 'live'): ?>
                                            <span class="live-indicator"></span>
                                        <?php endif; ?>
                                        <?php echo ucfirst($match['status']); ?>
                                    </span>
                                </div>
                            </div>
                            <div class="match-body">
                                <div class="teams-display">
                                    <div class="team">
                                        <div class="team-name"><?php echo htmlspecialchars($match['team1_name']); ?></div>
                                        <div class="team-score"><?php echo $match['team1_score'] ?? '0'; ?></div>
                                        <small style="color: var(--text-secondary);"><?php echo htmlspecialchars($match['team1_abbr']); ?></small>
                                    </div>
                                    <div class="vs-divider">VS</div>
                                    <div class="team">
                                        <div class="team-name"><?php echo htmlspecialchars($match['team2_name']); ?></div>
                                        <div class="team-score"><?php echo $match['team2_score'] ?? '0'; ?></div>
                                        <small style="color: var(--text-secondary);"><?php echo htmlspecialchars($match['team2_abbr']); ?></small>
                                    </div>

                                <div style="margin-bottom: 16px;">
                                    <?php if ($match['scheduled_time']): ?>
                                        <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">
                                            <i class="fas fa-clock"></i>
                                            <strong>Scheduled:</strong> <?php echo formatDateTime($match['scheduled_time']); ?>
                                        </p>
                                    <?php endif; ?>
                                    <p style="margin: 8px 0 0 0; color: var(--text-secondary); font-size: 0.875rem;">
                                        <i class="fas fa-cog"></i>
                                        <strong>Scoring System:</strong> <?php echo ucfirst($match['scoring_system']); ?>
                                    </p>
                                </div>

                                <div class="quick-actions">
                                    <?php if ($match['status'] === 'scheduled'): ?>
                                        <a href="scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-primary btn-lg">
                                            <i class="fas fa-play"></i>
                                            Start Match
                                        </a>
                                    <?php elseif ($match['status'] === 'live'): ?>
                                        <a href="scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-success btn-lg">
                                            <i class="fas fa-edit"></i>
                                            Continue Scoring
                                        </a>
                                    <?php else: ?>
                                        <a href="scoring.php?match_id=<?php echo $match['id']; ?>" class="btn btn-secondary">
                                            <i class="fas fa-eye"></i>
                                            View Results
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center" style="padding: 60px 20px;">
                        <i class="fas fa-clipboard-list" style="font-size: 4rem; color: var(--border-color); margin-bottom: 16px;"></i>
                        <p style="color: var(--text-secondary); font-style: italic; margin: 0; font-size: 1.125rem;">No matches assigned yet.</p>
                        <p style="color: var(--text-secondary); margin: 8px 0 0 0; font-size: 0.875rem;">Check back later for new assignments.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Scoring Activity -->
        <?php if (!empty($recent_scores)): ?>
        <div class="card">
            <div class="card-header">
                <i class="fas fa-history"></i>
                Recent Scoring Activity
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Match</th>
                            <th>Teams</th>
                            <th>Score</th>
                            <th>Time</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_scores as $score): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($score['sport_name']); ?></strong>
                                <br><small style="color: var(--text-secondary);">Round <?php echo $score['round']; ?></small>
                            </td>
                            <td><?php echo htmlspecialchars($score['team1_abbr'] . ' vs ' . $score['team2_abbr']); ?></td>
                            <td>
                                <span style="font-weight: 700; color: var(--primary-color);">
                                    <?php echo htmlspecialchars($score['team1_score'] . ' - ' . $score['team2_score']); ?>
                                </span>
                            </td>
                            <td style="color: var(--text-secondary);"><?php echo timeAgo($score['created_at']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>

        <!-- Navigation Footer -->
        <div style="text-align: center; margin-top: 48px; padding: 24px; background: white; border-radius: var(--border-radius); box-shadow: var(--shadow-sm); border: 2px solid var(--border-color);">
            <p style="margin-bottom: 16px; color: var(--text-secondary);">Quick Navigation</p>
            <div style="display: flex; gap: 16px; justify-content: center; flex-wrap: wrap;">
                <a href="../public/" class="btn btn-secondary">
                    <i class="fas fa-eye"></i>
                    Public Dashboard
                </a>
                <a href="../admin/" class="btn btn-secondary">
                    <i class="fas fa-cog"></i>
                    Admin Panel
                </a>
                <a href="scoring.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    New Scoring Session
                </a>
            </div>
        </div>
    </div>

    <!-- Auto-refresh script -->
    <script>
        // Auto-refresh every 30 seconds to keep live data current
        setTimeout(function() {
            location.reload();
        }, 30000);

        // Add visual feedback for live indicators
        document.addEventListener('DOMContentLoaded', function() {
            const liveIndicators = document.querySelectorAll('.live-indicator');
            liveIndicators.forEach(indicator => {
                indicator.style.animation = 'pulse 1.5s infinite';
            });
        });
    </script>
    </div>

    <script src="../assets/js/admin.js"></script>
    <script>
        // Auto-refresh every 30 seconds for live updates
        setInterval(function() {
            if (document.querySelector('.status-live')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
