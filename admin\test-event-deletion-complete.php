<?php
/**
 * Complete Event Deletion Test
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Complete Event Deletion Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        button.success { background: #28a745; }
        button.success:hover { background: #218838; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f8f9fa; }
    </style>
</head>
<body>
    <h1>Complete Event Deletion Test</h1>
    
    <div class="section">
        <h3>1. Create Test Events</h3>
        <div id="create-events-result"></div>
        <button onclick="createTestEvents()" class="success">Create Test Events</button>
    </div>
    
    <div class="section">
        <h3>2. Test Simple Event Deletion (No Dependencies)</h3>
        <div id="simple-deletion-result"></div>
        <button onclick="testSimpleDeletion()" class="danger">Test Simple Deletion</button>
    </div>
    
    <div class="section">
        <h3>3. Create Event with Dependencies</h3>
        <div id="create-dependencies-result"></div>
        <button onclick="createEventWithDependencies()" class="success">Create Event with Dependencies</button>
    </div>
    
    <div class="section">
        <h3>4. Test Cascade Deletion (With Dependencies)</h3>
        <div id="cascade-deletion-result"></div>
        <button onclick="testCascadeDeletion()" class="danger">Test Cascade Deletion</button>
    </div>
    
    <div class="section">
        <h3>5. Current Events Status</h3>
        <div id="events-status"></div>
        <button onclick="loadEventsStatus()">Refresh Events Status</button>
    </div>

    <script>
        let testEventIds = [];
        
        async function createTestEvents() {
            const resultDiv = document.getElementById('create-events-result');
            resultDiv.innerHTML = '<p>Creating test events...</p>';
            
            try {
                // Get CSRF token
                const tokenResponse = await fetch('../includes/get_csrf_token.php');
                const tokenData = await tokenResponse.json();
                
                const events = [
                    {
                        name: 'TEST SIMPLE DELETE ' + Date.now(),
                        description: 'Event for simple deletion testing',
                        start_date: '2025-01-01',
                        end_date: '2025-01-02',
                        location: 'Test Location',
                        status: 'upcoming'
                    },
                    {
                        name: 'TEST CASCADE DELETE ' + Date.now(),
                        description: 'Event for cascade deletion testing',
                        start_date: '2025-01-03',
                        end_date: '2025-01-04',
                        location: 'Test Location 2',
                        status: 'upcoming'
                    }
                ];
                
                let html = '<div class="success">✅ Test events created:</div>';
                
                for (const event of events) {
                    const formData = new FormData();
                    formData.append('entity', 'event');
                    formData.append('action', 'create');
                    formData.append('name', event.name);
                    formData.append('description', event.description);
                    formData.append('start_date', event.start_date);
                    formData.append('end_date', event.end_date);
                    formData.append('location', event.location);
                    formData.append('status', event.status);
                    formData.append('csrf_token', tokenData.token);
                    
                    const response = await fetch('ajax/modal-handler.php', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        testEventIds.push(data.id);
                        html += `<p>• ${event.name} (ID: ${data.id})</p>`;
                    } else {
                        html += `<div class="error">❌ Failed to create ${event.name}: ${data.message}</div>`;
                    }
                }
                
                resultDiv.innerHTML = html;
                loadEventsStatus();
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testSimpleDeletion() {
            const resultDiv = document.getElementById('simple-deletion-result');
            resultDiv.innerHTML = '<p>Testing simple event deletion...</p>';
            
            if (testEventIds.length === 0) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please create test events first</div>';
                return;
            }
            
            try {
                const eventId = testEventIds[0];
                
                // Check dependencies first
                const checkResponse = await fetch('ajax/check-event-dependencies.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `event_id=${eventId}`
                });
                
                const checkData = await checkResponse.json();
                
                if (checkData.success) {
                    resultDiv.innerHTML += `<div class="info">📊 Dependencies check: ${checkData.dependencies.can_delete ? 'Can delete safely' : 'Has dependencies'}</div>`;
                    
                    // Get CSRF token
                    const tokenResponse = await fetch('../includes/get_csrf_token.php');
                    const tokenData = await tokenResponse.json();
                    
                    // Delete the event
                    const formData = new FormData();
                    formData.append('entity', 'event');
                    formData.append('action', 'delete');
                    formData.append('id', eventId);
                    formData.append('csrf_token', tokenData.token);
                    formData.append('force_cascade', 'true');
                    
                    const response = await fetch('ajax/modal-handler.php', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML += `<div class="success">✅ Event deleted successfully: ${data.message}</div>`;
                        testEventIds.splice(0, 1); // Remove from array
                        loadEventsStatus();
                    } else {
                        resultDiv.innerHTML += `<div class="error">❌ Deletion failed: ${data.message}</div>`;
                    }
                } else {
                    resultDiv.innerHTML += `<div class="error">❌ Dependencies check failed: ${checkData.message}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function createEventWithDependencies() {
            const resultDiv = document.getElementById('create-dependencies-result');
            resultDiv.innerHTML = '<p>Creating event with dependencies...</p>';
            
            if (testEventIds.length === 0) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please create test events first</div>';
                return;
            }
            
            try {
                const eventId = testEventIds[testEventIds.length - 1]; // Use last created event
                
                // Get CSRF token
                const tokenResponse = await fetch('../includes/get_csrf_token.php');
                const tokenData = await tokenResponse.json();
                
                // Add a sport to the event to create dependencies
                const formData = new FormData();
                formData.append('action', 'add_sport');
                formData.append('event_id', eventId);
                formData.append('sport_id', '1'); // Basketball
                formData.append('tournament_format_id', '1');
                formData.append('seeding_method', 'random');
                formData.append('csrf_token', tokenData.token);
                
                const response = await fetch('ajax/event-management.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">✅ Dependencies created: Added sport to event ${eventId}</div>`;
                    loadEventsStatus();
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to create dependencies: ${data.message}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testCascadeDeletion() {
            const resultDiv = document.getElementById('cascade-deletion-result');
            resultDiv.innerHTML = '<p>Testing cascade deletion...</p>';
            
            if (testEventIds.length === 0) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please create test events first</div>';
                return;
            }
            
            try {
                const eventId = testEventIds[testEventIds.length - 1]; // Use last event
                
                // Check dependencies first
                const checkResponse = await fetch('ajax/check-event-dependencies.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `event_id=${eventId}`
                });
                
                const checkData = await checkResponse.json();
                
                if (checkData.success) {
                    let html = '<div class="info">📊 Dependencies found:</div><ul>';
                    html += `<li>Sports: ${checkData.dependencies.event_sports_count}</li>`;
                    html += `<li>Registrations: ${checkData.dependencies.registrations_count}</li>`;
                    html += `<li>Tournaments: ${checkData.dependencies.tournaments_count}</li>`;
                    html += `<li>Matches: ${checkData.dependencies.matches_count}</li>`;
                    html += '</ul>';
                    resultDiv.innerHTML = html;
                    
                    // Get CSRF token
                    const tokenResponse = await fetch('../includes/get_csrf_token.php');
                    const tokenData = await tokenResponse.json();
                    
                    // Delete with cascade
                    const formData = new FormData();
                    formData.append('entity', 'event');
                    formData.append('action', 'delete');
                    formData.append('id', eventId);
                    formData.append('csrf_token', tokenData.token);
                    formData.append('force_cascade', 'true');
                    
                    const response = await fetch('ajax/modal-handler.php', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        resultDiv.innerHTML += `<div class="success">✅ Cascade deletion successful: ${data.message}</div>`;
                        testEventIds.pop(); // Remove from array
                        loadEventsStatus();
                    } else {
                        resultDiv.innerHTML += `<div class="error">❌ Cascade deletion failed: ${data.message}</div>`;
                    }
                } else {
                    resultDiv.innerHTML += `<div class="error">❌ Dependencies check failed: ${checkData.message}</div>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML += `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function loadEventsStatus() {
            const resultDiv = document.getElementById('events-status');
            resultDiv.innerHTML = '<p>Loading events status...</p>';
            
            try {
                const response = await fetch('', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: 'action=get_events_status'
                });
                
                const text = await response.text();
                const data = JSON.parse(text);
                
                if (data.success) {
                    let html = '<h4>Current Events:</h4>';
                    if (data.events && data.events.length > 0) {
                        html += '<table><tr><th>ID</th><th>Name</th><th>Sports</th><th>Registrations</th><th>Tournaments</th><th>Matches</th><th>Can Delete</th></tr>';
                        data.events.forEach(event => {
                            html += `<tr>
                                <td>${event.id}</td>
                                <td>${event.name}</td>
                                <td>${event.event_sports_count}</td>
                                <td>${event.registrations_count}</td>
                                <td>${event.tournaments_count}</td>
                                <td>${event.matches_count}</td>
                                <td>${event.can_delete ? '✅' : '❌'}</td>
                            </tr>`;
                        });
                        html += '</table>';
                    } else {
                        html += '<p>No events found.</p>';
                    }
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="error">Error: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        // Load initial status
        document.addEventListener('DOMContentLoaded', function() {
            loadEventsStatus();
        });
    </script>
</body>
</html>

<?php
// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        switch ($_POST['action']) {
            case 'get_events_status':
                $sql = "
                    SELECT 
                        e.id,
                        e.name,
                        e.status,
                        (SELECT COUNT(*) FROM event_sports es WHERE es.event_id = e.id) as event_sports_count,
                        (SELECT COUNT(*) FROM registrations r 
                         JOIN event_sports es ON r.event_sport_id = es.id 
                         WHERE es.event_id = e.id) as registrations_count,
                        (SELECT COUNT(*) FROM tournaments t 
                         JOIN event_sports es ON t.event_sport_id = es.id 
                         WHERE es.event_id = e.id) as tournaments_count,
                        (SELECT COUNT(*) FROM matches m 
                         JOIN event_sports es ON m.event_sport_id = es.id 
                         WHERE es.event_id = e.id) as matches_count
                    FROM events e
                    ORDER BY e.id DESC
                    LIMIT 10
                ";
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // Determine if each event can be deleted
                foreach ($events as &$event) {
                    $event['can_delete'] = ($event['event_sports_count'] == 0 && 
                                          $event['registrations_count'] == 0 && 
                                          $event['tournaments_count'] == 0 && 
                                          $event['matches_count'] == 0);
                }
                
                echo json_encode([
                    'success' => true,
                    'events' => $events
                ]);
                break;
                
            default:
                echo json_encode([
                    'success' => false,
                    'message' => 'Invalid action'
                ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}
?>
