<?php
/**
 * Department-Centric Registration System Functions
 * Handles unified department registration and multi-sport participation
 */

/**
 * Register a department for an entire event (unified registration)
 * Automatically creates participations for ALL sports in the event
 */
function registerDepartmentForEvent($conn, $event_id, $department_id, $data = []) {
    try {
        $conn->beginTransaction();

        // Check if department already registered for this event
        $stmt = $conn->prepare("SELECT id FROM event_department_registrations WHERE event_id = ? AND department_id = ?");
        $stmt->execute([$event_id, $department_id]);
        if ($stmt->fetch()) {
            throw new Exception('Department already registered for this event');
        }

        // Create unified department registration
        $stmt = $conn->prepare("
            INSERT INTO event_department_registrations
            (event_id, department_id, status, contact_person, contact_email, contact_phone, notes, total_participants)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event_id,
            $department_id,
            $data['status'] ?? 'pending',
            $data['contact_person'] ?? null,
            $data['contact_email'] ?? null,
            $data['contact_phone'] ?? null,
            $data['notes'] ?? null,
            $data['total_participants'] ?? 0
        ]);

        $registration_id = $conn->lastInsertId();

        // AUTOMATICALLY ADD DEPARTMENT TO ALL EXISTING SPORTS IN THE EVENT
        $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ?");
        $stmt->execute([$event_id]);
        $event_sports = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($event_sports as $event_sport) {
            // Create automatic sport participation for each sport in the event
            $stmt = $conn->prepare("
                INSERT INTO department_sport_participations
                (event_department_registration_id, event_sport_id, team_name, participants, status, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $registration_id,
                $event_sport['id'],
                null, // Team name can be set later
                json_encode([]), // Empty participants initially
                $data['status'] ?? 'pending',
                'Auto-created from unified event registration'
            ]);
        }

        $conn->commit();
        return ['success' => true, 'registration_id' => $registration_id, 'sports_added' => count($event_sports)];

    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Add sport participation for a registered department
 */
function addDepartmentSportParticipation($conn, $event_department_registration_id, $event_sport_id, $data = []) {
    try {
        $conn->beginTransaction();
        
        // Check if department already participating in this sport
        $stmt = $conn->prepare("SELECT id FROM department_sport_participations WHERE event_department_registration_id = ? AND event_sport_id = ?");
        $stmt->execute([$event_department_registration_id, $event_sport_id]);
        if ($stmt->fetch()) {
            throw new Exception('Department already participating in this sport');
        }
        
        // Process participants list
        $participants = [];
        if (!empty($data['participants'])) {
            if (is_array($data['participants'])) {
                $participants = $data['participants'];
            } else {
                $lines = explode("\n", trim($data['participants']));
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line)) {
                        $participants[] = $line;
                    }
                }
            }
        }
        
        // Create sport participation
        $stmt = $conn->prepare("
            INSERT INTO department_sport_participations 
            (event_department_registration_id, event_sport_id, team_name, participants, status, notes) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event_department_registration_id,
            $event_sport_id,
            $data['team_name'] ?? null,
            json_encode($participants),
            $data['status'] ?? 'registered',
            $data['notes'] ?? null
        ]);
        
        $participation_id = $conn->lastInsertId();
        
        // Update total participants count in main registration
        $participant_count = count($participants);
        $stmt = $conn->prepare("
            UPDATE event_department_registrations 
            SET total_participants = total_participants + ? 
            WHERE id = ?
        ");
        $stmt->execute([$participant_count, $event_department_registration_id]);
        
        // Create corresponding entry in old registrations table for backward compatibility
        $stmt = $conn->prepare("
            INSERT INTO registrations 
            (event_sport_id, department_id, team_name, participants, status, department_sport_participation_id) 
            VALUES (?, (SELECT department_id FROM event_department_registrations WHERE id = ?), ?, ?, ?, ?)
        ");
        $stmt->execute([
            $event_sport_id,
            $event_department_registration_id,
            $data['team_name'] ?? null,
            json_encode($participants),
            $data['status'] ?? 'registered',
            $participation_id
        ]);
        
        $conn->commit();
        return ['success' => true, 'participation_id' => $participation_id];
        
    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Get department registration for an event
 */
function getDepartmentEventRegistration($conn, $event_id, $department_id) {
    $stmt = $conn->prepare("
        SELECT edr.*, d.name as department_name, d.abbreviation, d.color_code,
               e.name as event_name
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        JOIN events e ON edr.event_id = e.id
        WHERE edr.event_id = ? AND edr.department_id = ?
    ");
    $stmt->execute([$event_id, $department_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Get all department registrations for an event
 */
function getEventDepartmentRegistrations($conn, $event_id) {
    $stmt = $conn->prepare("
        SELECT edr.*, d.name as department_name, d.abbreviation, d.color_code,
               COUNT(dsp.id) as sports_count
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        LEFT JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        WHERE edr.event_id = ?
        GROUP BY edr.id
        ORDER BY edr.registration_date DESC
    ");
    $stmt->execute([$event_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get department sport participations for an event registration
 */
function getDepartmentSportParticipations($conn, $event_department_registration_id) {
    $stmt = $conn->prepare("
        SELECT dsp.*, es.id as event_sport_id, s.name as sport_name, s.type as sport_type,
               es.venue, es.status as sport_status
        FROM department_sport_participations dsp
        JOIN event_sports es ON dsp.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        WHERE dsp.event_department_registration_id = ?
        ORDER BY dsp.participation_date DESC
    ");
    $stmt->execute([$event_department_registration_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Check if department is registered for an event
 */
function isDepartmentRegisteredForEvent($conn, $event_id, $department_id) {
    $stmt = $conn->prepare("SELECT id FROM event_department_registrations WHERE event_id = ? AND department_id = ?");
    $stmt->execute([$event_id, $department_id]);
    return $stmt->fetch() !== false;
}

/**
 * Check if department is participating in a specific sport
 */
function isDepartmentParticipatingInSport($conn, $event_id, $department_id, $event_sport_id) {
    $stmt = $conn->prepare("
        SELECT dsp.id 
        FROM department_sport_participations dsp
        JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
        WHERE edr.event_id = ? AND edr.department_id = ? AND dsp.event_sport_id = ?
    ");
    $stmt->execute([$event_id, $department_id, $event_sport_id]);
    return $stmt->fetch() !== false;
}

/**
 * Automatically add all registered departments to a new sport when it's added to an event
 */
function addAllRegisteredDepartmentsToSport($conn, $event_sport_id) {
    try {
        $conn->beginTransaction();

        // Get event ID from event_sport_id
        $stmt = $conn->prepare("SELECT event_id FROM event_sports WHERE id = ?");
        $stmt->execute([$event_sport_id]);
        $event_sport = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$event_sport) {
            throw new Exception('Event sport not found');
        }

        $event_id = $event_sport['event_id'];

        // Get all departments registered for this event
        $stmt = $conn->prepare("SELECT id, status FROM event_department_registrations WHERE event_id = ?");
        $stmt->execute([$event_id]);
        $registrations = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $added_count = 0;

        foreach ($registrations as $registration) {
            // Check if department already participating in this sport
            $stmt = $conn->prepare("
                SELECT id FROM department_sport_participations
                WHERE event_department_registration_id = ? AND event_sport_id = ?
            ");
            $stmt->execute([$registration['id'], $event_sport_id]);

            if (!$stmt->fetch()) {
                // Add department to this sport
                $stmt = $conn->prepare("
                    INSERT INTO department_sport_participations
                    (event_department_registration_id, event_sport_id, team_name, participants, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $registration['id'],
                    $event_sport_id,
                    null, // Team name can be set later
                    json_encode([]), // Empty participants initially
                    $registration['status'], // Use same status as event registration
                    'Auto-added when sport was added to event'
                ]);
                $added_count++;
            }
        }

        $conn->commit();
        return ['success' => true, 'departments_added' => $added_count];

    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Get available sports for department participation
 */
function getAvailableSportsForDepartment($conn, $event_id, $department_id) {
    $stmt = $conn->prepare("
        SELECT es.*, s.name as sport_name, s.type as sport_type,
               CASE WHEN dsp.id IS NOT NULL THEN 1 ELSE 0 END as is_participating
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN department_sport_participations dsp ON es.id = dsp.event_sport_id
            AND dsp.event_department_registration_id = (
                SELECT id FROM event_department_registrations
                WHERE event_id = ? AND department_id = ?
            )
        WHERE es.event_id = ?
        ORDER BY s.name
    ");
    $stmt->execute([$event_id, $department_id, $event_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Update department registration status
 */
function updateDepartmentRegistrationStatus($conn, $registration_id, $status) {
    try {
        $stmt = $conn->prepare("UPDATE event_department_registrations SET status = ? WHERE id = ?");
        $stmt->execute([$status, $registration_id]);
        return ['success' => true, 'message' => 'Registration status updated successfully'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update status: ' . $e->getMessage()];
    }
}

/**
 * Remove department sport participation
 */
function removeDepartmentSportParticipation($conn, $participation_id) {
    try {
        $conn->beginTransaction();
        
        // Get participation details for participant count adjustment
        $stmt = $conn->prepare("
            SELECT dsp.event_department_registration_id, dsp.participants
            FROM department_sport_participations dsp
            WHERE dsp.id = ?
        ");
        $stmt->execute([$participation_id]);
        $participation = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$participation) {
            throw new Exception('Sport participation not found');
        }
        
        // Count participants to subtract
        $participants = json_decode($participation['participants'], true) ?: [];
        $participant_count = count($participants);
        
        // Remove from old registrations table
        $stmt = $conn->prepare("DELETE FROM registrations WHERE department_sport_participation_id = ?");
        $stmt->execute([$participation_id]);
        
        // Remove sport participation
        $stmt = $conn->prepare("DELETE FROM department_sport_participations WHERE id = ?");
        $stmt->execute([$participation_id]);
        
        // Update total participants count
        $stmt = $conn->prepare("
            UPDATE event_department_registrations 
            SET total_participants = GREATEST(0, total_participants - ?) 
            WHERE id = ?
        ");
        $stmt->execute([$participant_count, $participation['event_department_registration_id']]);
        
        $conn->commit();
        return ['success' => true, 'message' => 'Sport participation removed successfully'];
        
    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => 'Failed to remove participation: ' . $e->getMessage()];
    }
}

/**
 * Get department registration statistics for an event
 */
function getDepartmentRegistrationStats($conn, $event_id) {
    $stmt = $conn->prepare("
        SELECT
            COUNT(DISTINCT edr.id) as total_departments,
            COUNT(DISTINCT dsp.id) as total_sport_participations,
            SUM(edr.total_participants) as total_participants,
            COUNT(DISTINCT CASE WHEN edr.status = 'approved' THEN edr.id END) as approved_departments,
            COUNT(DISTINCT CASE WHEN edr.status = 'pending' THEN edr.id END) as pending_departments
        FROM event_department_registrations edr
        LEFT JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        WHERE edr.event_id = ?
    ");
    $stmt->execute([$event_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Record department score for a specific sport
 */
function recordDepartmentSportScore($conn, $event_id, $department_id, $sport_id, $position, $additional_data = []) {
    try {
        // Get department registration ID
        $stmt = $conn->prepare("SELECT id FROM event_department_registrations WHERE event_id = ? AND department_id = ?");
        $stmt->execute([$event_id, $department_id]);
        $registration = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$registration) {
            throw new Exception('Department not registered for this event');
        }

        // Get scoring system for the event
        $stmt = $conn->prepare("
            SELECT ss.position_points, ss.participation_bonus, ss.winner_bonus
            FROM events e
            JOIN scoring_systems ss ON e.scoring_system_id = ss.id
            WHERE e.id = ?
        ");
        $stmt->execute([$event_id]);
        $scoring = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$scoring) {
            throw new Exception('No scoring system found for this event');
        }

        // Calculate points based on position
        $position_points_config = json_decode($scoring['position_points'], true);
        $points_earned = $position_points_config[$position] ?? $position_points_config['participation'] ?? 0;

        // Add bonuses
        $bonus_points = $scoring['participation_bonus'];
        if ($position == 1) {
            $bonus_points += $scoring['winner_bonus'];
        }

        // Insert or update score
        $stmt = $conn->prepare("
            INSERT INTO department_overall_scores
            (event_department_registration_id, sport_id, position, points_earned, bonus_points, penalty_points, performance_data)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            position = VALUES(position),
            points_earned = VALUES(points_earned),
            bonus_points = VALUES(bonus_points),
            penalty_points = VALUES(penalty_points),
            performance_data = VALUES(performance_data)
        ");
        $stmt->execute([
            $registration['id'],
            $sport_id,
            $position,
            $points_earned,
            $bonus_points,
            $additional_data['penalty_points'] ?? 0,
            json_encode($additional_data['performance_data'] ?? [])
        ]);

        // Recalculate overall standings
        calculateOverallStandings($conn, $event_id);

        return ['success' => true, 'points_earned' => $points_earned + $bonus_points];

    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Calculate overall standings for an event
 */
function calculateOverallStandings($conn, $event_id) {
    try {
        $conn->beginTransaction();

        // Clear existing standings
        $stmt = $conn->prepare("DELETE FROM event_overall_standings WHERE event_id = ?");
        $stmt->execute([$event_id]);

        // Calculate new standings
        $stmt = $conn->prepare("
            SELECT
                edr.department_id,
                SUM(dos.final_score) as total_points,
                COUNT(dos.id) as sports_participated,
                COUNT(CASE WHEN dos.position = 1 THEN 1 END) as sports_won,
                COUNT(CASE WHEN dos.position <= 3 THEN 1 END) as sports_podium,
                AVG(dos.position) as average_position
            FROM event_department_registrations edr
            LEFT JOIN department_overall_scores dos ON edr.id = dos.event_department_registration_id
            WHERE edr.event_id = ? AND edr.status = 'approved'
            GROUP BY edr.department_id
            HAVING sports_participated > 0
            ORDER BY total_points DESC, sports_won DESC, average_position ASC
        ");
        $stmt->execute([$event_id]);
        $standings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Insert standings with ranks
        $rank = 1;
        foreach ($standings as $standing) {
            $stmt = $conn->prepare("
                INSERT INTO event_overall_standings
                (event_id, department_id, total_points, sports_participated, sports_won, sports_podium, average_position, overall_rank, is_overall_winner)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $event_id,
                $standing['department_id'],
                $standing['total_points'],
                $standing['sports_participated'],
                $standing['sports_won'],
                $standing['sports_podium'],
                $standing['average_position'],
                $rank,
                $rank == 1 ? 1 : 0
            ]);
            $rank++;
        }

        // Update event with overall winner
        if (!empty($standings)) {
            $winner_dept_id = $standings[0]['department_id'];
            $stmt = $conn->prepare("UPDATE events SET overall_winner_department_id = ?, overall_winner_announced_at = NOW() WHERE id = ?");
            $stmt->execute([$winner_dept_id, $event_id]);
        }

        $conn->commit();
        return ['success' => true, 'standings_count' => count($standings)];

    } catch (Exception $e) {
        $conn->rollBack();
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

/**
 * Get overall standings for an event
 */
function getOverallStandings($conn, $event_id) {
    $stmt = $conn->prepare("
        SELECT eos.*, d.name as department_name, d.abbreviation, d.color_code
        FROM event_overall_standings eos
        JOIN departments d ON eos.department_id = d.id
        WHERE eos.event_id = ?
        ORDER BY eos.overall_rank ASC
    ");
    $stmt->execute([$event_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get department scores breakdown for an event
 */
function getDepartmentScoresBreakdown($conn, $event_id, $department_id) {
    $stmt = $conn->prepare("
        SELECT dos.*, s.name as sport_name, s.type as sport_type
        FROM department_overall_scores dos
        JOIN event_department_registrations edr ON dos.event_department_registration_id = edr.id
        JOIN sports s ON dos.sport_id = s.id
        WHERE edr.event_id = ? AND edr.department_id = ?
        ORDER BY dos.final_score DESC
    ");
    $stmt->execute([$event_id, $department_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
