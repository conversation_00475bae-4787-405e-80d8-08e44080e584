<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== PHP Error Debug Test ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Current Time: " . date('Y-m-d H:i:s') . "\n\n";

// Test if we can include the required files
echo "Testing file inclusions...\n";

try {
    echo "1. Testing auth.php... ";
    require_once 'auth.php';
    echo "OK\n";
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

try {
    echo "2. Testing database.php... ";
    require_once '../config/database.php';
    echo "OK\n";
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

try {
    echo "3. Testing functions.php... ";
    require_once '../includes/functions.php';
    echo "OK\n";
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\nTesting admin authentication...\n";
try {
    requireAdmin();
    echo "Admin auth: OK\n";
} catch (Exception $e) {
    echo "Admin auth ERROR: " . $e->getMessage() . "\n";
}

echo "\nTesting database connection...\n";
try {
    $database = new Database();
    $conn = $database->getConnection();
    echo "Database connection: OK\n";
} catch (Exception $e) {
    echo "Database ERROR: " . $e->getMessage() . "\n";
}

echo "\nTesting event data retrieval...\n";
try {
    $event_id = $_GET['event_id'] ?? 1;
    $event = getEventById($conn, $event_id);
    if ($event) {
        echo "Event data: OK (Event: " . $event['name'] . ")\n";
    } else {
        echo "Event data: No event found with ID $event_id\n";
    }
} catch (Exception $e) {
    echo "Event data ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== All PHP tests completed ===\n";
?>

<!DOCTYPE html>
<html>
<head>
    <title>PHP Error Debug</title>
</head>
<body>
    <h1>PHP Error Debug Results</h1>
    <p>Check the output above for any PHP errors that might be preventing JavaScript from loading.</p>
    
    <h2>JavaScript Test</h2>
    <button onclick="alert('JavaScript is working')">Test JavaScript</button>
    
    <script>
        console.log('JavaScript loaded successfully');
        
        // Test if we can define a simple function
        function testFunction() {
            console.log('Test function works');
            return true;
        }
        
        window.testFunction = testFunction;
        
        console.log('Test function defined:', typeof window.testFunction);
    </script>
</body>
</html>
