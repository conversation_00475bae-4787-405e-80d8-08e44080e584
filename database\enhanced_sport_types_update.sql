-- Enhanced Sport Types System for SC_IMS
-- Comprehensive sport categorization with clear visual indicators and tournament integration
-- Date: 2025-07-05

-- =====================================================
-- 1. UPDATE SPORT_TYPES TABLE WITH COMPREHENSIVE CATEGORIES
-- =====================================================

-- First, ensure the sport_types table exists with proper structure
CREATE TABLE IF NOT EXISTS sport_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    category ENUM('traditional', 'academic', 'judged', 'performance') NOT NULL,
    color_code VARCHAR(7) DEFAULT '#007bff',
    icon_class VARCHAR(50) DEFAULT 'fas fa-trophy',
    tournament_formats TEXT, -- JSON array of compatible tournament formats
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clear existing data and insert comprehensive sport types
DELETE FROM sport_types;

INSERT INTO sport_types (name, description, category, color_code, icon_class, tournament_formats) VALUES
-- Traditional Sports (Team and Individual competitive sports)
('Traditional Team Sports', 'Team-based competitive sports like basketball, volleyball, football where teams compete directly against each other', 'traditional', '#28a745', 'fas fa-users', '["single_elimination", "double_elimination", "round_robin", "multi_stage"]'),

('Traditional Individual Sports', 'Individual competitive sports like track and field, swimming, tennis where individuals compete against each other', 'traditional', '#17a2b8', 'fas fa-user', '["single_elimination", "double_elimination", "round_robin", "best_performance"]'),

-- Academic Competitions
('Academic Games', 'Knowledge-based competitions including chess, quiz bowls, debate, math competitions, and other intellectual contests', 'academic', '#6f42c1', 'fas fa-brain', '["swiss_system", "round_robin", "knockout_academic", "elimination_rounds"]'),

-- Judged/Performance Sports
('Judged Competitions', 'Performance-based competitions evaluated by judges including dancing, singing, talent shows, pageants, and artistic performances', 'judged', '#fd7e14', 'fas fa-star', '["judged_rounds", "talent_showcase", "performance_scoring", "elimination_rounds"]'),

('Performance Arts', 'Creative and artistic performances including drama, poetry, art competitions, and cultural showcases', 'performance', '#e83e8c', 'fas fa-palette', '["judged_rounds", "showcase_format", "portfolio_review", "performance_scoring"]');

-- =====================================================
-- 2. UPDATE SPORTS TABLE TO USE ENHANCED SPORT TYPES
-- =====================================================

-- Add sport_type_id column if it doesn't exist
ALTER TABLE sports 
ADD COLUMN IF NOT EXISTS sport_type_id INT,
ADD COLUMN IF NOT EXISTS display_order INT DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Add foreign key constraint if it doesn't exist
SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE 
                         WHERE TABLE_NAME = 'sports' AND COLUMN_NAME = 'sport_type_id' 
                         AND CONSTRAINT_NAME LIKE 'FK_%');

SET @sql = IF(@constraint_exists = 0, 
    'ALTER TABLE sports ADD FOREIGN KEY (sport_type_id) REFERENCES sport_types(id) ON DELETE SET NULL',
    'SELECT "Foreign key already exists"');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. UPDATE EXISTING SPORTS WITH PROPER CATEGORIZATION
-- =====================================================

-- Update existing sports to use the new sport type system
UPDATE sports SET sport_type_id = (
    CASE 
        WHEN type = 'traditional' AND name IN ('Basketball', 'Volleyball', 'Football', 'Soccer') 
            THEN (SELECT id FROM sport_types WHERE name = 'Traditional Team Sports')
        WHEN type = 'traditional' AND name NOT IN ('Basketball', 'Volleyball', 'Football', 'Soccer')
            THEN (SELECT id FROM sport_types WHERE name = 'Traditional Individual Sports')
        WHEN type = 'academic' OR name IN ('Chess', 'Quiz Bowl', 'Debate', 'Math Competition')
            THEN (SELECT id FROM sport_types WHERE name = 'Academic Games')
        WHEN type = 'judged' AND name IN ('Singing Contest', 'Dance Competition', 'Talent Show', 'Pageant')
            THEN (SELECT id FROM sport_types WHERE name = 'Judged Competitions')
        WHEN type = 'judged' AND name IN ('Drama', 'Poetry', 'Art Competition', 'Cultural Show')
            THEN (SELECT id FROM sport_types WHERE name = 'Performance Arts')
        ELSE (SELECT id FROM sport_types WHERE name = 'Traditional Individual Sports')
    END
) WHERE sport_type_id IS NULL;

-- =====================================================
-- 4. ADD SAMPLE SPORTS FOR EACH CATEGORY
-- =====================================================

-- Insert sample sports if they don't exist
INSERT IGNORE INTO sports (name, description, type, scoring_method, bracket_format, sport_type_id, display_order, is_active) VALUES
-- Traditional Team Sports
('Basketball', 'Fast-paced team sport played on a court with hoops', 'traditional', 'point_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Traditional Team Sports'), 1, TRUE),
('Volleyball', 'Team sport played with a net, teams hit ball over net', 'traditional', 'point_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Traditional Team Sports'), 2, TRUE),
('Football', 'Team sport played on a field with goals', 'traditional', 'point_based', 'round_robin',
 (SELECT id FROM sport_types WHERE name = 'Traditional Team Sports'), 3, TRUE),

-- Traditional Individual Sports
('Track and Field', 'Individual athletic competitions including running, jumping, throwing', 'traditional', 'time_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Traditional Individual Sports'), 4, TRUE),
('Swimming', 'Individual water-based racing competitions', 'traditional', 'time_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Traditional Individual Sports'), 5, TRUE),
('Tennis', 'Individual racket sport played on a court', 'traditional', 'point_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Traditional Individual Sports'), 6, TRUE),

-- Academic Games
('Chess', 'Strategic board game requiring tactical thinking', 'academic', 'point_based', 'round_robin',
 (SELECT id FROM sport_types WHERE name = 'Academic Games'), 7, TRUE),
('Quiz Bowl', 'Knowledge-based competition covering various academic subjects', 'academic', 'point_based', 'round_robin',
 (SELECT id FROM sport_types WHERE name = 'Academic Games'), 8, TRUE),
('Debate', 'Formal argumentation competition on assigned topics', 'academic', 'criteria_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Academic Games'), 9, TRUE),

-- Judged Competitions
('Dance Competition', 'Choreographed performances judged on technique and artistry', 'judged', 'criteria_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Judged Competitions'), 10, TRUE),
('Singing Contest', 'Vocal performances judged on technique, tone, and presentation', 'judged', 'criteria_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Judged Competitions'), 11, TRUE),
('Talent Show', 'Variety performances showcasing diverse talents and skills', 'judged', 'criteria_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Judged Competitions'), 12, TRUE),

-- Performance Arts
('Drama Competition', 'Theatrical performances judged on acting and presentation', 'judged', 'criteria_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Performance Arts'), 13, TRUE),
('Poetry Recitation', 'Spoken word performances emphasizing delivery and interpretation', 'judged', 'criteria_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Performance Arts'), 14, TRUE),
('Art Exhibition', 'Visual arts competition showcasing creative works', 'judged', 'criteria_based', 'single_elimination',
 (SELECT id FROM sport_types WHERE name = 'Performance Arts'), 15, TRUE);

-- =====================================================
-- 5. CREATE VIEW FOR ENHANCED SPORTS DISPLAY
-- =====================================================

CREATE OR REPLACE VIEW sports_with_types AS
SELECT
    s.id,
    s.name,
    s.description,
    s.type as legacy_type,
    s.scoring_method,
    s.rules,
    s.max_participants,
    s.bracket_format,
    s.display_order,
    s.is_active,
    s.created_at,
    st.id as sport_type_id,
    st.name as sport_type_name,
    st.description as sport_type_description,
    st.category as sport_type_category,
    st.color_code,
    st.icon_class,
    st.tournament_formats
FROM sports s
LEFT JOIN sport_types st ON s.sport_type_id = st.id
ORDER BY s.display_order ASC, s.name ASC;

-- =====================================================
-- 6. SETUP COMPLETE
-- =====================================================

-- Enhanced sport types system setup complete
-- The system now supports:
-- - Traditional Team Sports (Basketball, Volleyball, Football)
-- - Traditional Individual Sports (Track & Field, Swimming, Tennis)
-- - Academic Games (Chess, Quiz Bowl, Debate)
-- - Judged Competitions (Dance, Singing, Talent Shows)
-- - Performance Arts (Drama, Poetry, Art Exhibitions)

COMMIT;
