-- Department-Centric Registration System Schema
-- This schema supports unified department registration with multi-sport participation

-- 1. Event Department Registrations (NEW TABLE)
-- Single registration per department per event
CREATE TABLE IF NOT EXISTS event_department_registrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    department_id INT NOT NULL,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'approved', 'rejected', 'withdrawn') DEFAULT 'pending',
    contact_person VARCHAR(255),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(20),
    notes TEXT,
    total_participants INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_department (event_id, department_id)
);

-- 2. Department Sport Participations (NEW TABLE)
-- Tracks which sports each registered department participates in
CREATE TABLE IF NOT EXISTS department_sport_participations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_department_registration_id INT NOT NULL,
    event_sport_id INT NOT NULL,
    team_name VARCHAR(255), -- Optional custom team name for this sport
    participants JSON, -- Sport-specific participant list
    participation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('registered', 'confirmed', 'withdrawn') DEFAULT 'registered',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_department_registration_id) REFERENCES event_department_registrations(id) ON DELETE CASCADE,
    FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
    UNIQUE KEY unique_dept_sport_participation (event_department_registration_id, event_sport_id)
);

-- 3. Department Overall Scores (NEW TABLE)
-- Tracks department performance across all sports for overall winner calculation
CREATE TABLE IF NOT EXISTS department_overall_scores (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_department_registration_id INT NOT NULL,
    sport_id INT NOT NULL,
    position INT, -- Final position in this sport (1st, 2nd, 3rd, etc.)
    points_earned DECIMAL(10,2) DEFAULT 0, -- Points based on position/performance
    bonus_points DECIMAL(10,2) DEFAULT 0, -- Additional bonus points
    penalty_points DECIMAL(10,2) DEFAULT 0, -- Penalty deductions
    final_score DECIMAL(10,2) GENERATED ALWAYS AS (points_earned + bonus_points - penalty_points) STORED,
    performance_data JSON, -- Additional performance metrics
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_department_registration_id) REFERENCES event_department_registrations(id) ON DELETE CASCADE,
    FOREIGN KEY (sport_id) REFERENCES sports(id) ON DELETE CASCADE,
    UNIQUE KEY unique_dept_sport_score (event_department_registration_id, sport_id)
);

-- 4. Overall Winner Calculations (NEW TABLE)
-- Stores calculated overall standings and winner information
CREATE TABLE IF NOT EXISTS event_overall_standings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    department_id INT NOT NULL,
    total_points DECIMAL(10,2) DEFAULT 0,
    sports_participated INT DEFAULT 0,
    sports_won INT DEFAULT 0, -- Number of sports where department placed 1st
    sports_podium INT DEFAULT 0, -- Number of sports where department placed 1st-3rd
    average_position DECIMAL(5,2), -- Average position across all sports
    overall_rank INT,
    is_overall_winner BOOLEAN DEFAULT FALSE,
    calculation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_dept_standing (event_id, department_id)
);

-- 5. Scoring System Configuration (NEW TABLE)
-- Configurable point system for overall winner calculation
CREATE TABLE IF NOT EXISTS scoring_systems (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_id INT NOT NULL,
    name VARCHAR(255) NOT NULL DEFAULT 'Default Scoring',
    description TEXT,
    position_points JSON, -- Points awarded for each position: {"1": 10, "2": 8, "3": 6, ...}
    participation_bonus DECIMAL(5,2) DEFAULT 0, -- Bonus points just for participating
    winner_bonus DECIMAL(5,2) DEFAULT 0, -- Extra bonus for 1st place
    min_sports_required INT DEFAULT 1, -- Minimum sports to be eligible for overall winner
    tie_breaker_method ENUM('total_wins', 'average_position', 'head_to_head', 'manual') DEFAULT 'total_wins',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
);

-- 6. Update existing registrations table to link with new system
-- Add foreign key to link old registrations with new department sport participations
ALTER TABLE registrations 
ADD COLUMN department_sport_participation_id INT NULL,
ADD FOREIGN KEY (department_sport_participation_id) REFERENCES department_sport_participations(id) ON DELETE SET NULL;

-- 7. Add overall winner tracking to events table
ALTER TABLE events 
ADD COLUMN overall_winner_department_id INT NULL,
ADD COLUMN overall_winner_announced_at TIMESTAMP NULL,
ADD COLUMN scoring_system_id INT NULL,
ADD FOREIGN KEY (overall_winner_department_id) REFERENCES departments(id) ON DELETE SET NULL,
ADD FOREIGN KEY (scoring_system_id) REFERENCES scoring_systems(id) ON DELETE SET NULL;

-- 8. Create indexes for performance
CREATE INDEX idx_event_dept_reg_event ON event_department_registrations(event_id);
CREATE INDEX idx_event_dept_reg_dept ON event_department_registrations(department_id);
CREATE INDEX idx_dept_sport_part_reg ON department_sport_participations(event_department_registration_id);
CREATE INDEX idx_dept_sport_part_sport ON department_sport_participations(event_sport_id);
CREATE INDEX idx_dept_overall_scores_reg ON department_overall_scores(event_department_registration_id);
CREATE INDEX idx_dept_overall_scores_sport ON department_overall_scores(sport_id);
CREATE INDEX idx_overall_standings_event ON event_overall_standings(event_id);
CREATE INDEX idx_overall_standings_dept ON event_overall_standings(department_id);
CREATE INDEX idx_overall_standings_rank ON event_overall_standings(overall_rank);

-- 9. Insert default scoring system for existing events
INSERT INTO scoring_systems (event_id, name, description, position_points, participation_bonus, winner_bonus)
SELECT 
    id as event_id,
    'Default Scoring System' as name,
    'Standard point-based scoring: 1st=10pts, 2nd=8pts, 3rd=6pts, 4th=4pts, 5th=2pts, participation=1pt' as description,
    '{"1": 10, "2": 8, "3": 6, "4": 4, "5": 2, "participation": 1}' as position_points,
    1.0 as participation_bonus,
    2.0 as winner_bonus
FROM events 
WHERE id NOT IN (SELECT DISTINCT event_id FROM scoring_systems WHERE event_id IS NOT NULL);

-- 10. Update events table to link with default scoring systems
UPDATE events e 
SET scoring_system_id = (
    SELECT id FROM scoring_systems s 
    WHERE s.event_id = e.id 
    AND s.name = 'Default Scoring System' 
    LIMIT 1
) 
WHERE scoring_system_id IS NULL;
