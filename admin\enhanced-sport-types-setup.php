<?php
/**
 * Enhanced Sport Types Setup for SC_IMS
 * Run this once to update the database with comprehensive sport type system
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

if ($_POST['action'] ?? '' === 'run_setup') {
    try {
        // Read and execute the SQL file
        $sqlFile = '../database/enhanced_sport_types_update.sql';
        
        if (!file_exists($sqlFile)) {
            throw new Exception('SQL file not found: ' . $sqlFile);
        }
        
        $sql = file_get_contents($sqlFile);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $conn->beginTransaction();
        
        $executedCount = 0;
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                $conn->exec($statement);
                $executedCount++;
            }
        }
        
        $conn->commit();
        
        $message = "Enhanced sport types setup completed successfully! Executed {$executedCount} SQL statements.";
        
        // Log the setup
        if (function_exists('logAdminActivity')) {
            logAdminActivity('ENHANCED_SPORT_TYPES_SETUP', 'system', 0, 'Enhanced sport types system installed');
        }
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        $error = 'Setup failed: ' . $e->getMessage();
    }
}

// Check current status
$statusChecks = [];
try {
    // Check if sport_types table exists and has data
    $stmt = $conn->query("SELECT COUNT(*) as count FROM sport_types");
    $sportTypesCount = $stmt->fetch()['count'] ?? 0;
    $statusChecks['sport_types'] = $sportTypesCount > 0;
    
    // Check if sports table has sport_type_id column
    $stmt = $conn->query("SHOW COLUMNS FROM sports LIKE 'sport_type_id'");
    $statusChecks['sports_updated'] = $stmt->rowCount() > 0;
    
    // Check if view exists
    $stmt = $conn->query("SHOW TABLES LIKE 'sports_with_types'");
    $statusChecks['view_created'] = $stmt->rowCount() > 0;
    
} catch (Exception $e) {
    $statusChecks['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Sport Types Setup - SC_IMS Admin</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <div class="main-content">
            <div class="content-header">
                <h1><i class="fas fa-cogs"></i> Enhanced Sport Types Setup</h1>
                <p>Set up comprehensive sport type categorization system</p>
            </div>

            <div class="content-body">
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- Current Status -->
                <div class="card">
                    <div class="card-header">
                        <h3>Current System Status</h3>
                    </div>
                    <div class="card-body">
                        <div class="status-grid">
                            <div class="status-item">
                                <i class="fas fa-database"></i>
                                <span>Sport Types Table</span>
                                <span class="status-badge <?php echo ($statusChecks['sport_types'] ?? false) ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo ($statusChecks['sport_types'] ?? false) ? 'Ready' : 'Not Set Up'; ?>
                                </span>
                            </div>
                            <div class="status-item">
                                <i class="fas fa-table"></i>
                                <span>Sports Table Updated</span>
                                <span class="status-badge <?php echo ($statusChecks['sports_updated'] ?? false) ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo ($statusChecks['sports_updated'] ?? false) ? 'Updated' : 'Needs Update'; ?>
                                </span>
                            </div>
                            <div class="status-item">
                                <i class="fas fa-eye"></i>
                                <span>Enhanced View</span>
                                <span class="status-badge <?php echo ($statusChecks['view_created'] ?? false) ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo ($statusChecks['view_created'] ?? false) ? 'Created' : 'Not Created'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Setup Information -->
                <div class="card">
                    <div class="card-header">
                        <h3>What This Setup Will Do</h3>
                    </div>
                    <div class="card-body">
                        <div class="setup-info">
                            <div class="info-section">
                                <h4><i class="fas fa-plus-circle"></i> Enhanced Sport Categories</h4>
                                <ul>
                                    <li><strong>Traditional Team Sports</strong> - Basketball, Volleyball, Football, etc.</li>
                                    <li><strong>Traditional Individual Sports</strong> - Track & Field, Swimming, Tennis, etc.</li>
                                    <li><strong>Academic Games</strong> - Chess, Quiz Bowl, Debate, Math Competitions</li>
                                    <li><strong>Judged Competitions</strong> - Dancing, Singing, Talent Shows, Pageants</li>
                                    <li><strong>Performance Arts</strong> - Drama, Poetry, Art Exhibitions, Cultural Shows</li>
                                </ul>
                            </div>
                            <div class="info-section">
                                <h4><i class="fas fa-palette"></i> Visual Improvements</h4>
                                <ul>
                                    <li>Color-coded sport type badges</li>
                                    <li>Category-specific icons</li>
                                    <li>Clear visual indicators in listings</li>
                                    <li>Improved admin interface</li>
                                </ul>
                            </div>
                            <div class="info-section">
                                <h4><i class="fas fa-trophy"></i> Tournament Integration</h4>
                                <ul>
                                    <li>Sport-specific tournament format restrictions</li>
                                    <li>Appropriate scoring systems for each type</li>
                                    <li>Enhanced bracket generation</li>
                                    <li>Judged competition support</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Setup Action -->
                <div class="card">
                    <div class="card-header">
                        <h3>Run Setup</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="run_setup">
                            <p>Click the button below to set up the enhanced sport types system. This will:</p>
                            <ul>
                                <li>Create comprehensive sport type categories</li>
                                <li>Update existing sports with proper categorization</li>
                                <li>Add sample sports for each category</li>
                                <li>Create enhanced database views</li>
                                <li>Update tournament format compatibility</li>
                            </ul>
                            <div style="margin-top: 20px;">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-play"></i>
                                    Run Enhanced Sport Types Setup
                                </button>
                                <a href="sports.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i>
                                    Back to Sports Management
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .status-item i {
            color: #007bff;
            font-size: 1.2em;
        }
        
        .setup-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .info-section h4 {
            color: #495057;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-section ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .info-section li {
            margin-bottom: 5px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</body>
</html>
