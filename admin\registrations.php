<?php
/**
 * Registration Management for SC_IMS Admin Panel
 * Sports Competition and Event Management System
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$event_sport_id = $_GET['event_sport_id'] ?? null;
if (!$event_sport_id) {
    header('Location: events.php');
    exit;
}

$event_sport = getEventSportById($conn, $event_sport_id);
if (!$event_sport) {
    header('Location: events.php');
    exit;
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        switch ($action) {
            case 'register_department':
                $result = registerDepartment($conn, $event_sport_id, $_POST);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'update_status':
                $result = updateRegistrationStatus($conn, $_POST['registration_id'], $_POST['status']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
            case 'delete_registration':
                $result = deleteRegistration($conn, $_POST['registration_id']);
                if ($result['success']) {
                    $message = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
        }
    }
}

// Get registrations
$registrations = getRegistrations($conn, $event_sport_id);

// Get available departments
$available_departments = getAvailableDepartments($conn, $event_sport_id);

function registerDepartment($conn, $event_sport_id, $data) {
    try {
        // Check if department already registered
        $sql = "SELECT id FROM registrations WHERE event_sport_id = ? AND department_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$event_sport_id, $data['department_id']]);
        if ($stmt->fetch()) {
            return ['success' => false, 'message' => 'Department already registered for this sport.'];
        }

        $participants = [];
        if (!empty($data['participants'])) {
            $participant_lines = explode("\n", trim($data['participants']));
            foreach ($participant_lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $participants[] = $line;
                }
            }
        }

        $sql = "INSERT INTO registrations (event_sport_id, department_id, participants, status) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $event_sport_id,
            $data['department_id'],
            json_encode($participants),
            $data['status'] ?? 'pending'
        ]);
        
        logAdminActivity('REGISTER_DEPARTMENT', 'registrations', $conn->lastInsertId());
        return ['success' => true, 'message' => 'Department registered successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to register department: ' . $e->getMessage()];
    }
}

function updateRegistrationStatus($conn, $registration_id, $status) {
    try {
        $sql = "UPDATE registrations SET status = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$status, $registration_id]);
        
        logAdminActivity('UPDATE_REGISTRATION_STATUS', 'registrations', $registration_id);
        return ['success' => true, 'message' => 'Registration status updated successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to update status: ' . $e->getMessage()];
    }
}

function deleteRegistration($conn, $registration_id) {
    try {
        $sql = "DELETE FROM registrations WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$registration_id]);
        
        logAdminActivity('DELETE_REGISTRATION', 'registrations', $registration_id);
        return ['success' => true, 'message' => 'Registration deleted successfully!'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Failed to delete registration: ' . $e->getMessage()];
    }
}

function getAvailableDepartments($conn, $event_sport_id) {
    $sql = "SELECT d.* FROM departments d 
            WHERE d.id NOT IN (
                SELECT department_id FROM registrations WHERE event_sport_id = ?
            )
            ORDER BY d.name";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$event_sport_id]);
    return $stmt->fetchAll();
}

// Current admin user already retrieved above
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrations - <?php echo htmlspecialchars($event_sport['sport_name']); ?> - <?php echo APP_NAME; ?></title>

    <?php include 'includes/admin-styles.php'; ?>
    <style>
        .event-sport-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .registration-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .registration-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .registration-body {
            padding: 20px;
        }
        
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        
        .participants-list {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .participants-list h5 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .participants-list ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .department-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .department-color {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #ddd;
        }
        
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-user-plus"></i>
                        <span>Registration Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username']); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Registration Management</h1>
                <p class="page-description">Manage team registrations for <?php echo htmlspecialchars($event_sport['sport_name']); ?></p>
            </div>

            <div class="admin-content">
                <div class="event-sport-header">
                    <h2><?php echo htmlspecialchars($event_sport['sport_name']); ?></h2>
                    <p><strong>Event:</strong> <?php echo htmlspecialchars($event_sport['event_name']); ?></p>
                    <p><strong>Bracket Type:</strong> <?php echo ucfirst(str_replace('_', ' ', $event_sport['bracket_type'])); ?></p>
                    <?php if ($event_sport['max_teams']): ?>
                        <p><strong>Max Teams:</strong> <?php echo $event_sport['max_teams']; ?></p>
                    <?php endif; ?>
                </div>

                <?php if ($error): ?>
                    <div class="alert alert-error"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-success"><?php echo $message; ?></div>
                <?php endif; ?>

                <!-- Register Department Form -->
                <?php if (!empty($available_departments)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3>Register New Department</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="register_department">

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="department_id">Department</label>
                                    <select id="department_id" name="department_id" class="form-control" required>
                                        <option value="">Select a department</option>
                                        <?php foreach ($available_departments as $dept): ?>
                                            <option value="<?php echo $dept['id']; ?>"><?php echo htmlspecialchars($dept['name']); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select id="status" name="status" class="form-control">
                                        <option value="pending">Pending</option>
                                        <option value="approved">Approved</option>
                                        <option value="rejected">Rejected</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="participants">Participants (one per line)</label>
                                <textarea id="participants" name="participants" class="form-control" rows="5" 
                                          placeholder="John Doe&#10;Jane Smith&#10;Mike Johnson"></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">Register Department</button>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Registrations List -->
                <div class="card">
                    <div class="card-header">
                        <h3>Registered Departments</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($registrations)): ?>
                            <?php foreach ($registrations as $registration): ?>
                                <div class="registration-card">
                                    <div class="registration-header">
                                        <div class="department-info">
                                            <div class="department-color" style="background-color: <?php echo $registration['color_code']; ?>"></div>
                                            <div>
                                                <h4><?php echo htmlspecialchars($registration['department_name']); ?></h4>
                                                <p><?php echo htmlspecialchars($registration['abbreviation']); ?></p>
                                            </div>
                                        </div>
                                        <div>
                                            <span class="status-badge status-<?php echo $registration['status']; ?>">
                                                <?php echo ucfirst($registration['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="registration-body">
                                        <p><strong>Registered:</strong> <?php echo formatDateTime($registration['created_at']); ?></p>
                                        
                                        <?php if ($registration['participants']): ?>
                                            <?php $participants = json_decode($registration['participants'], true); ?>
                                            <?php if (!empty($participants)): ?>
                                                <div class="participants-list">
                                                    <h5>Participants (<?php echo count($participants); ?>)</h5>
                                                    <ul>
                                                        <?php foreach ($participants as $participant): ?>
                                                            <li><?php echo htmlspecialchars($participant); ?></li>
                                                        <?php endforeach; ?>
                                                    </ul>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <div class="quick-actions">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="action" value="update_status">
                                                <input type="hidden" name="registration_id" value="<?php echo $registration['id']; ?>">
                                                <select name="status" onchange="this.form.submit()" class="form-control" style="width: auto; display: inline-block;">
                                                    <option value="pending" <?php echo $registration['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                                    <option value="approved" <?php echo $registration['status'] === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                                    <option value="rejected" <?php echo $registration['status'] === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                                </select>
                                            </form>
                                            
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this registration?');">
                                                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                <input type="hidden" name="action" value="delete_registration">
                                                <input type="hidden" name="registration_id" value="<?php echo $registration['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-center" style="color: #666; font-style: italic; padding: 40px;">No departments registered yet.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
</body>
</html>
