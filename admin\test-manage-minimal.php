<?php
/**
 * Minimal test version of manage-event.php
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? 1;

$event = getEventById($conn, $event_id);
if (!$event) {
    echo "Event not found!";
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Manage Event - <?php echo APP_NAME; ?></title>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .tab-button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .tab-button.active {
            background: #0056b3;
        }
        .tab-content {
            display: none;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .tab-content.active {
            display: block;
        }
        .test-section {
            background: #e9ecef;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Manage Event: <?php echo htmlspecialchars($event['name']); ?></h1>
        
        <div class="test-section">
            <h3>Event Information</h3>
            <p><strong>ID:</strong> <?php echo $event_id; ?></p>
            <p><strong>Name:</strong> <?php echo htmlspecialchars($event['name']); ?></p>
            <p><strong>Status:</strong> <?php echo $event['status']; ?></p>
            <p><strong>Admin:</strong> <?php echo htmlspecialchars($current_admin['username']); ?></p>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-buttons">
            <button class="tab-button active" onclick="switchTab('standings')">Standings</button>
            <button class="tab-button" onclick="switchTab('sports')">Sports</button>
            <button class="tab-button" onclick="switchTab('registrations')">Registrations</button>
            <button class="tab-button" onclick="switchTab('matches')">Matches</button>
        </div>

        <!-- Tab Contents -->
        <div id="standings-tab" class="tab-content active">
            <h3>Standings Tab</h3>
            <p>This is the standings content. Tab navigation is working!</p>
            <button onclick="alert('Standings button works!')">Test Button</button>
        </div>

        <div id="sports-tab" class="tab-content">
            <h3>Sports Management Tab</h3>
            <p>This is the sports management content.</p>
            <button onclick="alert('Sports button works!')">Add Sport</button>
        </div>

        <div id="registrations-tab" class="tab-content">
            <h3>Registrations Tab</h3>
            <p>This is the registrations content.</p>
            <button onclick="alert('Registration button works!')">Register Department</button>
        </div>

        <div id="matches-tab" class="tab-content">
            <h3>Recent Matches Tab</h3>
            <p>This is the recent matches content.</p>
            <button onclick="alert('Matches button works!')">View Matches</button>
        </div>
    </div>

    <script>
        // Simple tab switching function
        function switchTab(tabName) {
            console.log('switchTab called:', tabName);
            
            // Hide all tabs
            var tabs = document.querySelectorAll('.tab-content');
            for (var i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }
            
            // Remove active class from all buttons
            var buttons = document.querySelectorAll('.tab-button');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i].classList.remove('active');
            }
            
            // Show selected tab
            var targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('Showing tab:', tabName);
            }
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }
        
        console.log('JavaScript loaded successfully!');
        console.log('Tab switching function defined');
    </script>
</body>
</html>
